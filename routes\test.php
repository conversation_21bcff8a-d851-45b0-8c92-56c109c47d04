<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BudgetPlanController;

// Test route to verify LP optimization integration
Route::get('/test-lp-optimization', function () {
    // Create a test request with sample data
    $testData = [
        'activity' => [
            'Capdev/Training - gender sensitivity trainings (GST)',
            'Capdev/Training - gender analysis'
        ],
        'mooe' => 50000,
        'ps' => 30000,
        'co' => 20000
    ];
    
    $request = new \Illuminate\Http\Request();
    $request->merge($testData);
    
    // Create controller instance and call the private method using reflection
    $controller = new BudgetPlanController();
    $reflection = new \ReflectionClass($controller);
    $method = $reflection->getMethod('runLPOptimization');
    $method->setAccessible(true);
    
    try {
        $result = $method->invoke($controller, $request);
        
        return response()->json([
            'success' => true,
            'message' => 'LP optimization test completed',
            'test_data' => $testData,
            'lp_result' => $result
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'LP optimization test failed',
            'error' => $e->getMessage(),
            'test_data' => $testData
        ]);
    }
});
