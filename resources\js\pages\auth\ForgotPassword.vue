<template>
    <Head title="Forgot Password" />

    <!-- Main Content -->
    <div class="min-h-screen bg-emerald-100 relative overflow-hidden">
        <div class="flex items-center justify-center min-h-screen px-4 py-12 relative z-10">
            <div class="flex bg-white rounded-3xl shadow-2xl overflow-hidden max-w-4xl w-full relative z-20">
                <!-- Left Side - Image/Illustration -->
                <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-emerald-400 to-emerald-600 p-12 items-center justify-center relative">
                    <div class="text-center text-white">
                        <!-- Logo/Icon -->
                        <div class="mb-8">
                            <div class="w-48 h-48 bg-white rounded-full mx-auto flex items-center justify-center shadow-xl p-6">
                                <img
                                    src="/images/panabo-city-logo-1.png"
                                    alt="Panabo City Logo"
                                    class="w-full h-full object-contain"
                                />
                            </div>
                        </div>
                        <h2 class="text-3xl font-bold mb-4 whitespace-nowrap">Gender and Development</h2>
                        <p class="text-emerald-100 text-lg leading-relaxed whitespace-nowrap">
                            Planning and Budgetting Management System
                        </p>
                    </div>
                    <!-- Decorative elements -->
                    <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full"></div>
                    <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full"></div>
                </div>

                <!-- Right Side - Forgot Password Form -->
                <div class="w-full lg:w-1/2 p-8 lg:p-12">
                    <!-- Status Messages -->
                    <div v-if="status" class="mb-6 p-4 rounded-xl bg-emerald-50 border border-emerald-200 text-emerald-700 text-sm text-center">
                        {{ status }}
                    </div>

                    <div class="text-center mb-8">
                        <h1 class="text-3xl font-bold text-gray-800 mb-2">Forgot Password</h1>
                        <p class="text-gray-600">Enter your email address to reset your password</p>
                    </div>

                    <form @submit.prevent="submit" class="space-y-6">
                        <!-- Email Input -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <div class="relative">
                                <input
                                    id="email"
                                    type="email"
                                    class="w-full px-4 pr-10 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition duration-200 placeholder-gray-500 text-gray-800"
                                    v-model="form.email"
                                    required
                                    autofocus
                                    autocomplete="username"
                                    placeholder="Enter your email"
                                />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                    </svg>
                                </div>
                            </div>
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button
                                type="submit"
                                class="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-4 rounded-xl transition duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 flex items-center justify-center"
                                :disabled="form.processing"
                            >
                                <svg
                                    v-if="form.processing"
                                    class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>
                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                {{ form.processing ? 'Sending...' : 'Send Reset Link' }}
                            </button>
                        </div>

                        <!-- Back to Login Link -->
                        <div class="text-center text-sm text-gray-600 pt-4 border-t border-gray-200">
                            Remember your password?
                            <Link
                                :href="route('login')"
                                class="text-emerald-600 hover:text-emerald-700 font-medium ml-1 transition duration-200"
                            >
                                Log in here
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="absolute bottom-0 w-full py-4 text-center text-sm text-emerald-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                © {{ new Date().getFullYear() }} Panabo City Government - GAD Planning and Budgeting Management System
                <br />
                All Rights Reserved
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputError from '@/components/InputError.vue';

defineProps<{ status?: string }>();

const form = useForm({
    email: '',
});

const submit = () => {
    form.post(route('password.email'));
};
</script>

<style scoped>
/* Match Login.vue custom styles if any */
</style>
