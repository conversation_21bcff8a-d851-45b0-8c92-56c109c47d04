<template>

    <Head title="Admin Dashboard " />

    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex font-sans min-h-screen">
        <!-- Sidebar and Main Content -->
        <AppSidebar ref="sidebarRef" />

        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
          <AppSidebarHeader :sidebar-ref="sidebarRef" />
          <template v-if="adminActiveTab === 'dashboard'">
            <!-- Main Dashboard Content -->
            <div class="flex-1 overflow-y-auto">
              <!-- Refresh Indicator -->
              <div v-if="isRefreshing" class="bg-emerald-50 border-b border-emerald-200 px-4 py-2 flex items-center justify-center gap-2">
                <svg class="animate-spin h-4 w-4 text-emerald-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm font-medium text-emerald-700">Refreshing dashboard data...</span>
              </div>
              
              <!-- Stats Cards Grid -->
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 p-3 sm:p-4 lg:p-6">
                <!-- Budget Card -->
                <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="goToGeoAnalytics">
                  <!-- Hover overlay -->
                  <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <!-- Content -->
                  <div class="relative p-5">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div class="w-12 h-12 flex items-center justify-center rounded-xl bg-emerald-500 shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                            <line x1="8" y1="21" x2="16" y2="21"/>
                            <line x1="12" y1="17" x2="12" y2="21"/>
                          </svg>
                        </div>
                      </div>
                      <div class="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
                    </div>

                    <div class="space-y-2">
                      <h3 class="text-sm font-semibold text-emerald-700 uppercase tracking-wide">Geo Analytics</h3>
                      <p class="text-2xl font-bold text-emerald-800 truncate">Panabo City Map</p>
                      <div class="flex items-center gap-2 text-xs text-emerald-600">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 01-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 01.872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 012.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 012.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 01.872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 01-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 01-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 100-5.86 2.929 2.929 0 000 5.858z" clip-rule="evenodd"/>
                        </svg>
                        <span>Monitor barangay progress</span>
                      </div>
                    </div>

                    <!-- Action indicator -->
                    <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div class="w-6 h-6 rounded-full bg-emerald-500 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>



                <!-- Users Card -->
                <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="adminActiveTab = 'users'">
                  <!-- Hover overlay -->
                  <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <!-- Content -->
                  <div class="relative p-5">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div class="w-12 h-12 flex items-center justify-center rounded-xl bg-emerald-600 shadow-lg group-hover:shadow-emerald-600/25 transition-all duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                          </svg>
                        </div>
                      </div>
                      <div class="w-2 h-2 rounded-full bg-emerald-500 animate-pulse"></div>
                    </div>

                    <div class="space-y-3">
                      <h3 class="text-sm font-semibold text-emerald-800 uppercase tracking-wide">Register Users</h3>
                      <p class="text-2xl font-bold text-emerald-900">{{ barangayUsers.active + adminUsers.active }}</p>
                      <div class="space-y-2">
                        <div class="flex justify-between text-xs text-emerald-700">
                          <span>Registered Users</span>
                          <span class="font-semibold">{{ barangayUsers.active + adminUsers.active }}</span>
                        </div>
                        <div class="w-full h-2 bg-emerald-200 rounded-full overflow-hidden">
                          <div class="h-2 bg-emerald-500 rounded-full transition-all duration-500 ease-out" :style="{ width: ((barangayUsers.active + adminUsers.active)/totalPossibleUsers*100) + '%' }"></div>
                        </div>
                        <!-- Removed percentage completion display -->
                      </div>
                    </div>

                    <!-- Action indicator -->
                    <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div class="w-6 h-6 rounded-full bg-emerald-600 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>










                <!-- Approved Plans Card -->
                <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="adminActiveTab = 'approved-plans'">
                  <!-- Hover overlay -->
                  <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <!-- Content -->
                  <div class="relative p-5">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div class="w-12 h-12 flex items-center justify-center rounded-xl bg-emerald-700 shadow-lg group-hover:shadow-emerald-700/25 transition-all duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 12l2 2 4-4"/>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.61 1.98"/>
                          </svg>
                        </div>
                      </div>
                      <div class="w-2 h-2 rounded-full bg-emerald-600 animate-pulse"></div>
                    </div>

                    <div class="space-y-2">
                      <h3 class="text-sm font-semibold text-emerald-800 uppercase tracking-wide">Approved Plans</h3>
                      <p class="text-2xl font-bold text-emerald-900">{{ approvedPlansCount }}</p>
                      <div class="flex items-center gap-2 text-xs text-emerald-700">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>Budget plans in 2025</span>
                      </div>
                    </div>

                    <!-- Action indicator -->
                    <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div class="w-6 h-6 rounded-full bg-emerald-700 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Approved Reports Card -->
                <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="adminActiveTab = 'approved-reports'">
                  <!-- Hover overlay -->
                  <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <!-- Content -->
                  <div class="relative p-5">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div class="w-12 h-12 flex items-center justify-center rounded-xl bg-emerald-800 shadow-lg group-hover:shadow-emerald-800/25 transition-all duration-300">
                         <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                          </svg>
                        </div>
                      </div>
                      <div class="w-2 h-2 rounded-full bg-emerald-700 animate-pulse"></div>
                    </div>

                    <div class="space-y-2">
                      <h3 class="text-sm font-semibold text-emerald-900 uppercase tracking-wide">Approved Reports</h3>
                      <p class="text-2xl font-bold text-emerald-950">{{ approvedReportsCount }}</p>
                      <div class="flex items-center gap-2 text-xs text-emerald-800">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Accomplishment reports in 2025</span>
                      </div>
                    </div>

                    <!-- Action indicator -->
                    <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div class="w-6 h-6 rounded-full bg-emerald-800 flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Charts Section -->
              <div class="grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4 p-3 sm:p-4">
                <!-- Pie Chart -->
                <div id="pieChart" class="group relative bg-white border border-emerald-200 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <!-- Header Section -->
                  <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div>
                        <h3 class="text-sm font-bold text-white">User Status Overview</h3>
                        <p class="text-emerald-100 text-xs">Fiscal Year {{ new Date().getFullYear() }}</p>
                      </div>
                      <div class="flex gap-1">
                        <button
                          :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', pieChartType === 'budget' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                          @click="pieChartType = 'budget'"
                        >Budget Plan</button>
                        <button
                          :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', pieChartType === 'accomplishment' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                          @click="pieChartType = 'accomplishment'"
                        >Accomplishment</button>
                      </div>
                    </div>
                  </div>
                  <div class="p-3">
                    <div class="flex flex-col lg:flex-row gap-4 items-center">
                      <div class="flex-1 flex justify-center w-full">
                        <PieChart
                          :chart-data="pieChartData"
                          :chart-options="pieChartOptionsWithClick"
                          class="w-full max-w-xs sm:max-w-sm lg:max-w-md"
                        />
                      </div>

                      <!-- User List Modal for Pie Chart -->
                      <div v-if="showPieUserModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-4">
                        <div class="bg-white rounded-2xl shadow-2xl p-6 w-full max-w-2xl h-[80vh] mx-auto flex flex-col border border-emerald-200">
                          <!-- Modal Header -->
                          <div class="flex items-center justify-between flex-shrink-0 mb-4 pb-4 border-b border-emerald-100">
                            <div>
                              <h3 class="text-xl font-bold text-emerald-800">{{ pieUserModalTitle.toUpperCase() }} USERS</h3>
                              <p class="text-sm text-emerald-600">{{ pieUserModalUsers.length }} users found</p>
                            </div>
                            <button @click="showPieUserModal = false" class="text-emerald-600 hover:text-emerald-800 hover:bg-emerald-50 p-2 rounded-lg transition-colors">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                          <!-- Scrollable User List -->
                          <div class="flex-1 overflow-y-auto">
                            <ul class="space-y-3">
                              <li v-for="user in pieUserModalUsers" :key="user.id" class="bg-emerald-50/50 p-4 flex items-center gap-4 rounded-xl border border-emerald-100 hover:bg-emerald-50 transition-colors">
                                <img :src="user.avatar || 'https://randomuser.me/api/portraits/lego/5.jpg'" alt="avatar" class="w-12 h-12 rounded-full object-cover border-2 border-emerald-200 flex-shrink-0" />
                                <div class="flex flex-col justify-center min-w-0 flex-1">
                                  <div class="font-semibold text-emerald-800 leading-tight truncate">{{ user.user_name || user.name }}</div>
                                  <div class="text-sm text-emerald-600 leading-tight truncate">{{ user.user_email || user.email }}</div>
                                  <div class="inline-block mt-2 px-3 py-1 rounded-full bg-emerald-100 text-emerald-700 text-xs font-medium">{{ user.barangay_name || user.barangay || 'Unknown Barangay' }}</div>
                                  <div class="mt-2 text-xs text-gray-700">Title: <span class="font-medium">{{ user.title }}</span></div>
                                  <div class="text-xs text-gray-500">Date: {{ user.date }}</div>
                                </div>
                              </li>
                              <li v-if="pieUserModalUsers.length === 0" class="py-8 text-center text-emerald-400">
                                <div class="text-4xl mb-2">📊</div>
                                <p>No users in this status.</p>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <!-- Legend and Summary -->
                      <div class="flex flex-col gap-2 flex-1 w-full lg:w-auto">
                        <div class="flex flex-col gap-2">
                          <div v-for="status in pieChartType === 'budget' ? barangayStatusData : accomplishmentStatusData" :key="status.label" class="flex items-center gap-2 text-sm cursor-pointer hover:bg-emerald-100/50 p-1 rounded transition-colors" @click="openPieUserModal(status.label)">
                            <span class="w-3 h-3 rounded-full border border-emerald-200" :style="{ background: status.color }"></span>
                            <span class="font-medium text-emerald-700 flex-1">{{ status.label }}</span>
                            <span class="font-semibold text-emerald-600">{{ status.value }}</span>
                          </div>
                        </div>
                        <div class="mt-3 text-xs text-emerald-800 bg-emerald-50 rounded p-2 sm:p-3 font-medium">
                          <template v-if="pieChartType === 'budget'">
                            <span v-if="barangayStatusData[0]">{{ barangayStatusData[0].value }}</span> of 40 barangay users have <b>approved</b> plans.<br>
                            <span v-if="barangayStatusData[1]">{{ barangayStatusData[1].value }}</span> pending,
                            <span v-if="barangayStatusData[2]">{{ barangayStatusData[2].value }}</span> in revision,
                            <span v-if="barangayStatusData[3]">{{ barangayStatusData[3].value }}</span> not submitted.
                          </template>
                          <template v-else>
                            <span v-if="accomplishmentStatusData[0]">{{ accomplishmentStatusData[0].value }}</span> barangays have <b>accomplished</b> reports.<br>
                            <span v-if="accomplishmentStatusData[1]">{{ accomplishmentStatusData[1].value }}</span> in progress,
                            <span v-if="accomplishmentStatusData[2]">{{ accomplishmentStatusData[2].value }}</span> in revision,
                            <span v-if="accomplishmentStatusData[3]">{{ accomplishmentStatusData[3].value }}</span> not started.
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Bar Chart -->
                <div id="barChart" class="group relative bg-white border border-emerald-200 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <!-- Header Section -->
                  <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div>
                        <h3 class="text-sm font-bold text-white">Budget Utilization</h3>
                        <p class="text-emerald-100 text-xs">Annual Performance Overview</p>
                      </div>
                      <button class="bg-white/20 hover:bg-white/30 text-white px-2 py-1 rounded text-xs font-semibold transition-all duration-200 flex items-center gap-1 backdrop-blur-sm border border-white/20">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span class="hidden sm:inline">Export Summary</span>
                        <span class="sm:hidden">Export</span>
                      </button>
                    </div>
                  </div>

                  <!-- Chart Content -->
                  <div class="p-3">
                    <div class="w-full overflow-x-auto">
                      <BarChart
                        :chart-data="annualAccomplishmentData"
                        :chart-options="barChartOptions"
                        class="w-full min-w-[300px]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Recent Activity Section -->
              <div class="px-3 sm:px-4 lg:px-6 pb-6">
                <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-emerald-700">Recent Activity</h3>
                    <div class="flex items-center gap-2">
                      <button 
                        @click="refreshDashboardData" 
                        :disabled="isRefreshing"
                        class="text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <svg 
                          :class="['h-4 w-4', isRefreshing ? 'animate-spin' : '']" 
                          xmlns="http://www.w3.org/2000/svg" 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span class="hidden sm:inline">{{ isRefreshing ? 'Refreshing...' : 'Refresh' }}</span>
                      </button>
                      <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">View All</button>
                    </div>
                  </div>

                  <div class="space-y-3">
                    <div v-if="recentActivities.length === 0" class="text-center py-8 text-gray-500">
                      <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                      <p class="text-sm">No recent activity</p>
                      <p class="text-xs text-gray-400">Activity will appear here as users submit plans and reports</p>
                    </div>

                    <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center gap-3 p-3 bg-white rounded-lg transition-colors hover:bg-gray-50">
                      <div class="w-10 h-10 rounded-full flex items-center justify-center bg-emerald-100">
                        <svg class="w-6 h-6 text-emerald-700" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                          <path v-if="activity.type === 'budget'" d="M9 12l2 2 4-4"/>
                          <path v-if="activity.type === 'budget'" d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.61 1.98"/>
                          <path v-else d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-sm font-semibold truncate text-emerald-900">{{ activity.title }}</div>
                        <div class="text-xs truncate text-emerald-600">{{ activity.user }} • {{ activity.barangay }}</div>
                        <div class="text-xs text-emerald-500">{{ formatActivityDate(activity.date) }}</div>
                      </div>
                      <div class="text-xs px-3 py-1.5 rounded font-semibold whitespace-nowrap" :class="getActivityStatusClass(activity.status)">
                        {{ (activity.status || '').charAt(0).toUpperCase() + (activity.status || '').slice(1) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- // End Dashboard Layout -->
          </template>
          <template v-else>
            <component :is="activeTabComponent" />
          </template>
        </div>
      </div>





<!-- Add this near the end of your template, before the closing </template> tag -->
<AdminReportModalTable
  v-if="showAdminReportModal"
  :show="showAdminReportModal"
  :report="selectedReport"
  @close="showAdminReportModal = false"
/>
</AppLayout>

</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import AppSidebar from '@/components/AppSidebar.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import AdminBudgetPlan from './AdminBudgetPlan.vue';
import AdminAccomplishmentReport from './AdminAccomplishmentReport.vue';
import ApprovedPlans from './ApprovedPlans.vue';
import ApprovedReports from './ApprovedReports.vue';
import UserManagement from './UserManagement.vue';
import SystemNoticeManager from '../components/SystemNoticeManager.vue';
import GeoAnalyticsTab from './GeoAnalyticsTab.vue';
import PieChart from '@/components/ui/charts/PieChart.vue'
import BarChart from '@/components/ui/charts/BarChart.vue'
import axios from 'axios'
import AdminReportModalTable from '../components/adminreportmodalTable.vue';
import AppSidebarHeader from '@/components/AppSidebarHeader.vue';
import SettingsTab from '@/components/SettingsTab.vue';
interface DashboardUser {
  id: number
  name: string
  email: string
  barangay?: string
  avatar?: string
}

import { usePage } from '@inertiajs/vue3';
const page = usePage<{ auth: { user: { role: string, id: number, name: string, email: string, barangay?: string, avatar?: string } } }>();
const userRole = page.props.auth.user?.role;

const barangayUsers = ref<{ total: number; active: number; list: DashboardUser[] }>({ total: 0, active: 0, list: [] })
const adminUsers = ref<{ total: number; active: number; list: DashboardUser[] }>({ total: 0, active: 0, list: [] })

// Add missing variables that are referenced in template
const usersLoading = ref(false)

// Add missing pending reports variable
const pendingReports = ref<AccomplishmentReport[]>([])

// Add missing budget row interface and variable
interface BudgetRow {
  id: number
  name: string
  email: string
  barangay?: string
  title?: string
  date?: string
  status?: string
  [key: string]: any
}

// Recent Activity interface and state
interface RecentActivity {
  id: string
  type: 'budget' | 'report'
  title: string
  user: string
  barangay: string
  date: string
  status: string
}

const recentActivities = ref<RecentActivity[]>([])

// Add refresh state
const isRefreshing = ref(false)

// Add refresh function
async function refreshDashboardData() {
  if (isRefreshing.value) return; // Prevent multiple simultaneous refreshes
  
  isRefreshing.value = true;
  try {
    console.log('Refreshing dashboard data...');
    
    // Fetch all data in parallel for better performance
    await Promise.all([
      fetchPendingSubmissions(),
      fetchAccomplishmentReports(),
      fetchUsers()
    ]);
    
    // Update recent activities after all data is loaded
    updateRecentActivities();
    
    console.log('Dashboard data refreshed successfully');
  } catch (error) {
    console.error('Error refreshing dashboard data:', error);
  } finally {
    isRefreshing.value = false;
  }
}

// Add watcher for adminActiveTab to refresh when dashboard is clicked
watch(adminActiveTab, (newTab) => {
  if (newTab === 'dashboard') {
    console.log('Dashboard tab clicked, refreshing data...');
    refreshDashboardData();
  }
});

// Computed properties for real approved counts (including all data, not excluding mock)
const approvedPlansCount = computed(() => {
  const approvedPlans = barangayBudgetRows.value.filter(plan =>
    plan.status === 'Approved' || plan.status === 'approved'
  );
  console.log('Budget Plans Status Check:', {
    totalPlans: barangayBudgetRows.value.length,
    approvedCount: approvedPlans.length,
    allStatuses: barangayBudgetRows.value.map(p => p.status),
    approvedPlans: approvedPlans
  });
  return approvedPlans.length;
});

const approvedReportsCount = computed(() => {
  const approvedReports = accomplishmentReports.value.filter(report =>
    report.status === 'Approved' || report.status === 'approved'
  );
  console.log('Reports Status Check:', {
    totalReports: accomplishmentReports.value.length,
    approvedCount: approvedReports.length,
    allStatuses: accomplishmentReports.value.map(r => r.status),
    approvedReports: approvedReports
  });
  return approvedReports.length;
});

// Mock data for pending accomplishment reports
type AccomplishmentReport = {
  id: number;
  name: string;
  email: string;
  barangay: string;
  title: string;
  date: string;
  status?: string;
  remarks?: string;
  user_name?: string;
  barangay_name?: string;
  updated_at?: string;
  dateSubmitted?: string;
  created_at?: string;
  [key: string]: any;
};

const accomplishmentReports = ref<AccomplishmentReport[]>([]);




// Add these variables to your script setup
const showAdminReportModal = ref(false);
const selectedReport = ref(null);

// Sidebar reference
const sidebarRef = ref();

function openReportViewModal(report: any) {
  selectedReport.value = report;
  showAdminReportModal.value = true;
}



// Action handlers for Pending Reports
function viewReport(report: typeof pendingReports.value[0]) {
  alert('View report: ' + report.title + ' by ' + report.name)
}



onMounted(async () => {
  try {
    console.log('Fetching data...')

    // Fetch pending submissions
    await fetchPendingSubmissions();

    // Fetch accomplishment reports using the new function
    await fetchAccomplishmentReports();

    // Fetch real user data from API (excluding mock users)
    await fetchUsers();

    // Update recent activities after data is loaded
    updateRecentActivities();
  } catch (e) {
    console.error('Error fetching data:', e)
  }
})

// Budget modal state (unused for geo analytics navigation)
const showBudgetModal = ref(false)
const totalBudget = ref(Number(localStorage.getItem('gad_total_budget')) || 12000000)
const budgetInput = ref(totalBudget.value)
const budgetDateTime = ref('')
// Calculate total possible users dynamically based on actual barangays count
// This represents the expected number of barangay users (one per barangay) plus admin users
const totalPossibleUsers = computed(() => {
  // Use a fixed number for barangays (20) plus estimated admin users (5)
  return 20 + 5; // 20 barangays + 5 admin users = 25 total expected users
});
async function saveBudgetModal() {
  try {
    totalBudget.value = Number(budgetInput.value);
    localStorage.setItem('gad_total_budget', totalBudget.value.toString());
    localStorage.setItem('gad_budget_datetime', new Date().toISOString());

    // Sync budget to backend for LP optimization
    await axios.post('/api/budget-sync', {
      total_budget: totalBudget.value
    });

    showBudgetModal.value = false;
    alert('Budget has been set and synced to backend for LP optimization calculations.');
  } catch (error) {
    console.error('Error syncing budget to backend:', error);
    // Still save locally even if backend sync fails
    showBudgetModal.value = false;
    alert('Budget saved locally. Backend sync failed - LP optimization may use fallback values.');
  }
}

// Navigate to Geo Analytics tab (SPA)
function goToGeoAnalytics() {
  adminActiveTab.value = 'geo-analytics' as any
}

function formatCurrency(value: number) {
  return '₱' + Math.floor(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// --- Status label/color definitions ---
const budgetStatusLabels = [
  { label: 'Approved', color: '#10b981', match: ['approved', 'Approved'] },
  { label: 'Pending', color: '#f59e0b', match: ['pending', 'Pending'] },
  { label: 'Revision', color: '#1e40af', match: ['revision', 'Revision'] },
  { label: 'Not Submitted', color: '#6b7280', match: [] }, // calculated
];
const accomplishmentStatusLabels = [
  { label: 'Approved', color: '#10b981', match: ['approved', 'Approved'] },
  { label: 'Pending', color: '#f59e0b', match: ['pending', 'Pending'] },
  { label: 'Revision', color: '#1e40af', match: ['revision', 'Revision'] },
  { label: 'Not Submitted', color: '#6b7280', match: [] }, // calculated
];

// --- Reusable status counting function ---
function computeStatusData(items: any[], statusLabels: { label: string; color: string; match: string[] }[]): { label: string; value: number; color: string }[] {
  console.log('computeStatusData - items:', items);
  console.log('computeStatusData - statusLabels:', statusLabels);
  
  const counts = statusLabels.map(({ label, match }) => {
    if (label === 'Not Submitted') {
      // For "Not Submitted", count users who haven't submitted anything
      const submittedUserIds = items.map(item => item.user_id || item.id);
      const notSubmittedCount = allBarangayUsers.value.filter(user => 
        !submittedUserIds.includes(user.id) && 
        user.role !== 'admin' &&
        (user.barangay_name || user.barangay)
      ).length;
      console.log(`Not Submitted count: ${notSubmittedCount} (from ${allBarangayUsers.value.length} total users)`);
      return {
        label,
        value: notSubmittedCount,
        color: '',
      };
    }
    
    const count = match.length > 0 ? items.filter((item: any) => {
      const itemStatus = (item.status || '').toLowerCase();
      const matches = match.some((m: string) => itemStatus === m.toLowerCase());
      console.log(`Status check for ${label}: item.status="${item.status}" -> itemStatus="${itemStatus}" -> matches=${matches}`);
      return matches;
    }).length : 0;
    
    console.log(`Count for ${label}: ${count}`);
    return {
      label,
      value: count,
      color: '',
    };
  });
  
  const result = counts.map((c, i) => ({ ...c, color: statusLabels[i].color }));
  console.log('computeStatusData - final result:', result);
  return result;
}

// --- Chart data refs ---
const barangayStatusData = ref<{ label: string; value: number; color: string }[]>([]);
const accomplishmentStatusData = ref<{ label: string; value: number; color: string }[]>([]);
const barangayBudgetRows = ref<BudgetRow[]>([]);

// Add ref for all barangay users to handle "Not Submitted"
const allBarangayUsers = ref<any[]>([]);

// --- Pie chart user modal state ---
const showPieUserModal = ref(false);
const pieUserModalTitle = ref('');
const pieUserModalUsers = ref<any[]>([]);

// --- Watchers to update chart data after fetch ---
watch(barangayBudgetRows, () => {
  console.log('barangayBudgetRows changed, updating chart data...');
  barangayStatusData.value = computeStatusData(barangayBudgetRows.value, budgetStatusLabels);
}, { immediate: true });

watch(accomplishmentReports, () => {
  console.log('accomplishmentReports changed, updating chart data...');
  accomplishmentStatusData.value = computeStatusData(accomplishmentReports.value, accomplishmentStatusLabels);
}, { immediate: true });

// Add watcher for allBarangayUsers to update "Not Submitted" counts
watch(allBarangayUsers, () => {
  console.log('allBarangayUsers changed, updating chart data...');
  barangayStatusData.value = computeStatusData(barangayBudgetRows.value, budgetStatusLabels);
  accomplishmentStatusData.value = computeStatusData(accomplishmentReports.value, accomplishmentStatusLabels);
}, { immediate: true });

// --- Pie chart data computed property ---
const pieChartData = computed(() => {
  if (pieChartType.value === 'budget') {
    return {
      labels: barangayStatusData.value.map((s) => s.label),
      datasets: [
        {
          data: barangayStatusData.value.map((s) => s.value),
          backgroundColor: barangayStatusData.value.map((s) => s.color),
          borderColor: '#fff',
          borderWidth: 2,
        },
      ],
    }
  } else {
    return {
      labels: accomplishmentStatusData.value.map((s) => s.label),
      datasets: [
        {
          data: accomplishmentStatusData.value.map((s) => s.value),
          backgroundColor: accomplishmentStatusData.value.map((s) => s.color),
          borderColor: '#fff',
          borderWidth: 2,
        },
      ],
    }
  }
});

// --- Update modal user logic to use real data ---
function getBudgetPlanUserName(u: any): string {
  if (u.user_full_name) return String(u.user_full_name).toUpperCase();
  if (u.user_name) return String(u.user_name).toUpperCase();
  if (u.name) return String(u.name).toUpperCase();
  return 'UNKNOWN USER';
}

// Update admin filter to use role
function isLikelyAdminUser(u: any): boolean {
  // Filter out users with role 'admin'
  const isAdmin = u.user_role === 'admin' || u.role === 'admin';
  console.log(`isLikelyAdminUser check for ${u.user_name}: user_role="${u.user_role}", role="${u.role}" -> isAdmin=${isAdmin}`);
  return isAdmin;
}

// Function to get users who haven't submitted anything yet
function getUsersWhoHaventSubmitted(submittedUsers: any[], allUsers: any[]): any[] {
  const submittedUserIds = submittedUsers.map(u => u.user_id || u.id);
  return allUsers.filter(user => 
    !submittedUserIds.includes(user.id) && 
    user.role !== 'admin' &&
    (user.barangay_name || user.barangay)
  );
}

function openPieUserModal(statusLabel: string) {
  console.log('openPieUserModal called with statusLabel:', statusLabel);
  console.log('pieChartType:', pieChartType.value);
  console.log('barangayBudgetRows:', barangayBudgetRows.value);
  console.log('accomplishmentReports:', accomplishmentReports.value);
  
  showPieUserModal.value = true;
  pieUserModalTitle.value = statusLabel;

  if (pieChartType.value === 'budget') {
    if (statusLabel === 'Not Submitted') {
      // Show users who haven't submitted budget plans yet
      const notSubmittedUsers = getUsersWhoHaventSubmitted(barangayBudgetRows.value, allBarangayUsers.value);
      console.log('Not Submitted users for budget:', notSubmittedUsers);
      
      pieUserModalUsers.value = notSubmittedUsers
        .map((u: any) => ({
          ...u,
          user_name: u.user_name ? String(u.user_name).toUpperCase() : (u.name ? String(u.name).toUpperCase() : 'UNKNOWN USER'),
          user_email: u.user_email || u.email || 'No Email',
          barangay_name: u.barangay_name || u.barangay || 'Unknown Barangay',
          title: 'No Budget Plan Submitted',
          date: '-',
        }));
    } else {
      // Filter users by status
      const filteredUsers = barangayBudgetRows.value.filter((u: any) => {
        const statusMatch = (u.status || '').toLowerCase() === statusLabel.toLowerCase();
        const notAdmin = !isLikelyAdminUser(u);
        
        // Detailed logging for Pending and Revision
        if (statusLabel === 'Pending' || statusLabel === 'Revision') {
          console.log(`=== DETAILED FILTERING FOR ${statusLabel} ===`);
          console.log(`User: ${u.user_name}`);
          console.log(`Status: "${u.status}"`);
          console.log(`Looking for: "${statusLabel}"`);
          console.log(`Status match: ${statusMatch}`);
          console.log(`User role: ${u.user_role}`);
          console.log(`Not admin: ${notAdmin}`);
          console.log(`Final result: ${statusMatch && notAdmin}`);
          console.log('---');
        }
        
        console.log(`Filtering user ${u.user_name}: status="${u.status}" vs "${statusLabel}" -> ${statusMatch}, notAdmin=${notAdmin}`);
        
        // Temporarily disable admin filter for debugging
        if (statusLabel === 'Pending' || statusLabel === 'Revision') {
          console.log(`DEBUG: Including user ${u.user_name} with status "${u.status}" for ${statusLabel} (admin filter disabled)`);
          return statusMatch; // Temporarily remove admin filter
        }
        
        return statusMatch && notAdmin;
      });
      
      console.log(`Filtered users for ${statusLabel}:`, filteredUsers);
      
      pieUserModalUsers.value = filteredUsers
        .map((u: any) => ({
          ...u,
          user_name: getBudgetPlanUserName(u),
          user_email: u.user_email || u.email || 'No Email',
          barangay_name: u.barangay_name || u.barangay || 'Unknown Barangay',
          title: u.title_desc || '-',
          date: u.created_at ? new Date(u.created_at).toLocaleDateString() : '-',
        }));
    }
  } else {
    if (statusLabel === 'Not Submitted') {
      // Show users who haven't submitted accomplishment reports yet
      const notSubmittedUsers = getUsersWhoHaventSubmitted(accomplishmentReports.value, allBarangayUsers.value);
      console.log('Not Submitted users for accomplishment:', notSubmittedUsers);
      
      pieUserModalUsers.value = notSubmittedUsers
        .map((u: any) => ({
          ...u,
          user_name: u.user_name ? String(u.user_name).toUpperCase() : (u.name ? String(u.name).toUpperCase() : 'UNKNOWN USER'),
          user_email: u.user_email || u.email || 'No Email',
          barangay_name: u.barangay_name || u.barangay || 'Unknown Barangay',
          title: 'No Accomplishment Report Submitted',
          date: '-',
        }));
    } else {
      // Filter users by status
      const filteredUsers = accomplishmentReports.value.filter((u: any) => {
        const statusMatch = (u.status || '').toLowerCase() === statusLabel.toLowerCase();
        const notAdmin = !isLikelyAdminUser(u);
        console.log(`Filtering accomplishment user ${u.user_name}: status="${u.status}" vs "${statusLabel}" -> ${statusMatch}, notAdmin=${notAdmin}`);
        return statusMatch && notAdmin;
      });
      
      console.log(`Filtered accomplishment users for ${statusLabel}:`, filteredUsers);
      
      pieUserModalUsers.value = filteredUsers
        .map((u: any) => ({
          ...u,
          user_name: u.user_name ? String(u.user_name).toUpperCase() : (u.name ? String(u.name).toUpperCase() : 'UNKNOWN USER'),
          user_email: u.user_email || u.email || 'No Email',
          barangay_name: u.barangay_name || u.barangay || 'Unknown Barangay',
          title: u.title || '-',
          date: u.date || '-',
        }));
    }
  }
  
  console.log('Final pieUserModalUsers:', pieUserModalUsers.value);
}

// Pie chart switch state
const pieChartType = ref<'budget' | 'accomplishment'>('budget')


// Sample annual data (utilized budget and accomplishment report summary)
const annualAccomplishmentData = ref({
  labels: ['2019', '2020', '2021', '2022', '2023', '2024'],
  datasets: [
    {
      label: 'Total Utilized Budget',
      backgroundColor: '#34d399',
      borderRadius: 6,
      data: [1000000, 1200000, 1300000, 1250000, 1400000, 1350000],
    },
    {
      label: 'Accomplishment Reports',
      backgroundColor: '#34d399',
      borderRadius: 6,
      data: [30, 32, 35, 38, 39, 40],
    },
  ],
})


const barChartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    tooltip: {
      callbacks: {
        label: (ctx: any) => `${ctx.dataset.label}: ${ctx.parsed.y}`,
      },
    },
    // Remove datalabels for bar chart
    datalabels: {
      display: false,
    },
  },
  scales: {
    x: {
      grid: { display: false },
      ticks: {
        maxRotation: 45,
        minRotation: 0
      }
    },
    y: {
      grid: { color: '#e5e7eb' },
      beginAtZero: true,
      ticks: {
        callback: function(value: any) {
          return typeof value === 'number' ? value.toLocaleString() : value;
        }
      }
    },
  },
}))

const pieChartOptionsWithClick = computed(() => ({
  responsive: true,
  maintainAspectRatio: true,
  plugins: {
    legend: { display: false },
    tooltip: {
      callbacks: {
        label: (ctx: any) => `${ctx.label}: ${ctx.parsed}`,
      },
    },
    datalabels: {
      color: '#ffffff',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderColor: '#ffffff',
      borderWidth: 1,
      borderRadius: 3,
      padding: 2,
      font: {
        weight: 'bold',
        size: 10
      },
      formatter: (value: number, context: any) => {
        const data = context.chart.data.datasets[0].data;
        const total = data.reduce((a: number, b: number) => a + b, 0);
        const percent = total > 0 ? Math.round((value / total) * 100) : 0;
        return percent + '%';
      },
      display: true,
      anchor: 'center',
      align: 'center',
    },
  },
  onClick: (_evt: any, elements: any[]) => {
    if (elements && elements.length > 0) {
      const idx = elements[0].index
      const label = pieChartData.value.labels[idx]
      openPieUserModal(label)
    }
  },
}));




const breadcrumbs: BreadcrumbItem[] = [
    {
        title: ' ',
        href: '/dashboard',
    },
];

import { adminActiveTab } from '../dashboardTabState';

const activeTabComponent = computed(() => {
  if (adminActiveTab.value === 'budget') return AdminBudgetPlan;
  if (adminActiveTab.value === 'accomplishment') return AdminAccomplishmentReport;
  if (adminActiveTab.value === 'approved-plans') return ApprovedPlans;
  if (adminActiveTab.value === 'approved-reports') return ApprovedReports;
  if (adminActiveTab.value === 'users') return UserManagement;
  if (adminActiveTab.value === 'notices') return SystemNoticeManager;
  if (adminActiveTab.value === 'settings') return SettingsTab;
  if ((adminActiveTab.value as any) === 'geo-analytics') return GeoAnalyticsTab;
  return null;
});


// Fetch real users from API (excluding mock users)
async function fetchUsers() {
  usersLoading.value = true
  try {
    const response = await axios.get('/api/barangay-users?t=' + new Date().getTime())

    if (userRole === 'admin') {
      if (response.data.barangay && response.data.admin) {
        barangayUsers.value = response.data.barangay
        adminUsers.value = response.data.admin
        // Store all barangay users for "Not Submitted" logic
        allBarangayUsers.value = response.data.barangay.list || []
      }
    } else if (userRole === 'barangay') {
      barangayUsers.value = {
        total: 1,
        active: 1,
        list: [
          {
            id: page.props.auth.user.id,
            name: page.props.auth.user.name,
            email: page.props.auth.user.email,
            barangay: page.props.auth.user.barangay,
            avatar: page.props.auth.user.avatar
          }
        ]
      }
      adminUsers.value = { total: 0, active: 0, list: [] }
      allBarangayUsers.value = barangayUsers.value.list
    }
  } catch (error) {
    console.error('Error fetching users:', error)
  } finally {
    usersLoading.value = false
  }
}

// Enhanced fetchPendingSubmissions function with database joins
async function fetchPendingSubmissions() {
  try {
    console.log('Fetching pending submissions...');
    const response = await axios.get('/api/budget-plans');

    console.log('API Response:', response.data);

    if (response.data.budgetPlans) {
      // Log the raw data to see what status values exist
      console.log('Raw budget plans data:', response.data.budgetPlans);
      
      // Check what status values are in the data
      const statuses = [...new Set(response.data.budgetPlans.map((plan: any) => plan.status))];
      console.log('Unique status values in data:', statuses);
      
      // Log each plan with its status for debugging
      console.log('=== ALL BUDGET PLANS WITH STATUS ===');
      response.data.budgetPlans.forEach((plan: any, index: number) => {
        console.log(`${index + 1}. User: ${plan.user_name}, Status: "${plan.status}", Barangay: ${plan.barangay_name}`);
      });
      console.log('=== END BUDGET PLANS ===');
      
      // Map the response data but preserve all original fields
      barangayBudgetRows.value = response.data.budgetPlans.map((plan: any) => {
        // Create a copy of the original plan object
        const planData = { ...plan };

        // Add or override specific fields for display purposes
        return {
          ...planData,
          barangay_name: plan.barangay_name || 'Unknown Barangay',
          user_name: plan.user_name || 'Unknown User',
          user_email: plan.user_email || 'No Email',
          gender_issue: plan.gender_issue || '',
          title_desc: plan.title_desc || 'No Title',
          status: plan.status || 'Pending'
        };
      });
      console.log('Processed submissions:', barangayBudgetRows.value);
      
      // Log specific status counts
      const pendingCount = barangayBudgetRows.value.filter(plan => plan.status === 'Pending').length;
      const revisionCount = barangayBudgetRows.value.filter(plan => plan.status === 'Revision').length;
      const approvedCount = barangayBudgetRows.value.filter(plan => plan.status === 'Approved').length;
      console.log(`Status counts - Pending: ${pendingCount}, Revision: ${revisionCount}, Approved: ${approvedCount}`);
      
    } else {
      console.error('No budgetPlans array in API response:', response.data);
      barangayBudgetRows.value = [];
    }

    // Update recent activities after fetching budget plans
    updateRecentActivities();
  } catch (error: any) {
    console.error('Exception fetching pending submissions:', error);
    if (error.response) {
      console.error('Error response:', error.response.data);
      console.error('Status:', error.response.status);
    }
    barangayBudgetRows.value = [];
  }
}

function formatDate(date: string | undefined): string {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function getUserBarangay(row: any) {
  // Check all possible locations where barangay might be stored
  if (row.user && row.user.barangay) return row.user.barangay;
  if (row.barangay && typeof row.barangay === 'string' && row.barangay !== 'Default Barangay') return row.barangay;
  if (row.barangay_name) return row.barangay_name;
  return 'Unknown Barangay';
}

// Recent Activity Helper Functions

function getActivityStatusClass(status: string): string {
  switch ((status || '').toLowerCase()) {
    case 'approved': return 'bg-emerald-100 text-emerald-800 border border-emerald-200';
    case 'pending': return 'bg-amber-100 text-amber-800 border border-amber-200';
    case 'revision': return 'bg-blue-100 text-blue-800 border border-blue-200';
    case 'disapproved': return 'bg-red-100 text-red-800 border border-red-200';
    case 'draft': return 'bg-blue-100 text-blue-800 border border-blue-200';
    default: return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
}

function formatActivityDate(date: string): string {
  if (!date) return 'Recently';

  const activityDate = new Date(date);
  const now = new Date();
  const diffInMs = now.getTime() - activityDate.getTime();
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  } else {
    // Show month, day, and year
    return activityDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }
}

function updateRecentActivities() {
  const activities: RecentActivity[] = [];

  // Add budget plan activities
  barangayBudgetRows.value.slice(0, 10).forEach(plan => {
    activities.push({
      id: `budget-${plan.id}`,
      type: 'budget',
      title: `Budget Plan: ${plan.title_desc || plan.gender_issue || 'Untitled'}`,
      user: plan.user_full_name || plan.user_name || 'Unknown User',
      barangay: plan.barangay_name || getUserBarangay(plan),
      date: plan.updated_at || plan.created_at || '',
      status: plan.status || 'pending'
    });
  });

  // Add accomplishment report activities
  accomplishmentReports.value.slice(0, 10).forEach(report => {
    activities.push({
      id: `report-${report.id}`,
      type: 'report',
      title: `Accomplishemnt Report: ${report.title || 'Untitled'}`,
      user: report.user_name || 'Unknown User',
      barangay: report.barangay_name || report.barangay || 'Unknown Barangay',
      date: report.updated_at || report.dateSubmitted || report.created_at || '',
      status: report.status || 'pending'
    });
  });

  // Sort by date (most recent first) and limit to 6 items
  activities.sort((a, b) => {
    const dateA = new Date(a.date || 0).getTime();
    const dateB = new Date(b.date || 0).getTime();
    return dateB - dateA;
  });

  recentActivities.value = activities.slice(0, 6);
}

// Fetch all barangay users for "Not Submitted" calculation
async function fetchAllBarangayUsers() {
  try {
    const response = await fetch('/api/users?role=barangay');
    if (response.ok) {
      const data = await response.json();
      allBarangayUsers.value = data.data || data || [];
      console.log('Fetched all barangay users:', allBarangayUsers.value);
    }
  } catch (error) {
    console.error('Error fetching all barangay users:', error);
  }
}

// Add missing fetchAccomplishmentReports function
async function fetchAccomplishmentReports() {
  try {
    const response = await axios.get('/api/accomplishment-reports');
    console.log('Accomplishment API Response:', response.data);

    if (response.data.success && response.data.reports) {
      // Map the response data with default values like you do for budget plans
      accomplishmentReports.value = response.data.reports.map((report: any) => {
        // Create a copy of the original report object
        const reportData = { ...report };

        // Add or override specific fields for display purposes
        return {
          ...reportData,
          barangay: report.barangay || 'Unknown Barangay',
          user_name: report.user_name || 'Unknown User',
          user_email: report.user_email || 'No Email',
          title: report.title || 'No Title',
          status: report.status || 'Pending',
          date: report.dateSubmitted || report.created_at || 'No Date'
        };
      });
      console.log('Processed accomplishment reports:', accomplishmentReports.value);
    } else {
      console.error('No reports array in API response:', response.data);
      accomplishmentReports.value = [];
    }
  } catch (error: any) {
    console.error('Error fetching accomplishment reports:', error);
    if (error.response) {
      console.error('Error response:', error.response.data);
      console.error('Status:', error.response.status);
    }
    accomplishmentReports.value = [];
  }
}

</script>








