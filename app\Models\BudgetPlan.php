<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BudgetPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'barangay_id',
        'fiscal_year',
        'focused',
        'gender_issue',
        'title_desc',
        'supporting_stats',
        'source_stats',
        'ppa_si',
        'gad_objective',
        'lgu_program',
        'activity',
        'other_activity_category',
        'gad_activity',
        'date_implementation_start',
        'date_implementation_end',
        'performance_target',
        'mooe',
        'ps',
        'co',
        'total_budget',
        'lp_allocation',
        'activity_scores',
        'lead_office',
        'remarks',
        'status',
        'admin_approver',
        'approval_date',
        'approved_by_punong_barangay',
        'is_draft',
        'total_gad_budget',
    ];

    protected $casts = [
        'is_draft' => 'boolean',
        'mooe' => 'float',
        'ps' => 'float',
        'co' => 'float',
        'total_budget' => 'float',
        'lp_allocation' => 'float',
        'activity_scores' => 'array',
        'ppa_si' => 'array',
        'activity' => 'array',
        'date_implementation_start' => 'date',
        'date_implementation_end' => 'date',
        'approval_date' => 'datetime',
        'approved_by_punong_barangay' => 'boolean',
        'total_gad_budget' => 'float',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function barangay()
    {
        return $this->belongsTo(Barangay::class);
    }
}
