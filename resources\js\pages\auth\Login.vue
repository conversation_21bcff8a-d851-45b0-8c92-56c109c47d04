

<template>
    <Head title="Login" />

    <!-- Main Content -->
    <div class="min-h-screen bg-emerald-100 relative overflow-hidden">

        <div class="flex items-center justify-center min-h-screen px-4 py-12 relative z-10">
            <div class="flex bg-white rounded-3xl shadow-2xl overflow-hidden max-w-4xl w-full relative z-20">
                <!-- Left Side - Image/Illustration -->
                <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-emerald-400 to-emerald-600 p-12 items-center justify-center relative">
                    <div class="text-center text-white">
                        <!-- Logo/Icon -->
                        <div class="mb-8">
                            <div class="w-48 h-48 bg-white rounded-full mx-auto flex items-center justify-center shadow-xl p-6">
                                <img
                                    src="/images/panabo-city-logo-1.png"
                                    alt="Panabo City Logo"
                                    class="w-full h-full object-contain"
                                />
                            </div>
                        </div>
                        <h2 class="text-3xl font-bold mb-4 whitespace-nowrap">Gender and Development</h2>
                        <p class="text-emerald-100 text-lg leading-relaxed whitespace-nowrap">
                            Planning and Budgetting Management System
                        </p>
                    </div>

                    <!-- Decorative elements -->
                    <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full"></div>
                    <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full"></div>
                </div>

                <!-- Right Side - Login Form -->
                <div class="w-full lg:w-1/2 p-8 lg:p-12">
                    <!-- Status Messages -->
                    <div v-if="status" class="mb-6 p-4 rounded-xl bg-emerald-50 border border-emerald-200 text-emerald-700 text-sm text-center">
                        {{ status }}
                    </div>

                    <!-- Deactivated Account Message -->
                    <div v-if="form.errors.email && form.errors.email.includes('deactivated')" class="mb-6 p-4 rounded-xl bg-red-50 border border-red-200 text-red-700 text-sm text-center">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span class="font-semibold">Account Deactivated</span>
                        </div>
                        <p>{{ form.errors.email }}</p>
                    </div>

                    <!-- Welcome Header -->
                    <div class="text-center mb-8">
                        <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome Back!</h1>
                        <p class="text-gray-600">Please log in to access your account</p>
                    </div>

                    <!-- Login Form -->
                    <form @submit.prevent="submit" class="space-y-6">
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <div class="relative">
                                <input
                                    id="email"
                                    type="email"
                                    class="w-full px-4 pr-10 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition duration-200 placeholder-gray-500 text-gray-800"
                                    v-model="form.email"
                                    required
                                    autofocus
                                    autocomplete="username"
                                    placeholder="Enter your email"
                                />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                    </svg>
                                </div>
                            </div>
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <div class="relative">
                                <input
                                    id="password"
                                    :type="showPassword ? 'text' : 'password'"
                                    class="w-full px-4 pr-10 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition duration-200 placeholder-gray-500 text-gray-800"
                                    v-model="form.password"
                                    required
                                    autocomplete="current-password"
                                    placeholder="Enter your password"
                                />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button
                                        type="button"
                                        @click="togglePasswordVisibility"
                                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition duration-200"
                                    >
                                        <!-- Eye Open (when password is visible) -->
                                        <svg v-if="showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <!-- Eye Closed (when password is hidden) -->
                                        <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <InputError class="mt-2" :message="form.errors.password" />
                        </div>

                        <!-- Remember Me and Forgot Password -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input
                                    id="remember"
                                    type="checkbox"
                                    v-model="form.remember"
                                    class="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500 transition duration-150 ease-in-out"
                                />
                                <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                            </div>
                            <Link
                                :href="route('password.request')"
                                class="text-emerald-600 hover:text-emerald-700 text-sm font-medium transition duration-200"
                            >
                                Forgot password?
                            </Link>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button
                                type="submit"
                                class="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-4 rounded-xl transition duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 flex items-center justify-center"
                                :disabled="form.processing"
                            >
                                <svg
                                    v-if="form.processing"
                                    class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>
                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                {{ form.processing ? 'Logging in...' : 'Log In' }}
                            </button>
                        </div>

                        <!-- Register Link -->
                        <div class="text-center text-sm text-gray-600 pt-4 border-t border-gray-200">
                            Don't have an account?
                            <Link
                                :href="route('register')"
                                class="text-emerald-600 hover:text-emerald-700 font-medium ml-1 transition duration-200"
                            >
                                Sign Up here
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="absolute bottom-0 w-full py-4 text-center text-sm text-emerald-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                © {{ new Date().getFullYear() }} Panabo City Government - GAD Planning and Budgeting Management System
                <br />
                All Rights Reserved
            </div>
        </footer>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputError from '@/components/InputError.vue';

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const showPassword = ref(false);

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.post('/login', {
        onFinish: () => form.reset('password'),
    });
};
</script>