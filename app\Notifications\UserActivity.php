<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class UserActivity extends Notification implements ShouldQueue, ShouldBroadcast
{
    use Queueable;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    public function toArray($notifiable)
    {
        return [
            'type' => 'user',
            'title' => $this->data['title'] ?? 'User Activity',
            'message' => $this->data['message'] ?? '',
            'data' => $this->data,
            'user_id' => $notifiable->id,
            'created_at' => now()->toDateTimeString(),
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->toArray($notifiable));
    }

    public function broadcastOn()
    {
        return ['App.Models.User.' . $this->data['user_id']];
    }

    public function broadcastType()
    {
        return 'NotificationPushed';
    }
}
