<template>
  <Head title="Budget Plan" />
  <AppShell variant="sidebar">
    <!-- Barangay Sidebar -->
    <AppSidebarBarangay ref="sidebarRef" @spa-tab="handleTabSwitch" />

    <!-- Main Content -->
    <AppContent variant="sidebar">
      <!-- Header -->
      <AppSidebarHeaderBarangay :sidebar-ref="sidebarRef" />

      <!-- Dynamic Content Based on Active Tab -->
      <template v-if="settingsActiveTab">
        <SettingsTab />
      </template>

      <template v-else-if="barangayActiveTab === 'dashboard'">
        <BarangayDashboard @switch-tab="handleTabSwitch" />
      </template>

      <template v-else-if="barangayActiveTab === 'budget'">
        <!-- Budget Plan Content -->
        <div class="bg-white flex font-sans">
          <div class="px-6 py-4">
      <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-4 gap-3">
        <h1 class="text-2xl sm:text-3xl font-bold text-green-900 flex items-center gap-2 flex-shrink-0">
          <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3-1.343-3-3-3zm0 0V4m0 0a8 8 0 100 16v-4"></path></svg>
          <span class="hidden sm:inline">GAD Focal Budget Planning</span>
          <span class="hidden">Budget Planning</span>
        </h1>
        <div class="relative w-full lg:w-auto">
          <div class="relative">
            <div v-if="deadlineLoading" class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-emerald-200">
              <svg class="animate-spin h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
              </svg>
              <span class="text-emerald-700 text-xs font-medium">Loading deadline...</span>
            </div>
            <div v-else-if="!deadlinePassed && globalDeadline"
                 class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-emerald-200 hover:shadow-xl hover:shadow-emerald-200/50 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-105">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="font-medium">Deadline:</span>
              </div>
              <div class="flex flex-col">
                <span class="text-emerald-900 font-bold">{{ deadlineDisplay }}</span>
              </div>
            </div>
            <div v-else-if="deadlinePassed"
                 class="inline-flex items-center gap-2 bg-gradient-to-r from-red-50 to-rose-50 text-red-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-red-200">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span class="font-bold text-red-700">Deadline Passed</span>
              </div>
            </div>
            <div v-else
                 class="inline-flex items-center gap-2 bg-gradient-to-r from-gray-50 to-slate-50 text-gray-600 px-4 py-3 rounded-xl text-sm font-medium shadow-md border border-gray-200">
              <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>No deadline set</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex flex-col sm:flex-row mb-4 gap-2">
        <ui-button
  variant="default"
  class="w-full sm:w-auto px-4 py-2 text-sm sm:text-base font-semibold bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg shadow-md flex items-center justify-center gap-2 transition-all duration-200 border-none focus:ring-2 focus:ring-green-300 focus:outline-none cursor-pointer"
  @click="openFormModal()"
>
  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
  <span class="hidden sm:inline">New Budget Plan</span>
</ui-button>
      </div>

      <!-- Tab Content -->
      <div v-if="activeTab === 'myplans'" class="bg-white">
        <BudgetPlanTable
          :plans="budgetPlans"
          @view="handleViewBudgetPlan"
          @edit="openFormModal"
          @delete="deleteBudgetPlan"
          @download="handleDownload"
        />
      </div>
      <div v-else-if="activeTab === 'barangay'" class="bg-white">
  <!-- BarangayBudgetapprovedTable removed -->
  <BarangayBudgetPlanModal
    v-if="viewModal"
    :show="viewModal"
    :plan="selectedPlan"
    @close="viewModal = false"
  />
</div>
      <!-- Pre-form Modal for Date and Fiscal Year -->
      <div v-if="preFormModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-4">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
              <svg class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-emerald-800">Budget Plan Details</h3>
          </div>
          <p class="text-emerald-700 mb-6">Please provide the fiscal year for this budget plan before proceeding.</p>

          <div class="space-y-4">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <!-- Fiscal Year Field -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Fiscal Year *</label>
                <div ref="preYearPickerRef" class="relative inline-block w-full">
                  <button
                    type="button"
                    class="w-full text-left rounded-lg border border-gray-300 bg-white p-3 text-sm shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all flex items-center justify-between"
                    @click.stop="preYearPickerOpen = !preYearPickerOpen"
                  >
                    <span>{{ preFormData.fiscalYear }}</span>
                    <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                  </button>

                  <div v-if="preYearPickerOpen" class="absolute z-50 mt-1 w-64 bg-white rounded-lg shadow-lg border border-gray-200 p-2">
                    <div class="flex items-center justify-between mb-2">
                      <button class="p-1 rounded hover:bg-gray-100" @click.stop="prePrevYearPage" aria-label="Previous years">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                      </button>
                      <div class="text-xs font-semibold text-gray-700">{{ preYearPageStart }} - {{ preYearPageStart + 11 }}</div>
                      <button class="p-1 rounded hover:bg-gray-100" @click.stop="preNextYearPage" aria-label="Next years">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                      </button>
                    </div>
                    <div class="grid grid-cols-4 gap-1">
                      <button
                        v-for="y in preYearGrid"
                        :key="y"
                        type="button"
                        class="text-xs px-2 py-1 rounded border transition-colors"
                        :class="[
                          y === preFormData.fiscalYear ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'
                        ]"
                        @click.stop="preSelectYear(y)"
                      >
                        {{ y }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Total GAD Budget Field -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Total GAD Budget *</label>
                <input
                  v-model="formattedPreFormTotalGad"
                  type="text"
                  inputmode="numeric"
                  class="w-full rounded-lg border border-gray-300 bg-white p-3 text-sm shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all"
                  placeholder="e.g. 12,000,000"
                  required
                />
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-3 mt-6">
            <button
              @click="closePreFormModal"
              class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              @click="proceedToMainForm"
              class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
            >
              Continue
            </button>
          </div>
        </div>
      </div>

      <!-- Create/Edit Budget Plan Modal -->
      <div v-if="formModal" class="fixed inset-0 z-50 flex items-start sm:items-center justify-center bg-black/30 backdrop-blur-sm p-2 sm:p-4 overflow-y-auto">
        <div class="bg-gradient-to-br from-emerald-700 to-emerald-900 rounded-lg shadow-xl p-3 sm:p-4 w-full max-w-7xl mx-auto flex flex-col min-h-0 max-h-[98vh] sm:max-h-[95vh] my-2 sm:my-4">
          <div class="flex justify-between items-center mb-3 flex-shrink-0">
            <h2 class="text-sm sm:text-base font-semibold text-white">{{ isEditMode ? 'Edit' : 'New' }} Budget Plan</h2>
            
          </div>

          <form @submit.prevent class="space-y-3 sm:space-y-4 overflow-y-auto flex-1 min-h-0">

  <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 w-full">
    <!-- Column 1 -->
    <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
      <div>
        <select v-model="form.focused" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" required>
          <option disabled value="">Focused (required)</option>
          <option>CLIENT-FOCUSED</option>
          <option>ORGANIZATION-FOCUSED</option>
        </select>
        <div v-if="errors.focused" class="text-red-500 text-xs mt-1">{{ errors.focused }}</div>
      </div>
      <div>
        <select v-model="form.genderIssue" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" required>
          <option disabled value="">Gender Issue or GAD Mandate (required)</option>
          <option>Gender Issue </option>
          <option>GAD Mandate</option>
        </select>
        <div v-if="errors.genderIssue" class="text-red-500 text-xs mt-1">{{ errors.genderIssue }}</div>
      </div>
      <div>
        <textarea v-model="form.titleDesc" rows="2" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all resize-none" required placeholder="Title/Description of Gender Issue or GAD Mandate (required)"></textarea>
        <div v-if="errors.titleDesc" class="text-red-500 text-xs mt-1">{{ errors.titleDesc }}</div>
      </div>
      <div>
        <textarea v-model="form.supportingStats" rows="2" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all resize-none" placeholder="Supporting Statistics Data (optional)"></textarea>
      </div>
      <div>
        <textarea v-model="form.sourceStats" rows="2" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all resize-none" placeholder="Source of Supporting Statistics Data (optional)"></textarea>
      </div>
      <div>
        <div class="relative">
  <div
    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 cursor-pointer flex items-center justify-between transition-all hover:bg-emerald-50 min-h-[2.5rem]"
    @click="ppaSiDropdownOpen = !ppaSiDropdownOpen"
    tabindex="0"
    @blur="ppaSiDropdownOpen = false"
  >
    <span class="flex flex-wrap gap-1 min-h-[1.2em] flex-1">
      <template v-if="form.ppaSi.length">
        <span v-for="cat in form.ppaSi" :key="cat" class="inline-flex items-center bg-emerald-500 text-white rounded-md px-2 py-0.5 text-xs mr-1 mb-0.5">
          {{ cat.charAt(0).toUpperCase() + cat.slice(1) }}
          <button type="button" class="ml-1 text-white hover:text-red-200 focus:outline-none" @click.stop="removePpaSi(cat)">×</button>
        </span>
      </template>
      <template v-else>
        <span class="text-gray-400">PPA/s</span>
      </template>
    </span>
    <span class="flex items-center ml-2 space-x-1 flex-shrink-0">
  <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg>
</span>
  </div>
  <div
    v-if="ppaSiDropdownOpen"
    class="absolute z-20 mt-1 w-full bg-white rounded-lg shadow-lg p-2 ring-1 ring-emerald-100 max-h-48 overflow-y-auto"
    @mousedown.prevent
  >
    <label class="flex items-center mb-2 text-sm">
  <input type="checkbox" class="mr-2 rounded text-emerald-500 focus:ring-emerald-500" :checked="isAllPpaSiSelected" @change="toggleAllPpaSi" /> Select All
</label>
<div class="divide-y divide-emerald-50">
  <div
    v-for="opt in ppaSiOptions"
    :key="opt"
    class="py-1.5 px-2 cursor-pointer hover:bg-emerald-50 flex items-center text-sm"
    :class="{'font-medium text-emerald-700': form.ppaSi.includes(opt)}"
    @click="toggleSinglePpaSi(opt)"
  >
    <span>{{ opt.charAt(0).toUpperCase() + opt.slice(1) }}</span>
    <span v-if="form.ppaSi.includes(opt)" class="ml-auto text-emerald-600">✓</span>
  </div>
</div>
  </div>
</div>
      </div>
      <div>
        <textarea v-model="form.gadObjective" rows="2" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all resize-none" required placeholder="GAD Objective (required)"></textarea>
        <div v-if="errors.gadObjective" class="text-red-500 text-xs mt-1">{{ errors.gadObjective }}</div>
      </div>
    </div>
    <!-- Column 2 -->
    <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
      <div>
        <input v-model="form.lguProgram" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" required placeholder="Relevant GAD Program or Project (required)" />
        <div v-if="errors.lguProgram" class="text-red-500 text-xs mt-1">{{ errors.lguProgram }}</div>
      </div>
      <div>
        <div class="relative">
  <div
    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 cursor-pointer flex items-start justify-between transition-all hover:bg-emerald-50 min-h-[2.5rem]"
    @click="activityDropdownOpen = !activityDropdownOpen"
    tabindex="0"
    @blur="activityDropdownOpen = false"
  >
    <div class="flex flex-wrap gap-1.5 min-h-[1.2em] flex-1 pr-2">
      <template v-if="form.activity && form.activity.length">
        <span
          v-for="act in form.activity"
          :key="act"
          class="inline-flex items-center bg-emerald-500 text-white rounded-md px-2 py-1 text-xs font-medium shadow-sm"
        >
          <span class="truncate max-w-[150px] sm:max-w-[200px]">{{ act }}</span>
          <button
            type="button"
            class="ml-1.5 text-white hover:text-red-200 focus:outline-none flex-shrink-0 text-sm font-bold"
            @click.stop="removeActivity(act)"
          >
            ×
          </button>
        </span>
      </template>
      <template v-else>
        <span class="text-gray-400 py-1">Activity</span>
      </template>
    </div>
    <div class="flex items-center space-x-1 flex-shrink-0 ml-2">
      <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
      </svg>
      <button
        v-if="form.activity && form.activity.length"
        type="button"
        class="w-4 h-4 flex items-center justify-center text-emerald-500 hover:text-red-500 rounded focus:outline-none"
        @click.stop="clearActivityDropdown"
        title="Clear selected activities"
      >
        <span class="text-sm leading-none font-bold">&times;</span>
      </button>
    </div>
  </div>
  <div
    v-if="activityDropdownOpen"
    class="absolute z-20 mt-1 w-full bg-white rounded-lg shadow-lg p-2 ring-1 ring-emerald-100 max-h-60 overflow-y-auto"
    @mousedown.prevent
  >
    <label class="flex items-center mb-2 text-sm">
      <input type="checkbox" class="mr-2 rounded text-emerald-500 focus:ring-emerald-500" :checked="isAllActivitySelected" @change="toggleAllActivity" /> Select All
    </label>
    <div class="divide-y divide-emerald-50">
      <div
        v-for="opt in activityOptions"
        :key="opt"
        class="py-1.5 px-2 cursor-pointer hover:bg-emerald-50 flex items-center text-sm"
        :class="{'font-medium text-emerald-700': form.activity && form.activity.includes(opt)}"
        @click="toggleSingleActivity(opt)"
      >
        <span class="flex-1 text-left">{{ opt }}</span>
        <span v-if="form.activity && form.activity.includes(opt)" class="ml-2 text-emerald-600 flex-shrink-0">✓</span>
      </div>
    </div>
  </div>
</div>
      </div>
      <div>
        <input v-model="form.otherActivityCategory" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" placeholder="Other Activity Category Value" />
      </div>
      <div>
        <input v-model="form.gadActivity" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" required placeholder="GAD Activity (required)" />
        <div v-if="errors.gadActivity" class="text-red-500 text-xs mt-1">{{ errors.gadActivity }}</div>
      </div>
      <div>
        <div class="flex flex-col gap-2 sm:gap-3">
  <div class="w-full">
    <div class="relative bg-white rounded-lg">
      <Datepicker
        v-model="dateImplementationStartProxy"
        :format="'yyyy-MM-dd'"
        input-class-name="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
        placeholder="Start of Implementation *"
        required
        :teleport="true"
        :teleport-center="true"
      />
    </div>
    <div v-if="errors.dateImplementationStart" class="text-red-500 text-xs mt-1">{{ errors.dateImplementationStart }}</div>
  </div>
  <div class="w-full">
    <div class="relative bg-white rounded-lg">
      <Datepicker
        v-model="dateImplementationEndProxy"
        :format="'yyyy-MM-dd'"
        input-class-name="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
        placeholder="End of Implementation *"
        required
        :teleport="true"
        :teleport-center="true"
      />
    </div>
    <div v-if="errors.dateImplementationEnd" class="text-red-500 text-xs mt-1">{{ errors.dateImplementationEnd }}</div>
  </div>
</div>
      </div>
      <div>
        <input v-model="form.performanceTarget" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" required placeholder="Performance Target / Indicator (required)" />
        <div v-if="errors.performanceTarget" class="text-red-500 text-xs mt-1">{{ errors.performanceTarget }}</div>
      </div>
    </div>
    <!-- Column 3 -->
    <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
      <div>
        <input v-model="formattedMooe" type="text" inputmode="numeric" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" placeholder="Maintenance + Other operating Expense (mooe)" />
      </div>
      <div>
        <input v-model="formattedPs" type="text" inputmode="numeric" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" placeholder="Personal Service (ps)" />
      </div>
      <div>
        <input v-model="formattedCo" type="text" inputmode="numeric" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" placeholder="Capital Outline (co)" />
      </div>

      <div>
        <textarea v-model="form.leadOffice" rows="2" class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all resize-none" required placeholder="Lead / Responsible Office (required)"></textarea>
        <div v-if="errors.leadOffice" class="text-red-500 text-xs mt-1">{{ errors.leadOffice }}</div>
      </div>

    </div>
  </div>

  <div class="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 mt-4 sm:mt-6 shrink-0 px-1">
    <button type="button" class="w-full sm:w-auto px-4 py-2 sm:py-2.5 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors text-sm font-medium order-3 sm:order-1" @click="closeFormModal">Cancel</button>
    <button
      type="button"
      class="w-full sm:w-auto px-4 py-2 sm:py-2.5 rounded-lg bg-amber-50/90 hover:bg-amber-100 text-amber-700 font-medium transition-colors text-sm order-2 sm:order-2"
      @click="confirmSaveDraft"
      :disabled="loading"
    >
      <span v-if="loading && savingDraft" class="inline-block animate-spin mr-1">↻</span>
      Save as Draft
    </button>
    <button
      type="button"
      class="w-full sm:w-auto px-4 py-2 sm:py-2.5 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium transition-colors text-sm order-1 sm:order-3"
      @click="confirmSubmit"
      :disabled="loading"
    >
      <span v-if="loading && !savingDraft" class="inline-block animate-spin mr-1">↻</span>
      <span v-if="loading && !savingDraft">
        {{ isEditMode ? 'Updating...' : 'Submitting...' }}
      </span>
      <span v-else>
        {{ isEditMode ? 'Update' : 'Submit' }}
      </span>
    </button>
          </div>
        </form>
          </div>
        </div>
        </div>
      </div>
      </template>

      <template v-else-if="barangayActiveTab === 'supplemental'">
        <SupplementalPage />
      </template>

      <template v-else-if="barangayActiveTab === 'revised'">
        <RevisedPlanPage @select-plan="handleExternalEdit" />
      </template>

      <template v-else-if="barangayActiveTab === 'accomplishment'">
        <AccomplishmentReport />
      </template>
    </AppContent>

    <!-- Modal for viewing budget plan -->
    <BarangayBudgetPlanModal
      v-if="viewModal"
      :show="viewModal"
      :plan="selectedPlan"
      @close="viewModal = false"
    />

    <!-- Custom Alert/Confirm Modal -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Confirm Icon -->
            <svg v-else-if="modalType === 'confirm'" class="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button
            v-if="modalType === 'confirm'"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            @click="modalType === 'confirm' ? confirmAction() : closeModal()"
            class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
          >
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </AppShell>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import 'vue-datepicker-next/index.css';
import { format, parseISO } from 'date-fns';
import AppShell from '@/components/AppShell.vue';
import AppContent from '@/components/AppContent.vue';
import AppSidebarHeaderBarangay from '@/components/AppSidebarHeaderBarangay.vue';
import AppSidebarBarangay from '@/components/AppSidebarBarangay.vue';
import BudgetPlanTable from '@/components/BarangayBudgetPlanTable.vue';
import BarangayBudgetPlanModal from '@/components/BarangayBudgetPlanModal.vue';
import BarangayDashboard from '@/components/BarangayDashboard.vue';
import AccomplishmentReport from '@/pages/AccomplishmentReport.vue';
import SettingsTab from '@/components/SettingsTab.vue';
import SupplementalPage from './SupplementalPage.vue';
import RevisedPlanPage from './RevisedPlanPage.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { barangayActiveTab } from '../dashboardTabState';
import { settingsActiveTab } from '../settingsTabState';

// Set up axios with CSRF token
axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
axios.defaults.withCredentials = true;

// Loading states
const loading = ref(false);
const savingDraft = ref(false);
const refreshing = ref(false);
// Sidebar reference
const sidebarRef = ref();

// Modal states
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref('info'); // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref<(() => void) | null>(null);
const returnToTab = ref<null | 'revised'>(null);

// Handle edit selection coming from external tabs (revised)
async function handleExternalEdit(plan: any) {
  // Switch to budget behind the scenes so the modal exists, then restore tab after close
  settingsActiveTab.value = null;
  returnToTab.value = 'revised';
  barangayActiveTab.value = 'budget';
  await nextTick();
  try { console.debug('[RevisedPlan] opening edit modal for plan', plan?.id || plan); } catch (e) {}
  openFormModal(plan);
}

// Modal functions
function showModal(title: string, message: string, type: string = 'info') {
  console.log('showModal called:', { title, message, type });
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = type;
  modalConfirmCallback.value = null;
  modalVisible.value = true;
  console.log('Modal state after setting:', {
    modalVisible: modalVisible.value,
    modalTitle: modalTitle.value,
    modalMessage: modalMessage.value,
    modalType: modalType.value
  });
}

function showConfirmModal(title: string, message: string, onConfirm: () => void) {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = 'confirm';
  modalConfirmCallback.value = onConfirm;
  modalVisible.value = true;
}

function closeModal() {
  modalVisible.value = false;
  modalConfirmCallback.value = null;
}

function confirmAction() {
  if (modalConfirmCallback.value) {
    modalConfirmCallback.value();
  }
  closeModal();
}

// Confirmation functions for draft and submit
function confirmSaveDraft() {
  showConfirmModal(
    'Save as Draft',
    'Are you sure you want to save this budget plan as a draft? You can edit it later before submitting.',
    () => {
      saveDraft();
    }
  );
}

function confirmSubmit() {
  // Check validation first
  if (!validateForm()) {
    showModal('Validation Error', 'Please fill in all required fields before submitting.', 'error');
    return;
  }

  // Check if budget meets minimum 5% requirement
  // Use the combined total budget which includes LP optimization allocation
  const totalBudget = combinedTotalBudget.value;
  // Get total available budget from localStorage (set by admin) or use fallback
  const totalAvailableBudget = Number(localStorage.getItem('gad_total_budget')) || 91407936.05;
  const minimumRequired = totalAvailableBudget * 0.05; // 5% of total available budget

  if (totalBudget < minimumRequired) {
    const formattedMinimum = formatCurrency(minimumRequired);
    const formattedCurrent = formatCurrency(totalBudget);

    showModal(
      'Budget Below Minimum Requirement',
      `Your current budget allocation (${formattedCurrent}) is below the minimum 5% requirement (${formattedMinimum}).

Please increase your budget allocation to meet the minimum requirement before submitting, or save as draft to continue working on it later.`,
      'error'
    );
    return;
  }

  showConfirmModal(
    isEditMode.value ? 'Update Budget Plan' : 'Submit Budget Plan',
    isEditMode.value
      ? 'Are you sure you want to update this budget plan? This action cannot be undone.'
      : 'Are you sure you want to submit this budget plan for review? Once submitted, you cannot edit it until it is reviewed.',
    () => {
      handleFormSave();
    }
  );
}

// Tab switching function
function handleTabSwitch(tab: 'dashboard' | 'budget' | 'accomplishment' | 'supplemental' | 'settings') {
  if (tab === 'settings') {
    settingsActiveTab.value = 'general';
    // Don't reset barangayActiveTab - let the settings template show
  } else {
    barangayActiveTab.value = tab;
    settingsActiveTab.value = null; // Clear settings tab when switching to other tabs
  }
  
  if (tab === 'budget') {
    fetchBudgetPlans();
    fetchBudgetPlanDeadline(); // Auto-refresh deadline when switching to budget tab
  }
}


// Tab state
// --- Viewing Modal Props ---
// These computed properties derive the focusType and issueOrMandate for the viewed plan


const activeTab = ref('myplans');

function handleViewBudgetPlan(plan: any) {
  selectedPlan.value = plan;
  viewModal.value = true;
}


const globalDeadline = ref(null);
const deadlineLoading = ref(false);

// --- Deadline Logic ---
const deadline = ref(new Date('2025-04-30T17:00:00+08:00'));
const deadlineDisplay = computed(() => {
  const d = deadline.value;
  // Format: August 28, 2025 at 03:51 PM
  const datePart = d.toLocaleDateString('en-US', {
    month: 'long', day: 'numeric', year: 'numeric'
  });
  const timePart = d.toLocaleTimeString('en-US', {
    hour: '2-digit', minute: '2-digit', hour12: true
  });
  return `${datePart} at ${timePart}`;
});
const deadlinePassed = ref(false);
let intervalId: any = null;

async function fetchBudgetPlanDeadline() {
  deadlineLoading.value = true;
  try {
    const response = await axios.get('/api/budget-plan-deadline');
    if (response.data.success && response.data.deadline) {
      globalDeadline.value = response.data.deadline;
      deadline.value = new Date(response.data.deadline);
    }
  } catch (error) {
    globalDeadline.value = null;
    deadline.value = new Date('2025-04-30T17:00:00+08:00');
  } finally {
    deadlineLoading.value = false;
  }
}

onMounted(async () => {
  // Ensure we start with dashboard view, not settings
  settingsActiveTab.value = null;
  barangayActiveTab.value = 'dashboard';

  await fetchBudgetPlanDeadline();
  intervalId = setInterval(fetchBudgetPlanDeadline, 60000); // Update every minute
});
onUnmounted(() => {
  if (intervalId) clearInterval(intervalId);
});

const budgetPlans = ref<any[]>([]);

const viewModal = ref(false);
const selectedPlan = ref<any>(null);

const formModal = ref(false);
const isEditMode = ref(false);
const editIndex = ref(-1);

// Pre-form modal for Date and Fiscal Year
const preFormModal = ref(false);
const preFormData = reactive({
  planDate: '',
  fiscalYear: new Date().getFullYear(),
  totalGadBudget: 0
});

// Year picker state for pre-form
const preYearPickerOpen = ref(false);
const preYearPickerRef = ref<HTMLElement | null>(null);
const preYearPageStart = ref<number>(preFormData.fiscalYear - 6);
const preYearGrid = computed<number[]>(() => Array.from({ length: 12 }, (_, i) => preYearPageStart.value + i));
function prePrevYearPage() {
  preYearPageStart.value -= 12;
}
function preNextYearPage() {
  preYearPageStart.value += 12;
}
function preSelectYear(y: number) {
  preFormData.fiscalYear = y;
  preYearPageStart.value = y - 6;
  preYearPickerOpen.value = false;
}

onMounted(() => {
  // Close pre-year picker on outside click
  document.addEventListener('click', (e: MouseEvent) => {
    const target = e.target as Node;
    if (preYearPickerOpen.value && preYearPickerRef.value && !preYearPickerRef.value.contains(target)) {
      preYearPickerOpen.value = false;
    }
  });
});

// Fiscal years array (kept for potential use elsewhere)
const currentYear = new Date().getFullYear();
const fiscalYears = ref([
  currentYear - 2,
  currentYear - 1,
  currentYear,
  currentYear + 1,
  currentYear + 2
]);

const dateImplementationStartProxy = computed({
  get() {
    return form.dateImplementationStart ? parseISO(form.dateImplementationStart) : undefined;
  },
  set(val: Date | undefined) {
    form.dateImplementationStart = val ? format(val, 'yyyy-MM-dd') : '';
  }
});
const dateImplementationEndProxy = computed({
  get() {
    return form.dateImplementationEnd ? parseISO(form.dateImplementationEnd) : undefined;
  },
  set(val: Date | undefined) {
    form.dateImplementationEnd = val ? format(val, 'yyyy-MM-dd') : '';
  }
});

const planDateProxy = computed({
  get() {
    return preFormData.planDate ? parseISO(preFormData.planDate) : undefined;
  },
  set(val: Date | undefined) {
    preFormData.planDate = val ? format(val, 'yyyy-MM-dd') : '';
  }
});

const form = reactive({
  planDate: '',
  fiscalYear: new Date().getFullYear(),
  focused: '',
  genderIssue: '',
  titleDesc: '',
  supportingStats: '',
  sourceStats: '',
  ppaSi: [] as string[],
  gadObjective: '',
  lguProgram: '',
  activity: [] as string[],
  otherActivityCategory: '',
  gadActivity: '',
  dateImplementationStart: '',
  dateImplementationEnd: '',
  performanceTarget: '',
  mooe: 0,
  ps: 0,
  co: 0,
  leadOffice: '',
  items: [ { name: '', cost: 0, qty: 1 } ],
  remarks: '',
  totalGadBudget: 0,
});
const errors = reactive<any>({});

// LP optimization removed

// Computed property for combined total budget
const combinedTotalBudget = computed(() => {
  return (form.mooe || 0) + (form.ps || 0) + (form.co || 0);
});

// LP estimation removed

function openFormModal(plan: any = null) {
  if (plan) {
    // We're in edit mode - map API fields to form structure and go directly to main form
    const mapped = mapApiPlanToForm(plan);
    Object.assign(form, mapped);
    isEditMode.value = true;
    editIndex.value = budgetPlans.value.findIndex((p: any) => p.id === plan.id);

    // Ensure arrays
    if (!Array.isArray(form.ppaSi)) form.ppaSi = form.ppaSi ? [form.ppaSi as any] : [];
    if (!Array.isArray(form.activity)) form.activity = form.activity ? [form.activity as any] : [];

    // Items are not provided by API; keep at least one blank row
    form.items = Array.isArray(mapped.items) && mapped.items.length > 0
      ? mapped.items.map((i: any) => ({ ...i }))
      : [{ name: '', cost: 0, qty: 1 }];

    // For edit mode, go directly to main form
    formModal.value = true;
  } else {
    // For new budget plan, show pre-form modal first
    resetPreFormData();
    isEditMode.value = false;
    editIndex.value = -1;
    preFormModal.value = true;
  }
}

function mapApiPlanToForm(apiPlan: any) {
  // Safely parse possible JSON strings into arrays
  const toArray = (val: any): string[] => {
    if (Array.isArray(val)) return val;
    if (typeof val === 'string') {
      const s = val.trim();
      if ((s.startsWith('[') && s.endsWith(']')) || (s.startsWith('{') && s.endsWith('}'))) {
        try {
          const parsed = JSON.parse(s);
          return Array.isArray(parsed) ? parsed : (parsed ? [String(parsed)] : []);
        } catch {}
      }
      return s ? [s] : [];
    }
    return [];
  };

  // Normalize date strings (yyyy-MM-dd)
  const normalizeDate = (d: any) => {
    if (!d) return '';
    try {
      const dt = new Date(d);
      if (isNaN(dt.getTime())) return String(d);
      const yyyy = dt.getFullYear();
      const mm = String(dt.getMonth() + 1).padStart(2, '0');
      const dd = String(dt.getDate()).padStart(2, '0');
      return `${yyyy}-${mm}-${dd}`;
    } catch { return String(d); }
  };

  return {
    // Simple maps
    fiscalYear: apiPlan.fiscal_year ?? form.fiscalYear,
    focused: apiPlan.focused ?? form.focused,
    genderIssue: apiPlan.gender_issue ?? apiPlan.genderIssue ?? form.genderIssue,
    titleDesc: apiPlan.title_desc ?? apiPlan.titleDesc ?? form.titleDesc,
    supportingStats: apiPlan.supporting_stats ?? apiPlan.supportingStats ?? form.supportingStats,
    sourceStats: apiPlan.source_stats ?? apiPlan.sourceStats ?? form.sourceStats,
    gadObjective: apiPlan.gad_objective ?? apiPlan.gadObjective ?? form.gadObjective,
    lguProgram: apiPlan.lgu_program ?? apiPlan.lguProgram ?? form.lguProgram,
    otherActivityCategory: apiPlan.other_activity_category ?? apiPlan.otherActivityCategory ?? form.otherActivityCategory,
    gadActivity: apiPlan.gad_activity ?? apiPlan.gadActivity ?? form.gadActivity,
    performanceTarget: apiPlan.performance_target ?? apiPlan.performanceTarget ?? form.performanceTarget,
    mooe: Number(apiPlan.mooe ?? form.mooe ?? 0),
    ps: Number(apiPlan.ps ?? form.ps ?? 0),
    co: Number(apiPlan.co ?? form.co ?? 0),
    leadOffice: apiPlan.lead_office ?? apiPlan.leadOffice ?? form.leadOffice,
    remarks: apiPlan.remarks ?? form.remarks,
    totalGadBudget: Number(apiPlan.total_gad_budget ?? apiPlan.totalGadBudget ?? form.totalGadBudget ?? 0),

    // Arrays
    ppaSi: toArray(apiPlan.ppa_si ?? apiPlan.ppaSi ?? form.ppaSi),
    activity: toArray(apiPlan.activity ?? form.activity),

    // Dates
    dateImplementationStart: normalizeDate(apiPlan.date_implementation_start ?? apiPlan.dateImplementationStart ?? form.dateImplementationStart),
    dateImplementationEnd: normalizeDate(apiPlan.date_implementation_end ?? apiPlan.dateImplementationEnd ?? form.dateImplementationEnd),

    // Leave items as-is; API does not return items list
    items: form.items,
  };
}
function closeFormModal() {
  formModal.value = false;
  if (returnToTab.value) {
    barangayActiveTab.value = returnToTab.value;
    returnToTab.value = null;
  }
  resetForm();
}

// Pre-form modal functions
function resetPreFormData() {
  preFormData.fiscalYear = new Date().getFullYear();
  preFormData.totalGadBudget = 0;
}

function closePreFormModal() {
  preFormModal.value = false;
  resetPreFormData();
}

function proceedToMainForm() {
  // Validate pre-form data
  if ( !preFormData.fiscalYear) {
    showModal('Validation Error', 'Please fill in both Date and Fiscal Year before proceeding.', 'error');
    return;
  }

  // Transfer pre-form data to main form
  form.fiscalYear = preFormData.fiscalYear;
  form.totalGadBudget = preFormData.totalGadBudget;

  // Persist to localStorage so barangay info card reflects immediately
  try {
    if (preFormData.totalGadBudget && preFormData.totalGadBudget > 0) {
      localStorage.setItem('gad_total_budget', String(preFormData.totalGadBudget));
      // Save year-specific value as well
      localStorage.setItem(`gad_total_budget_${preFormData.fiscalYear}`, String(preFormData.totalGadBudget));
    }
  } catch (e) {}

  // Reset main form for new entry
  resetForm();

  // Keep the pre-form data in the main form
  form.fiscalYear = preFormData.fiscalYear;
  form.totalGadBudget = preFormData.totalGadBudget;

  // Close pre-form modal and open main form
  preFormModal.value = false;
  formModal.value = true;
}

function validateForm() {
  // Reset all errors first
  Object.keys(errors).forEach(key => errors[key] = '');

  // Check required fields
  errors.focused = form.focused ? '' : 'Required';
  errors.genderIssue = form.genderIssue ? '' : 'Required';
  errors.titleDesc = form.titleDesc ? '' : 'Required';
  errors.gadObjective = form.gadObjective ? '' : 'Required';
  errors.gadActivity = form.gadActivity ? '' : 'Required';
  errors.leadOffice = form.leadOffice ? '' : 'Required';

  // Optional validation for dates and numbers
  if (!form.dateImplementationStart) errors.dateImplementationStart = 'Required';
  if (!form.dateImplementationEnd) errors.dateImplementationEnd = 'Required';
  if (!form.performanceTarget) errors.performanceTarget = 'Required';

  // Check if any errors exist
  const hasErrors = Object.values(errors).some(error => error !== '');
  console.log("Validation result:", !hasErrors, errors);

  return !hasErrors;
}
function resetForm() {
  // Preserve planDate and fiscalYear
  const preservedPlanDate = form.planDate;
  const preservedFiscalYear = form.fiscalYear;
  const preservedTotalGad = form.totalGadBudget;

  form.focused = '';
  form.genderIssue = '';
  form.titleDesc = '';
  form.supportingStats = '';
  form.sourceStats = '';
  form.ppaSi = [];
  form.gadObjective = '';
  form.lguProgram = '';
  form.activity = [];
  form.otherActivityCategory = '';
  form.gadActivity = '';
  form.dateImplementationStart = '';
  form.dateImplementationEnd = '';
  form.performanceTarget = '';
  form.mooe = 0;
  form.ps = 0;
  form.co = 0;
  form.leadOffice = '';
  form.items = [ { name: '', cost: 0, qty: 1 } ];
  form.remarks = '';
  Object.keys(errors).forEach(k => errors[k] = '');

  // Restore preserved values
  form.planDate = preservedPlanDate;
  form.fiscalYear = preservedFiscalYear;
  form.totalGadBudget = preservedTotalGad;

  // LP optimization removed
}
async function handleFormSave(event?: Event) {
  console.log("Form submission triggered");

  // Ensure we prevent any default form submission behavior
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  // Check validation
  if (!validateForm()) {
    console.log("Validation failed", errors);
    return;
  }

  loading.value = true;
  savingDraft.value = false;

  try {
    // Create plan data object from form
    const planData = {
      planDate: form.planDate,
      fiscalYear: form.fiscalYear,
      totalGadBudget: parseNumber(form.totalGadBudget),
      focused: form.focused,
      genderIssue: form.genderIssue,
      titleDesc: form.titleDesc,
      supportingStats: form.supportingStats,
      sourceStats: form.sourceStats,
      ppaSi: form.ppaSi,
      gadObjective: form.gadObjective,
      lguProgram: form.lguProgram,
      activity: form.activity,
      otherActivityCategory: form.otherActivityCategory,
      gadActivity: form.gadActivity,
      dateImplementationStart: form.dateImplementationStart,
      dateImplementationEnd: form.dateImplementationEnd,
      performanceTarget: form.performanceTarget,
      mooe: parseNumber(form.mooe),
      ps: parseNumber(form.ps),
      co: parseNumber(form.co),
      totalBudget: parseNumber(form.mooe) + parseNumber(form.ps) + parseNumber(form.co),
      leadOffice: form.leadOffice,
      items: form.items,
      remarks: form.remarks
    };

    console.log("Sending data to API:", planData);

    let response;
    let url = '/api/budget-plans';

    if (isEditMode.value && editIndex.value > -1) {
      // Update existing plan
      const planId = budgetPlans.value[editIndex.value].id;
      url = `/api/budget-plans/${planId}`;
      console.log(`Updating plan with ID ${planId} at URL: ${url}`);
      response = await axios.put(url, planData);
    } else {
      // Create new plan
      console.log(`Creating new plan at URL: ${url}`);
      response = await axios.post(url, planData);
    }

    console.log("API response:", response.data);

    // Update the local budgetPlans array directly instead of refetching
    if (isEditMode.value && editIndex.value > -1) {
      // Update existing plan in the array
      console.log("Updating existing plan at index:", editIndex.value);
      budgetPlans.value[editIndex.value] = { ...budgetPlans.value[editIndex.value], ...response.data.budgetPlan };
    } else {
      // Add new plan to the array
      console.log("Adding new plan to array");
      if (response.data.budgetPlan) {
        budgetPlans.value.unshift(response.data.budgetPlan);
      }
    }

    console.log("Updated budgetPlans array:", budgetPlans.value);

    // Show success modal
    showModal('Success', `Budget plan ${isEditMode.value ? 'updated' : 'submitted'} successfully!`, 'success');

    // Close the modal and reset the form
    console.log("Closing modal...");
    closeFormModal();
    console.log("Form submission completed successfully");
    return; // Ensure we exit the function

  } catch (error: any) {
    console.error('Error saving budget plan:', error.response?.data || error.message);
    showModal('Error', 'Failed to save budget plan. Please check console for details.');
    return; // Exit on error
  } finally {
    loading.value = false;
  }
}

async function saveDraft() {
  loading.value = true;
  savingDraft.value = true;

  try {
    // Create plan data object from form without validation
    const planData = {
      planDate: form.planDate,
      fiscalYear: form.fiscalYear,
      totalGadBudget: parseNumber(form.totalGadBudget),
      focused: form.focused,
      genderIssue: form.genderIssue,
      titleDesc: form.titleDesc,
      supportingStats: form.supportingStats,
      sourceStats: form.sourceStats,
      ppaSi: form.ppaSi,
      gadObjective: form.gadObjective,
      lguProgram: form.lguProgram,
      activity: form.activity,
      otherActivityCategory: form.otherActivityCategory,
      gadActivity: form.gadActivity,
      dateImplementationStart: form.dateImplementationStart,
      dateImplementationEnd: form.dateImplementationEnd,
      performanceTarget: form.performanceTarget,
      mooe: parseNumber(form.mooe),
      ps: parseNumber(form.ps),
      co: parseNumber(form.co),
      totalBudget: parseNumber(form.mooe) + parseNumber(form.ps) + parseNumber(form.co),
      leadOffice: form.leadOffice,
      items: form.items,
      remarks: form.remarks
    };

    const response = await axios.post('/api/budget-plans/draft', planData);
    console.log("Draft saved:", response.data);

    // Update the local budgetPlans array directly instead of refetching
    if (response.data.budgetPlan) {
      budgetPlans.value.unshift(response.data.budgetPlan);
    }

    // Close the modal and reset the form
    closeFormModal();

  } catch (error) {
    console.error('Error saving draft:', error);
    showModal('Error', 'Failed to save draft. Please try again.');
  } finally {
    loading.value = false;
    savingDraft.value = false;
  }
}
async function fetchBudgetPlans() {
  refreshing.value = true;
  budgetPlans.value = []; // Clear the array so the table is empty during fetch
  try {
    console.log("Fetching budget plans...");
    const response = await axios.get('/api/budget-plans');
    console.log("Fetched budget plans:", response.data);
    budgetPlans.value = response.data.budgetPlans;
  } catch (error: any) {
    console.error('Error fetching budget plans:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      showModal('Authentication Required', 'You need to be logged in to access this feature.');
    } else if (error.response?.status === 404) {
      console.error('API endpoint not found. Check your routes configuration.');
    }
  } finally {
    refreshing.value = false;
  }
}

function handleDownload(plan: any) {
  showModal('Download', `Downloading plan: ${plan.title}`);
}


import Datepicker from 'vue3-datepicker';

// --- Activity Dropdown Logic ---
const activityOptions = [
  'Capdev/Training - gender sensitivity trainings (GST)',
  'Capdev/Training - gender analysis',
  'Capdev/Training - gender responsive planning and budgeting',
  'Capdev/Training - gad related policies',
  'development of IEC materials',
  'PPAs related to the implementation of republic act no. 10354-reproductive health law',
  'establishment of violence against women and their children (VAWC) Center',
  'establishment / Maintenance of day care center',
  'establishment / Maintenance of Women Crisis Center',
  'establishment / Maintenance of haftway Houses for traficked women and girls',
  'institutional meachanism to implement the MCW - creation and/or strengthening the LGU GFPS',
  'institutional meachanism to implement the MCW - establishment & maintenance of GAD database',
  'institutional meachanism to implement the MCW - GAD planning and budgetting',
  'institutional meachanism to implement the MCW - mainstreaming gender perspective in lolcal development plans',
  'institutional meachanism to implement the MCW - Development plans',
  'institutional meachanism to implement the MCW - formulation/enhancement and implementation of the lgu GAD code',
  'provision of the child and material healthcare programs',
  'establishment / maintenance of the child development center',
  'Establishment of gender based violence (GBV) reporting and referral mechanisms',
  'GAD related laws / policies (with gender issues, clearly stated)',
  'provision of targeted assistance/support such as livelihood programs to vurnerable, unemployed and/ or indigent women heads of families or single mothers',
];

const activityDropdownOpen = ref(false);
if (!Array.isArray(form.activity)) form.activity = [];

const isAllActivitySelected = computed(() => {
  return activityOptions.every(opt => form.activity.includes(opt));
});

function toggleAllActivity() {
  if (isAllActivitySelected.value) {
    form.activity = [];
  } else {
    form.activity = [...activityOptions];
  }
}

function toggleSingleActivity(opt: string) {
  if (form.activity.includes(opt)) {
    form.activity = form.activity.filter(o => o !== opt);
  } else {
    form.activity = [...form.activity, opt];
  }
}

function removeActivity(act: string) {
  form.activity = form.activity.filter(o => o !== act);
}
function clearActivityDropdown() {
  form.activity = [];
  activityDropdownOpen.value = false;
}
// --- End Activity Dropdown Logic ---

// --- PPA SI Dropdown Logic ---
const ppaSiOptions = [
  'economic',
  'infrastructure',
  'environmental',
  'institutional',
];

const ppaSiDropdownOpen = ref(false);

// Ensure form.ppaSi is always an array
if (!Array.isArray(form.ppaSi)) form.ppaSi = [];

const isAllPpaSiSelected = computed(() => {
  return ppaSiOptions.every(opt => form.ppaSi.includes(opt));
});

function toggleAllPpaSi() {
  if (isAllPpaSiSelected.value) {
    form.ppaSi = [];
  } else {
    form.ppaSi = [...ppaSiOptions];
  }
}

function toggleSinglePpaSi(opt: string) {
  if (form.ppaSi.includes(opt)) {
    form.ppaSi = form.ppaSi.filter(o => o !== opt);
  } else {
    form.ppaSi = [...form.ppaSi, opt];
  }
}

function removePpaSi(cat: string) {
  form.ppaSi = form.ppaSi.filter(o => o !== cat);
}
// --- End PPA SI Dropdown Logic ---

// Helper function to format numbers with commas
function formatNumberWithCommas(value: any) {
  if (value === null || value === undefined || value === '' || value === 0) return '';
  const num = Number(String(value).replace(/,/g, ''));
  if (isNaN(num) || num === 0) return '';
  return num.toLocaleString('en-US');
}

// Add this helper function to parse number values
function parseNumber(value: any): number {
  if (!value) return 0;
  // Remove currency symbols and commas
  const cleanValue = String(value).replace(/[^\d.-]/g, '');
  return parseFloat(cleanValue) || 0;
}

// Format currency function
function formatCurrency(value: number): string {
  if (!value && value !== 0) return '₱0.00';
  return '₱' + Math.floor(Number(value)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

const formattedMooe = computed({
  get() {
    return formatNumberWithCommas(form.mooe);
  },
  set(val) {
    form.mooe = parseNumber(val);
  }
});
const formattedPs = computed({
  get() {
    return formatNumberWithCommas(form.ps);
  },
  set(val) {
    form.ps = parseNumber(val);
  }
});
const formattedCo = computed({
  get() {
    return formatNumberWithCommas(form.co);
  },
  set(val) {
    form.co = parseNumber(val);
  }
});

// Formatted binding for Total GAD Budget in pre-form
const formattedPreFormTotalGad = computed({
  get() {
    return formatNumberWithCommas(preFormData.totalGadBudget);
  },
  set(val) {
    preFormData.totalGadBudget = parseNumber(val);
  }
});

function deleteBudgetPlan(plan: any) {
  showConfirmModal(
    'Delete Budget Plan',
    `Are you sure you want to delete the budget plan "${plan.title || 'Untitled Plan'}"?`,
    () => {
      // Remove the plan from the budgetPlans array
      budgetPlans.value = budgetPlans.value.filter((p: any) => p.id !== plan.id);
    }
  );
}

// Consolidated onMounted to load budget plans and admin feedback
onMounted(() => {
  // Fetch budget plans from API
  fetchBudgetPlans();

  try {
    const savedPlans = localStorage.getItem('my_budget_plans');
    if (savedPlans) {
      const parsedPlans = JSON.parse(savedPlans);
      if (Array.isArray(parsedPlans) && parsedPlans.length > 0) {
        budgetPlans.value = parsedPlans;
      }
    }

    // Check for admin feedback on our submissions
    const dashboardSubmissions = JSON.parse(localStorage.getItem('dashboard_budget_submissions') || '[]');

    // Update our local budget plans with any status changes or comments from admin
    budgetPlans.value.forEach((plan: any, index: number) => {
      const dashboardPlan = dashboardSubmissions.find((sub: any) => sub.id === plan.id);
      if (dashboardPlan) {
        // Update status and remarks if changed by admin
        if (dashboardPlan.status !== plan.status) {
          budgetPlans.value[index].status = dashboardPlan.status;
        }
        if (dashboardPlan.adminRemarks) {
          budgetPlans.value[index].adminRemarks = dashboardPlan.adminRemarks;
        }
      }
    });
  } catch (error) {
    console.error('Error loading budget plans:', error);
  }
});

</script>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
}
th, td {
  border-bottom: 1px solid #e5e7eb;
}
</style>
































