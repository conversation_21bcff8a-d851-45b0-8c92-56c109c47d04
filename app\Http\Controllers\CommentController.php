<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comment;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    // Get all comments for a plan
    public function index(Request $request, $plan_id)
    {
        $comments = Comment::where('plan_id', $plan_id)->get();
        return response()->json($comments);
    }

    // Store or update a comment for a specific cell
    public function storeOrUpdate(Request $request)
    {
        $validated = $request->validate([
            'plan_id' => 'required|integer',
            'column_key' => 'required|string',
            'row_index' => 'nullable|integer',
            'status' => 'nullable|string',
            'text' => 'required|string',
        ]);

        $author_id = Auth::id() ?? $request->user()->id ?? $request->input('author_id');
        if (!$author_id) {
            return response()->json(['error' => 'No author_id'], 401);
        }

        $comment = Comment::updateOrCreate(
            [
                'plan_id' => $validated['plan_id'],
                'column_key' => $validated['column_key'],
                'row_index' => $validated['row_index'] ?? 0,
                'author_id' => $author_id,
            ],
            [
                'status' => $validated['status'] ?? null,
                'text' => $validated['text'],
            ]
        );

        return response()->json($comment);
    }
} 