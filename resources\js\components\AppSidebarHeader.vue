<script setup lang="ts">
import type { BreadcrumbItemType } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { adminActiveTab } from '../dashboardTabState';
import { Menu } from 'lucide-vue-next';
import { useNotifications } from '../composables/useNotifications';

const page = usePage<{ auth: { user: { id: number, role: string } } }>();
const userRole = page.props.auth.user?.role;

// Use the notifications composable
const {
  notifications,
  unreadCount,
  loading,
  error,
  isPolling,
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  getNotificationIcon,
  init: initNotifications,
  cleanup: cleanupNotifications
} = useNotifications();

// Visual feedback for new notifications
const hasNewNotifications = ref(false);
const previousUnreadCount = ref(0);
const showToast = ref(false);
const toastMessage = ref('');

// Watch for changes in unread count to show visual feedback
watch(unreadCount, (newCount, oldCount) => {
  if (newCount > oldCount && oldCount > 0) {
    // New notification received
    hasNewNotifications.value = true;
    
    // Show notification sound or visual feedback
    showNewNotificationFeedback();
    
    // Show toast notification
    toastMessage.value = `You have ${newCount - oldCount} new notification${newCount - oldCount > 1 ? 's' : ''}`;
    showToast.value = true;
    
    // Reset the flag after 3 seconds
    setTimeout(() => {
      hasNewNotifications.value = false;
    }, 3000);
    
    // Hide toast after 4 seconds
    setTimeout(() => {
      showToast.value = false;
    }, 4000);
  }
  previousUnreadCount.value = newCount;
});

// Show visual feedback for new notifications
function showNewNotificationFeedback() {
  // You can add sound notification here
  // const audio = new Audio('/notification-sound.mp3');
  // audio.play().catch(() => {}); // Ignore errors if audio fails to play
  
  // Or show a toast notification
  console.log('New notification received!');
}

// Dynamic page title based on active tab
const pageTitle = computed(() => {
  switch (adminActiveTab.value) {
    case 'dashboard':
      return {
        title: 'GAD Dashboard',
        subtitle: 'Gender and Development Management System'
      };
    case 'budget':
      return {
        title: 'Budget Plans',
        subtitle: 'Manage and review budget submissions'
      };
    case 'accomplishment':
      return {
        title: 'Accomplishment Reports',
        subtitle: 'Track and monitor project progress'
      };
    case 'approved-plans':
      return {
        title: 'Approved Plans',
        subtitle: 'View approved budget plans'
      };
    case 'approved-reports':
      return {
        title: 'Approved Reports',
        subtitle: 'View approved accomplishment reports'
      };
    case 'users':
      return {
        title: 'User Management',
        subtitle: 'Manage system users and permissions'
      };
    case 'notices':
      return {
        title: 'System Notices',
        subtitle: 'Create and manage system-wide announcements'
      };
    default:
      return {
        title: 'GAD Dashboard',
        subtitle: 'Gender and Development Management System'
      };
  }
});

// Notification state
const showNotifications = ref(false);

function toggleNotifications() {
  showNotifications.value = !showNotifications.value;
  if (showNotifications.value) {
    // Refresh notifications when opening
    fetchNotifications();
  }
}

function closeNotifications() {
  showNotifications.value = false;
}

function handleMarkAsRead(notificationId: number) {
  markAsRead(notificationId);
}

function handleMarkAllAsRead() {
  markAllAsRead();
}

const props = defineProps<{
    breadcrumbs?: BreadcrumbItemType[];
    sidebarRef?: any;
}>();

// Toggle sidebar function
const toggleSidebar = () => {
  if (props.sidebarRef) {
    props.sidebarRef.toggleSidebar();
  }
};

// Initialize notifications on mount
onMounted(() => {
  const userId = page.props.auth.user?.id;
  if (userRole === 'admin' && userId) {
    initNotifications(userId, true); // Pass correct userId and enable polling
  }
});

// Cleanup on unmount
onUnmounted(() => {
  cleanupNotifications();
});
</script>

<style scoped>
.scrollbar-none {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.scrollbar-none::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}
</style>

<template>
    <!-- Header with notification icon positioned to the far right -->
    <div class="w-full">
        <!-- Toast Notification -->
        <div
            v-if="showToast"
            class="fixed top-4 right-4 z-[10000] bg-emerald-500 text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out"
            :class="{ 'translate-x-0 opacity-100': showToast, 'translate-x-full opacity-0': !showToast }"
        >
            <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405C18.37 15.37 18 14.698 18 14V11a6 6 0 1 0-12 0v3c0 .698-.37 1.37-.595 1.595L4 17h5m6 0v1a3 3 0 1 1-6 0v-1m6 0H9" />
                </svg>
                <span class="font-medium">{{ toastMessage }}</span>
            </div>
        </div>

        <!-- Dynamic Page Title -->
        <div class="bg-white px-3 sm:px-4 py-2 border-b border-emerald-300">
            <div class="flex items-center justify-between">
                <!-- Toggle Button on the Left -->
                <button
                    @click="toggleSidebar"
                    class="p-2 text-emerald-700 hover:text-emerald-800 hover:bg-emerald-50 rounded-lg transition-all duration-200 flex items-center justify-center"
                    aria-label="Toggle Sidebar"
                >
                    <Menu class="h-5 w-5" />
                </button>

                <div class="flex-1 ml-4">
                    <h1 class="text-lg font-bold text-emerald-700">{{ pageTitle.title }}</h1>
                    <p class="text-emerald-700 text-xs">{{ pageTitle.subtitle }}</p>
                </div>
                <div class="flex items-center gap-3 mr-2 lg:mr-0">
                    <div class="text-emerald-700 text-xs">
                        {{ new Date().toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) }}
                    </div>
                    <!-- Notification Bell (Admin Only) - Inside title section -->
                    <div v-if="userRole === 'admin'" class="relative">
                        <button
                            @click="toggleNotifications"
                            class="relative p-2 text-emerald-700 hover:text-emerald-800 hover:bg-emerald-50 rounded-full transition-all duration-200 flex items-center justify-center"
                            :class="{ 'animate-pulse': hasNewNotifications }"
                            aria-label="Notifications"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405C18.37 15.37 18 14.698 18 14V11a6 6 0 1 0-12 0v3c0 .698-.37 1.37-.595 1.595L4 17h5m6 0v1a3 3 0 1 1-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Notification Badge -->
                            <span
                                v-if="unreadCount > 0"
                                class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-600 rounded-full border-2 border-white shadow"
                            >
                                {{ unreadCount > 99 ? '99+' : unreadCount }}
                            </span>
                            <!-- Real-time indicator -->
                            <div v-if="isPolling" class="absolute -bottom-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </button>

                        <!-- Desktop Notification Panel -->
                        <div
                            v-if="showNotifications"
                            class="hidden lg:block absolute top-full right-0 mt-2 w-80 xl:w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-[9999] max-h-[calc(100vh-3rem)] xl:max-h-[calc(100vh-3rem)] flex flex-col"
                        >
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                        <div class="flex items-center gap-2">
                            <button
                                v-if="unreadCount > 0"
                                @click="handleMarkAllAsRead"
                                class="text-xs text-emerald-600 hover:text-emerald-700 font-medium transition-colors duration-200"
                            >
                                Mark all read
                            </button>
                            <button
                                @click="closeNotifications"
                                class="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                            >
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Notification List -->
                    <div class="max-h-[calc(100vh-8rem)] xl:max-h-96 overflow-y-auto">
                        <div
                            v-for="notification in notifications"
                            :key="notification.id"
                            @click="handleMarkAsRead(notification.id)"
                            class="p-3 sm:p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors duration-200 active:bg-gray-100"
                            :class="{ 
                                'bg-emerald-50': notification.unread,
                                'border-l-4': notification.data?.priority,
                                'border-l-red-400': notification.data?.priority === 'urgent',
                                'border-l-orange-400': notification.data?.priority === 'high',
                                'border-l-blue-400': notification.data?.priority === 'normal',
                                'border-l-green-400': notification.data?.priority === 'low'
                            }"
                        >
                            <div class="flex items-start gap-3">
                                <div class="text-lg flex-shrink-0">{{ getNotificationIcon(notification.type) }}</div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between gap-2">
                                        <h4 class="text-sm font-medium text-gray-900 leading-tight">{{ notification.title }}</h4>
                                        <div class="flex items-center gap-2">
                                            <span v-if="notification.unread" class="w-2 h-2 bg-emerald-600 rounded-full flex-shrink-0 mt-1"></span>
                                            <span
                                                v-if="notification.data?.priority"
                                                class="px-2 py-1 text-xs font-medium rounded-full"
                                                :class="{
                                                    'bg-red-100 text-red-800': notification.data.priority === 'urgent',
                                                    'bg-orange-100 text-orange-800': notification.data.priority === 'high',
                                                    'bg-blue-100 text-blue-800': notification.data.priority === 'normal',
                                                    'bg-green-100 text-green-800': notification.data.priority === 'low'
                                                }"
                                            >
                                                {{ notification.data.priority }}
                                            </span>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1 leading-relaxed">{{ notification.message }}</p>
                                    <div class="flex items-center gap-2 mt-2">
                                        <p class="text-xs text-gray-400">{{ notification.time }}</p>
                                        <span
                                            v-if="notification.data?.notice_type"
                                            class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full"
                                        >
                                            {{ notification.data.notice_type }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Notifications -->
                    <div v-if="notifications.length === 0" class="p-8 text-center text-gray-500">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405C18.37 15.37 18 14.698 18 14V11a6 6 0 1 0-12 0v3c0 .698-.37 1.37-.595 1.595L4 17h5m6 0v1a3 3 0 1 1-6 0v-1m6 0H9"></path>
                        </svg>
                        <p class="text-sm">No notifications yet</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Modal for mobile -->
        <div v-if="showNotifications" class="fixed inset-0 z-50 flex items-center justify-center bg-black/40 lg:hidden">
            <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md h-[700px] flex flex-col mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">All Notifications</h3>
                    <button @click="closeNotifications" class="text-gray-400 hover:text-gray-700">&times;</button>
                </div>
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-600">{{ notifications.length }} notifications</span>
                    <button v-if="unreadCount > 0" @click="handleMarkAllAsRead" class="text-xs text-emerald-600 hover:text-emerald-800">Mark all as read</button>
                </div>
                <div class="overflow-y-auto flex-1">
                    <div
                        v-for="notification in notifications"
                        :key="notification.id"
                        @click="handleMarkAsRead(notification.id)"
                        class="p-3 hover:bg-emerald-50 cursor-pointer border-b border-gray-100"
                        :class="{ 'bg-emerald-50': notification.unread }"
                    >
                        <div class="flex items-start gap-3">
                            <span class="text-xl">{{ getNotificationIcon(notification.type) }}</span>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-semibold text-emerald-900 flex items-center gap-2">
                                    {{ notification.title }}
                                    <span v-if="notification.unread" class="w-2 h-2 bg-emerald-600 rounded-full"></span>
                                </div>
                                <div class="text-sm text-gray-700 mt-1">{{ notification.message }}</div>
                                <div class="text-xs text-gray-400 mt-1">{{ notification.time }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-right">
                    <button @click="closeNotifications" class="px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700">Close</button>
                </div>
            </div>
        </div>
                        </div>
                    </div>
                </div>
</template>
