<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-1 sm:p-4">
    <div class="bg-white shadow-lg w-full max-w-7xl mx-auto overflow-hidden border border-gray-400 rounded-xl max-h-[98vh] sm:max-h-[95vh] flex flex-col">
            <!-- Title -->
      <div class="text-center py-2 sm:py-4 relative px-2 sm:px-4 flex-shrink-0">
        <h1 class="text-xs sm:text-xl font-bold text-gray-700 pr-8 sm:pr-0 leading-tight">BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY {{ currentYear }}</h1>
 <button @click="$emit('close')" class="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 px-2 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-md shadow-sm hover:shadow-md hover:from-red-600 hover:to-red-700 transition-all duration-200 text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-1.5">
    <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
    <span class="hidden sm:inline">Close</span>
  </button>
</div>

      <!-- Status Badges -->
      <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 px-2 sm:px-4 pt-2 pb-2 flex-shrink-0">
        <div class="flex items-center gap-2 flex-wrap">
          <span v-if="report.status === 'Draft'" class="bg-gray-200 border border-gray-400 text-xs px-2 py-1 font-medium rounded">Pending</span>
          <span v-if="report.status === 'pending'" class="bg-yellow-200 border border-yellow-300 text-xs px-2 py-1 font-medium rounded">Pending</span>
          <span v-if="report.status === 'approved'" class="bg-green-200 border border-green-300 text-xs px-2 py-1 font-medium rounded">Approved</span>
          <span v-if="report.status === 'rejected'" class="bg-pink-200 border border-pink-300 text-xs px-2 py-1 font-medium rounded">Needs Revision</span>
          <span class="text-xs text-gray-500 hidden lg:inline">{{ getStatusMessage(report.status) }}</span>
        </div>
        <div class="flex flex-wrap gap-1 sm:gap-2 w-full sm:w-auto">
          <button @click="exportToExcel" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer flex-1 sm:flex-none justify-center rounded hover:bg-gray-50">
            <span class="text-xs">📈</span>
            <span class="hidden sm:inline">Export to Excel</span>
            <span class="sm:hidden">Export</span>
          </button>
          <button @click="openComments" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer flex-1 sm:flex-none justify-center rounded hover:bg-gray-50">
            <span class="text-xs">💬</span>
            <span class="hidden sm:inline">Comments</span>
            <span class="sm:hidden">Comments</span>
          </button>
          <button @click="printpreview" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer flex-1 sm:flex-none justify-center rounded hover:bg-gray-50">
            <span class="text-xs">🖨️</span>
            <span class="hidden sm:inline">Print Preview</span>
            <span class="sm:hidden">Print</span>
          </button>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 overflow-auto">
        <!-- Mobile Card View -->
        <div class="block md:hidden p-2 space-y-4">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
            <div class="space-y-3">
              <div class="border-b pb-2">
                <h3 class="font-semibold text-sm text-gray-800 mb-1">{{ report.focused || 'CLIENT-FOCUSED' }}</h3>
                <p class="text-xs text-gray-600">{{ report.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue' }}</p>
              </div>

              <div class="grid grid-cols-1 gap-3 text-xs">
                <div>
                  <span class="font-semibold text-gray-700">Gender Issues/GAD Mandate:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.title || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Objective:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.gadObjective || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">LGU Program/Project:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.lguPpa || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Activity:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.gadActivity || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Performance Indicator:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.performanceIndicator || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Actual Results:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.actualResults || '-' }}</p>
                </div>

                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <span class="font-semibold text-gray-700">Approved Budget:</span>
                    <p class="mt-1 text-gray-600 font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</p>
                  </div>
                  <div>
                    <span class="font-semibold text-gray-700">Actual Cost:</span>
                    <p class="mt-1 text-gray-600">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</p>
                  </div>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Remarks:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.remarks || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Supporting Documents:</span>
                  <div class="mt-1">
                    <span v-if="attachmentFile" class="text-red-500 text-xs">{{ truncateFilename(attachmentFile.name, 20) }}</span>
                    <div v-else-if="report.attachment_path" class="flex flex-col gap-1">
                      <div v-for="(path, index) in getAttachmentPaths(report.attachment_path)" :key="index">
                        <span class="text-blue-500 cursor-pointer hover:underline text-xs" @click="downloadAttachment(path)" :title="getAttachmentName(path)">
                          {{ truncateFilename(getAttachmentName(path), 20) }}
                        </span>
                      </div>
                    </div>
                    <span v-else class="text-red-500 text-xs">No file(s) attached</span>
                  </div>
                </div>
              </div>

              <!-- Mobile Grand Total -->
              <div class="bg-emerald-700 text-white p-3 rounded mt-4">
                <div class="flex justify-between items-center">
                  <span class="font-bold text-sm">GRAND TOTAL (A+B+C)</span>
                </div>
                <div class="grid grid-cols-2 gap-2 mt-2 text-xs">
                  <div>
                    <span>Approved: </span>
                    <span class="font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</span>
                  </div>
                  <div>
                    <span>Actual: </span>
                    <span class="font-bold">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Desktop Table View -->
        <div class="hidden md:block p-4">
          <div class="overflow-x-auto">
            <table class="w-full border border-gray-400 text-xs min-w-[1200px]">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Gender Issues or GAD Mandate</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">GAD Objective</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Relevant LGU Program or Project</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">GAD Activity</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Performance Indicator and Target</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Actual Results</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Approved GAD Budget</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Actual Cost or Expenditure</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Variance or Remarks</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Supporting Documents</th>
                </tr>
                <tr>
                  <th colspan="10" class="border border-gray-400 font-bold text-left px-2 py-2 bg-gray-100">{{ report.focused || 'CLIENT-FOCUSED' }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td colspan="10" class="border border-gray-400 bg-white text-left px-2 py-2">{{ report.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue' }}</td>
                </tr>
                <tr>
                  <td class="border border-gray-400 px-2 py-2">{{ report.title || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.gadObjective || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.lguPpa || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.gadActivity || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.performanceIndicator || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.actualResults || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.remarks || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">
                    <div class="flex items-center space-x-1.5 text-sm text-gray-600 hover:text-gray-800">
                      <!-- No files text -->
                      <div class="text-sm pl-6 text-left">
                        <span v-if="attachmentFile" class="text-red-500">{{ truncateFilename(attachmentFile.name, 20) }}</span>
                        <div v-else-if="report.attachment_path" class="flex flex-col gap-1">
                          <div v-for="(path, index) in getAttachmentPaths(report.attachment_path)" :key="index">
                            <span class="text-blue-500 cursor-pointer hover:underline" @click="downloadAttachment(path)" :title="getAttachmentName(path)">
                              {{ truncateFilename(getAttachmentName(path), 20) }}
                            </span>
                          </div>
                        </div>
                        <span v-else class="text-red-500">No file(s) attached</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr class="bg-emerald-700 text-white">
                  <td colspan="6" class="border border-gray-400 px-2 py-2 font-bold text-left">GRAND TOTAL (A+B+C)</td>
                  <td class="border border-gray-400 px-2 py-2 font-bold text-right">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2"></td>
                  <td class="border border-gray-400 px-2 py-2"></td>
                </tr>
                <tr>
                  <td colspan="3" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Prepared by:</div>
                    <div class="font-bold text-base mb-0">{{ report.user_name || 'UNKNOWN USER' }}</div>
                    <div class="text-xs mt-1">Barangay Gad Focal</div>
                  </td>
                  <td colspan="3" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Approved by:</div>
                    <div class="font-bold text-base mb-0">
                      <br>
                      <span class="text-gray-400"></span>
                    </div>
                    <div class="text-xs mt-1">Punong Barangay</div>
                  </td>
                  <td colspan="4" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Date:</div>
                    <div class="font-bold text-base mb-0">
                      <br>
                      <span class="text-gray-400"></span>
                    </div>
                    <div class="text-xs mt-1">DD/MM/YEAR</div>
                  </td>
                </tr>
                <tr>
                  <td colspan="6" class="border-b border-t border-l border-gray-400 px-2 py-2">
                    <div class="font-semibold mb-1">Verified and Endorsed by:</div>
                    <div class="font-bold text-base mb-0">
                      <br>
                      <span v-if="report.status === 'approved' && report.admin_approver" class="text-black">{{ report.admin_approver }}</span>
                      <span v-else class="text-gray-400"></span>
                    </div>
                    <div class="text-xs mt-1">Chairperson, GFPS TWG/secretary</div>
                  </td>
                  <td colspan="4" class="border-b border-t border-r border-gray-400 px-2 py-2">
                    <div class="font-semibold mb-1">Date Verified:</div>
                    <div class="font-bold text-base mb-0">
                      <br>
                      <span v-if="report.status === 'approved' && report.date_approved" class="text-black">{{ formatApprovalDate(report.date_approved) }}</span>
                      <span v-else class="text-gray-400"></span>
                    </div>
                    <div class="text-xs mt-1">DD/MM/YEAR</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Signature Section -->
        <div class="block md:hidden p-2">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm space-y-4">
            <div class="grid grid-cols-1 gap-4 text-xs">
              <div class="border-b pb-3">
                <span class="font-semibold text-gray-700">Prepared by:</span>
                <p class="mt-1 font-bold text-sm">{{ report.user_name || 'UNKNOWN USER' }}</p>
                <p class="text-xs text-gray-600">Barangay Gad Focal</p>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="font-semibold text-gray-700">Approved by:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Punong Barangay</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 border-t pt-3">
                <div>
                  <span class="font-semibold text-gray-700">Verified and Endorsed by:</span>
                  <p v-if="report.status === 'approved' && report.admin_approver" class="mt-1 font-bold text-sm text-black">{{ report.admin_approver }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Chairperson, GFPS TWG/secretary</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date Verified:</span>
                  <p v-if="report.status === 'approved' && report.date_approved" class="mt-1 font-bold text-sm text-black">{{ formatApprovalDate(report.date_approved) }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';
import * as XLSX from 'xlsx';
const props = defineProps<{ show: boolean, report: any }>();
const emit = defineEmits(['close']);
const attachmentFile = ref(null);

// Debug the report data
console.log('Barangayreportmodal report data:', props.report);
console.log('Report admin_approver:', props.report?.admin_approver);
console.log('Report date_approved:', props.report?.date_approved);
console.log('Report status:', props.report?.status);

// Use the fiscal year from the report or default to current year
const currentYear = computed(() => props.report?.fiscalYear || new Date().getFullYear());

function formatLongDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function formatApprovalDate(dateString: string) {
  if (!dateString) return 'DD/MM/YEAR';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

function getStatusMessage(status: string) {
  if (!status) return '';

  const messages = {
    'Draft': 'The report is a draft and is not yet submitted for review',
    'pending': 'The report is awaiting review',
    'approved': 'The report has been approved',
    'rejected': 'The revision has been requested by the reviewer'
  };

  return messages[status.toLowerCase()] || '';
}

function getAttachmentPaths(attachmentPath: string): string[] {
  if (!attachmentPath) return [];

  // Handle JSON array of paths
  if (attachmentPath.startsWith('[')) {
    try {
      return JSON.parse(attachmentPath);
    } catch (e) {
      console.error('Error parsing attachment paths:', e);
      return [attachmentPath];
    }
  }

  // Handle single path
  return [attachmentPath];
}

function getAttachmentName(path: string): string {
  if (!path) return '';
  return path.split('/').pop();
}

function downloadAttachment(path: string) {
  if (!path) return;
  window.open('/storage/' + path, '_blank');
}

function truncateFilename(filename: string, maxLength: number): string {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;

  const extension = filename.split('.').pop();
  const nameWithoutExtension = filename.substring(0, filename.length - extension.length - 1);

  // Calculate how many characters to keep from the name
  const charsToKeep = maxLength - extension.length - 3; // 3 for "..." and "."

  if (charsToKeep <= 0) return filename; // If maxLength is too small, return the original

  return nameWithoutExtension.substring(0, charsToKeep) + '...' + '.' + extension;
}

function exportToExcel() {
  if (!props.report) return;

  // Create structured data that matches the print preview format
  const structuredData = [
    // Header row
    ['BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY ' + currentYear.value],
    [''], // Empty row for spacing

    // Main table headers
    ['Gender Issues or GAD Mandate', 'GAD Objective', 'Relevant LGU Program or Project', 'GAD Activity', 'Performance Indicator and Target', 'Actual Results', 'Approved GAD Budget', 'Actual Cost or Expenditure', 'Variance or Remarks', 'Supporting Documents'],

    // Focused type row
    [props.report?.focused || 'CLIENT-FOCUSED', '', '', '', '', '', '', '', '', ''],

    // Issue/Mandate type row
    [props.report?.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue', '', '', '', '', '', '', '', '', ''],

    // Data row
    [
      props.report?.title || '-',
      props.report?.gadObjective || '-',
      props.report?.lguPpa || '-',
      props.report?.gadActivity || '-',
      props.report?.performanceIndicator || '-',
      props.report?.actualResults || '-',
      props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-',
      props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-',
      props.report?.remarks || '-',
      getAttachmentDisplayText()
    ],

    // Grand Total row
    [
      'GRAND TOTAL (A+B+C)', '', '', '', '', '',
      props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-',
      props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-',
      '', ''
    ],

    [''], // Empty row for spacing
    [''], // Empty row for spacing

    // Signature section
    ['SIGNATURES'],
    ['Prepared by:', '', 'Approved by:', '', '', 'Date:'],
    [props.report?.user_name || 'UNKNOWN USER', '', '_________________', '', '', '_________________'],
    ['Barangay Gad Focal', '', 'Punong Barangay', '', '', 'DD/MM/YEAR'],
    [''], // Empty row
    ['Verified and Endorsed by:', '', '', '', 'Date Verified:'],
    [props.report?.status === 'approved' && props.report?.admin_approver ? props.report.admin_approver : '_________________', '', '', '', props.report?.status === 'approved' && props.report?.date_approved ? formatApprovalDate(props.report.date_approved) : '_________________'],
    ['Chairperson, GFPS TWG/secretary', '', '', '', 'DD/MM/YEAR']
  ];

  // Create worksheet from the structured data
  const ws = XLSX.utils.aoa_to_sheet(structuredData);

  // Set column widths for better formatting
  ws['!cols'] = [
    { wch: 25 }, // Gender Issues or GAD Mandate
    { wch: 20 }, // GAD Objective
    { wch: 25 }, // Relevant LGU Program or Project
    { wch: 20 }, // GAD Activity
    { wch: 25 }, // Performance Indicator and Target
    { wch: 20 }, // Actual Results
    { wch: 18 }, // Approved GAD Budget
    { wch: 18 }, // Actual Cost or Expenditure
    { wch: 20 }, // Variance or Remarks
    { wch: 25 }  // Supporting Documents
  ];

  // Create workbook and add the worksheet
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'GAD Report FY ' + currentYear.value);

  // Generate filename with current year
  const filename = `Barangay_GAD_Accomplishment_Report_FY${currentYear.value}.xlsx`;
  XLSX.writeFile(wb, filename);
}

function openComments() {
  // Comments functionality can be implemented here
  console.log('Comments clicked');
}

function printpreview() {
  // Create a new window for printing only the modal content
  const printWindow = window.open('', '_blank', 'width=800,height=600');

  if (!printWindow) {
    alert('Please allow popups to enable printing');
    return;
  }

  // Get the print content
  const printContent = generatePrintContent();

  // Write the complete HTML document to the print window
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Barangay GAD Accomplishment Report FY ${currentYear.value}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          font-size: 12px;
          line-height: 1.4;
        }

        .header {
          text-align: center;
          margin-bottom: 20px;
          font-weight: bold;
          font-size: 16px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          page-break-inside: avoid;
        }

        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
          vertical-align: top;
          font-size: 10px;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .text-center {
          text-align: center;
        }

        .text-right {
          text-align: right;
        }

        .font-bold {
          font-weight: bold;
        }

        .bg-emerald-700 {
          background-color: #047857;
          color: white;
        }

        .signature-section {
          margin-top: 20px;
        }

        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `);

  printWindow.document.close();

  // Wait for content to load, then print
  printWindow.onload = function() {
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
}

function generatePrintContent() {
  const currentYearValue = currentYear.value;
  const userName = props.report?.user_name || 'UNKNOWN USER';

  return `
    <div class="header">
      BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY ${currentYearValue}
    </div>

    <!-- Main Report Table -->
    <table>
      <thead>
        <tr>
          <th>Gender Issues or GAD Mandate</th>
          <th>GAD Objective</th>
          <th>Relevant LGU Program or Project</th>
          <th>GAD Activity</th>
          <th>Performance Indicator and Target</th>
          <th>Actual Results</th>
          <th>Approved GAD Budget</th>
          <th>Actual Cost or Expenditure</th>
          <th>Variance or Remarks</th>
          <th>Supporting Documents</th>
        </tr>
        <tr>
          <th colspan="10" class="font-bold">${props.report?.focused || 'CLIENT-FOCUSED'}</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="10" class="font-bold">${props.report?.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue'}</td>
        </tr>
        <tr>
          <td>${props.report?.title || '-'}</td>
          <td>${props.report?.gadObjective || '-'}</td>
          <td>${props.report?.lguPpa || '-'}</td>
          <td>${props.report?.gadActivity || '-'}</td>
          <td>${props.report?.performanceIndicator || '-'}</td>
          <td>${props.report?.actualResults || '-'}</td>
          <td class="text-right font-bold">${props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-'}</td>
          <td class="text-right">${props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-'}</td>
          <td>${props.report?.remarks || '-'}</td>
          <td>${getAttachmentDisplayText()}</td>
        </tr>

        <!-- Grand Total row -->
        <tr class="font-bold">
          <td colspan="6" class="font-bold">GRAND TOTAL (A+B+C)</td>
          <td class="text-right font-bold">${props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-'}</td>
          <td class="text-right">${props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-'}</td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>

    <!-- Signature Section -->
    <table class="signature-section">
      <tr>
        <td style="width: 30%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Prepared by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${userName}</div>
          <div style="font-size: 10px;">Barangay Gad Focal</div>
        </td>
        <td style="width: 30%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Approved by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">Punong Barangay</div>
        </td>
        <td style="width: 40%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
      <tr>
        <td colspan="2">
          <div style="font-weight: bold; margin-bottom: 5px;">Verified and Endorsed by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.report?.status === 'approved' && props.report?.admin_approver ? props.report.admin_approver : '_________________'}</div>
          <div style="font-size: 10px;">Chairperson, GFPS TWG/secretary</div>
        </td>
        <td>
          <div style="font-weight: bold; margin-bottom: 5px;">Date Verified:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.report?.status === 'approved' && props.report?.date_approved ? formatApprovalDate(props.report.date_approved) : '_________________'}</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
    </table>
  `;
}

function getAttachmentDisplayText(): string {
  if (attachmentFile.value) {
    return truncateFilename(attachmentFile.value.name, 30);
  }

  if (props.report?.attachment_path) {
    const paths = getAttachmentPaths(props.report.attachment_path);
    return paths.map(path => getAttachmentName(path)).join(', ');
  }

  return 'No file(s) attached';
}
</script>

<style scoped>
.bg-pink-200 { background-color: #e9b7e7 !important; }
.bg-yellow-200 { background-color: #fff9c4 !important; }
.bg-blue-700 { background-color: #1976d2 !important; }
</style>











