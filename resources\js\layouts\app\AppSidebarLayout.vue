<script setup lang="ts">
import AppContent from '@/components/AppContent.vue';
import AppShell from '@/components/AppShell.vue';
import AppSidebar from '@/components/AppSidebar.vue';
import AppSidebarHeader from '@/components/AppSidebarHeader.vue';
import AppSidebarHeaderBarangay from '@/components/AppSidebarHeaderBarangay.vue';
import type { BreadcrumbItemType } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { onMounted } from 'vue';
import { route } from 'ziggy-js';

const page = usePage<{ auth: { user: { role: string } } }>();
const userRole = page.props.auth.user?.role;

onMounted(() => {
  if (!page.props.auth || !page.props.auth.user) {
    window.location.replace(route('login'));
  }
});

interface Props {
    breadcrumbs?: BreadcrumbItemType[];
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});
</script>

<template>
    <AppShell variant="sidebar">
        <AppContent variant="sidebar">
            <slot />
        </AppContent>
    </AppShell>
</template>
