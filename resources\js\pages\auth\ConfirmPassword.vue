

<template>
    <Head title="Confirm Password" />

    <!-- Navbar -->
    <nav class="bg-white shadow-sm fixed w-full top-0 z-50">
        <div class="w-full px-4 sm:px-0">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center w-full h-auto sm:h-16">
                <!-- Logo and Title -->
                <div class="flex flex-row items-center justify-between w-full sm:mb-0 sm:w-auto mb-2">
                    <div class="flex items-center">
                        <img src="/images/panabo-city-logo-1.png" alt="Panabo City Logo" class="h-10 w-auto" />
                        <span class="ml-2 text-lg font-semibold text-green-900 whitespace-normal sm:whitespace-nowrap">
                            GAD Planning and Budgeting Management System
                        </span>
                    </div>
                    <!-- Hamburger for mobile -->
                    <button @click="showMenu = !showMenu" class="sm:hidden ml-auto p-2 rounded focus:outline-none" aria-label="Open menu">
                        <svg class="w-7 h-7 text-green-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
                <!-- Navigation Links -->
                <!-- Modal Dropdown for Mobile -->
                <transition name="fade">
                  <div v-if="showMenu">
                    <!-- Click outside to close (z-40) -->
                    <div @click="showMenu = false" class="fixed inset-0 z-40 sm:hidden"></div>
                    <!-- Modal menu (z-50) -->
                    <div class="fixed top-12 right-4 z-50 sm:hidden">
                      <div class="bg-white rounded-xl shadow-xl border border-gray-200 flex flex-col min-w-[160px] p-3">
                        <button @click="showMenu = false" class="self-end mb-2 text-gray-400 hover:text-green-900 focus:outline-none" aria-label="Close menu">
                          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                        <Link
                          :href="route('login')"
                          class="text-green-900 hover:text-green-600 px-4 py-2 rounded-md text-sm font-medium cursor-pointer text-left"
                          @click="showMenu = false"
                        >
                          Log In
                        </Link>
                        <Link
                          :href="route('register')"
                          class="text-green-900 hover:text-green-600 px-4 py-2 rounded-md text-sm font-medium cursor-pointer text-left mt-1"
                          @click="showMenu = false"
                        >
                          Sign Up
                        </Link>
                      </div>
                    </div>
                  </div>
                </transition>
                <!-- Desktop navigation links -->
                <div class="hidden sm:flex sm:flex-row sm:w-auto sm:items-center sm:justify-end sm:gap-2 sm:mt-0">
                    <Link
                        :href="route('login')"
                        class="text-green-900 hover:text-green-600 px-2 py-2 rounded-md text-sm font-medium cursor-pointer w-full sm:w-auto text-left sm:text-center"
                    >
                        Log In
                    </Link>
                    <Link
                        :href="route('register')"
                        class="text-green-900 hover:text-green-600 px-2 py-2 rounded-md text-sm font-medium cursor-pointer w-full sm:w-auto text-left sm:text-center"
                    >
                        Sign Up
                    </Link>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="min-h-screen bg-green-100 pt-16">
        <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="bg-white shadow-xl rounded-2xl p-8">
                <div class="text-center mb-8">
                    <h4 class="text-2xl font-bold text-green-800">Confirm Password</h4>
                    <p class="mt-2 text-sm text-gray-600">Please confirm your password before continuing</p>
                </div>

                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Password Input -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-black">Password</label>
                        <div class="mt-1">
                            <input
                                id="password"
                                type="password"
                                class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-green-800 focus:outline-none focus:ring-green-800 sm:text-sm"
                                v-model="form.password"
                                required
                                autocomplete="current-password"
                            />
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button
                            type="submit"
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-800 hover:bg-green-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-800 transition duration-150 ease-in-out transform hover:scale-[1.02]"
                            :disabled="form.processing"
                        >
                            <svg
                                v-if="form.processing"
                                class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                            {{ form.processing ? 'Confirming...' : 'Confirm Password' }}
                        </button>
                    </div>

                    <!-- Forgot Password Link -->
                    <div class="text-center text-sm text-gray-600">
                        Forgot your password?
                        <Link
                            :href="route('password.request')"
                            class="text-green-800 hover:text-green-900 font-medium ml-1 transition duration-150 ease-in-out"
                        >
                            Reset it here
                        </Link>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
const showMenu = ref(false);
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputError from '@/components/InputError.vue';

const form = useForm({
    password: '',
});

const submit = () => {
    form.post(route('password.confirm'), {
        onFinish: () => form.reset(),
    });
};
</script>
<style scoped>
/* Custom styles */
.focus\:ring-2:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.transition {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
