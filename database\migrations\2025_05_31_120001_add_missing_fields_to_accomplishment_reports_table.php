<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Add activity column if it doesn't exist
            if (!Schema::hasColumn('accomplishment_reports', 'activity')) {
                $table->json('activity')->nullable()->after('attachment_path');
            }
            
            // Add ppaSi column if it doesn't exist
            if (!Schema::hasColumn('accomplishment_reports', 'ppaSi')) {
                $table->json('ppaSi')->nullable()->after('activity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Drop the columns if they exist
            if (Schema::hasColumn('accomplishment_reports', 'ppaSi')) {
                $table->dropColumn('ppaSi');
            }
            
            if (Schema::hasColumn('accomplishment_reports', 'activity')) {
                $table->dropColumn('activity');
            }
        });
    }
};
