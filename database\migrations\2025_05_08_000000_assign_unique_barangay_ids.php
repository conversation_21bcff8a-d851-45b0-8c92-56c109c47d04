<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Get all users
        $users = DB::table('users')->get();
        
        // 2. For each user, create a unique barangay record
        foreach ($users as $user) {
            // Create a unique barangay for this user
            $barangayId = DB::table('barangays')->insertGetId([
                'name' => 'Barangay for User ' . $user->id,
                'region' => $user->region ?? 'Region for User ' . $user->id,
                'province' => $user->province ?? 'Province for User ' . $user->id,
                'city' => $user->city ?? 'City for User ' . $user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            // Update the user with the new unique barangay_id
            DB::table('users')
                ->where('id', $user->id)
                ->update(['barangay_id' => $barangayId]);
            
            // Log the assignment
            \Log::info("Assigned unique barangay ID {$barangayId} to user {$user->id}");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a data migration, so we don't provide a way to reverse it
        // If needed, you could reset all users to a default barangay_id
    }
};