<script setup lang="ts">
import AppearanceTabs from '@/components/AppearanceTabs.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { settingsActiveTab } from '@/settingsTabState';
import { Palette, Monitor, Smartphone, CheckCircle } from 'lucide-vue-next';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Settings',
        href: '/settings',
    },
    {
        title: 'Appearance',
        href: '/settings/appearance',
    },
];

// Set active tab
settingsActiveTab.value = 'appearance';
</script>

<template>
    <div class="p-8 space-y-8">
            <!-- Appearance Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-emerald-100 dark:bg-emerald-900/30 rounded-full mb-4">
                    <Palette class="h-8 w-8 text-emerald-600" />
                </div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Appearance Settings</h1>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Customize the look and feel of your application to match your preferences.
                </p>
            </div>

            <!-- Appearance Settings -->
            <Card class="max-w-4xl mx-auto border-0 shadow-lg">
                <CardHeader>
                    <CardTitle class="flex items-center gap-2 text-xl">
                        <Palette class="h-5 w-5 text-emerald-600" />
                        Theme & Display
                    </CardTitle>
                    <CardDescription>
                        Choose your preferred theme and display settings
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <AppearanceTabs />
                </CardContent>
            </Card>

            <!-- Device Preview -->
            <Card class="max-w-4xl mx-auto border-0 shadow-lg">
                <CardHeader>
                    <CardTitle class="flex items-center gap-2 text-xl">
                        <Monitor class="h-5 w-5 text-emerald-600" />
                        Preview
                    </CardTitle>
                    <CardDescription>
                        See how your settings will look across different devices
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Desktop Preview -->
                        <div class="text-center">
                            <div class="relative inline-block mb-4">
                                <div class="w-32 h-20 bg-gray-200 dark:bg-gray-700 rounded-t-lg border-2 border-gray-300 dark:border-gray-600"></div>
                                <div class="w-32 h-16 bg-white dark:bg-gray-800 rounded-b-lg border-2 border-t-0 border-gray-300 dark:border-gray-600 shadow-lg">
                                    <div class="flex items-center justify-center h-full">
                                        <div class="w-8 h-1 bg-emerald-500 rounded"></div>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Desktop</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Full layout with sidebar</p>
                        </div>

                        <!-- Tablet Preview -->
                        <div class="text-center">
                            <div class="relative inline-block mb-4">
                                <div class="w-24 h-16 bg-gray-200 dark:bg-gray-700 rounded-t-lg border-2 border-gray-300 dark:border-gray-600"></div>
                                <div class="w-24 h-12 bg-white dark:bg-gray-800 rounded-b-lg border-2 border-t-0 border-gray-300 dark:border-gray-600 shadow-lg">
                                    <div class="flex items-center justify-center h-full">
                                        <div class="w-6 h-1 bg-emerald-500 rounded"></div>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Tablet</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Responsive layout</p>
                        </div>

                        <!-- Mobile Preview -->
                        <div class="text-center">
                            <div class="relative inline-block mb-4">
                                <div class="w-16 h-12 bg-gray-200 dark:bg-gray-700 rounded-t-lg border-2 border-gray-300 dark:border-gray-600"></div>
                                <div class="w-16 h-10 bg-white dark:bg-gray-800 rounded-b-lg border-2 border-t-0 border-gray-300 dark:border-gray-600 shadow-lg">
                                    <div class="flex items-center justify-center h-full">
                                        <div class="w-4 h-1 bg-emerald-500 rounded"></div>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Mobile</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Compact layout</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Settings Summary -->
            <Card class="max-w-4xl mx-auto border-0 shadow-lg bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/50 dark:to-teal-950/50">
                <CardContent class="p-6">
                    <div class="flex items-center gap-3 mb-4">
                        <CheckCircle class="h-6 w-6 text-emerald-600" />
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Settings Applied</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">
                        Your appearance settings have been saved and will be applied across all your devices. 
                        Changes may take a moment to reflect in your browser.
                    </p>
                            </CardContent>
        </Card>
    </div>
</template>
