<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // Add approval_date column if it doesn't exist
            if (!Schema::hasColumn('budget_plans', 'approval_date')) {
                $table->timestamp('approval_date')->nullable()->after('approver_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // Drop the column if it exists
            if (Schema::hasColumn('budget_plans', 'approval_date')) {
                $table->dropColumn('approval_date');
            }
        });
    }
};
