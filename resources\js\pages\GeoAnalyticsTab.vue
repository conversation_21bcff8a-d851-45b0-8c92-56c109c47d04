<template>
  <div class="p-4 sm:p-6">
          <div class="mb-4 flex items-center justify-between">
        <div>
          <h2 class="text-xl font-bold text-emerald-800">Geo Analytics</h2>
          <p class="text-sm text-emerald-600">Monitoring barangay progress across Panabo City</p>
        </div>
        <div class="flex items-center gap-3">
          <button 
            @click="toggleHeatmap" 
            :class="[
              'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2',
              showHeatmap 
                ? 'bg-emerald-500 text-white hover:bg-emerald-600' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
            {{ showHeatmap ? 'Hide Heatmap' : 'Show Heatmap' }}
          </button>
        </div>
      </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
      <div class="lg:col-span-3">
        <div class="bg-white border border-emerald-200 rounded-xl shadow-sm overflow-hidden">
          <div id="map" class="h-[70vh] w-full"></div>
        </div>
      </div>
      <div class="lg:col-span-1">
        <div class="bg-white border border-emerald-200 rounded-xl shadow-sm p-4 space-y-3">
          <div class="flex items-center justify-between">
            <div class="font-semibold text-emerald-800">Barangay Progress</div>
            <div class="text-xs text-emerald-600">FY {{ new Date().getFullYear() }}</div>
          </div>
          
          <!-- Heatmap Legend -->
          <div v-if="showHeatmap" class="bg-emerald-50 rounded-lg p-3 border border-emerald-200">
            <div class="text-xs font-semibold text-emerald-800 mb-2">User Density Heatmap</div>
            <div class="space-y-1">
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded bg-green-500"></div>
                <span class="text-xs text-emerald-700">Low: Few users or low progress</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded bg-yellow-500"></div>
                <span class="text-xs text-emerald-700">Medium: Moderate user density</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded bg-orange-500"></div>
                <span class="text-xs text-emerald-700">High: Many users, good progress</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-4 h-4 rounded bg-red-500"></div>
                <span class="text-xs text-emerald-700">Very High: High user density + progress</span>
              </div>
            </div>
            <div class="mt-2 text-xs text-emerald-600">
              Intensity = User Count × Progress %
            </div>
          </div>

          <div class="space-y-2 max-h-[65vh] overflow-y-auto pr-1">
            <div v-for="b in barangays" :key="b.name" class="border border-emerald-100 rounded-lg p-2 hover:bg-emerald-50/40 cursor-pointer"
                 @click="focusBarangay(b)">
              <div class="flex items-center justify-between gap-2">
                <div class="text-sm font-medium text-emerald-800 truncate">{{ b.name }}</div>
                <div class="text-xs font-semibold px-2 py-0.5 rounded-full" :class="statusPillClass(b.status)">{{ b.status }}</div>
              </div>
                             <div class="mt-2 space-y-2">
                 <div class="flex justify-between text-xs text-emerald-600">
                   <span>Users: {{ b.userCount }}</span>
                   <span>{{ b.progress }}% complete</span>
                 </div>
                 <div class="w-full h-1.5 bg-emerald-100 rounded-full overflow-hidden">
                   <div class="h-1.5 bg-emerald-500 rounded-full" :style="{ width: b.progress + '%' }"></div>
                 </div>
                 <div class="text-[10px] text-emerald-500">
                   Heatmap intensity: {{ Math.round(b.userCount * (b.progress / 100)) }}
                 </div>
               </div>
            </div>
            <div v-if="barangays.length === 0" class="text-sm text-emerald-600">No data available.</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'
import L, { Map as LeafletMap, Marker as LeafletMarker } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import 'leaflet.heat'

console.log('=== GEO ANALYTICS TAB SCRIPT LOADING ===')

type BarangayInfo = {
  name: string
  lat: number
  lng: number
  progress: number
  status: 'Approved' | 'Pending' | 'Revision' | 'Not Submitted'
  userCount: number
}

const barangays = ref<BarangayInfo[]>([
  { name: 'A.O. Floirendo', lat: 7.3026, lng: 125.6824, progress: 76, status: 'Approved', userCount: 45 },
  { name: 'Cagangohan', lat: 7.3087, lng: 125.6718, progress: 42, status: 'Pending', userCount: 28 },
  { name: 'Gredu', lat: 7.3029, lng: 125.6859, progress: 58, status: 'Revision', userCount: 32 },
  { name: 'San Vicente', lat: 7.3140, lng: 125.6880, progress: 0, status: 'Not Submitted', userCount: 15 },
  { name: 'San Pedro', lat: 7.3075, lng: 125.6780, progress: 90, status: 'Approved', userCount: 52 },
  { name: 'New Pandan', lat: 7.3100, lng: 125.6900, progress: 65, status: 'Pending', userCount: 38 },
  { name: 'Old Pandan', lat: 7.3120, lng: 125.6920, progress: 88, status: 'Approved', userCount: 41 },
  { name: 'Southern Davao', lat: 7.3050, lng: 125.6750, progress: 34, status: 'Revision', userCount: 22 },
  { name: 'Datu Abdul Dadia', lat: 7.3080, lng: 125.6800, progress: 72, status: 'Approved', userCount: 35 },
  { name: 'Consolacion', lat: 7.3060, lng: 125.6780, progress: 55, status: 'Pending', userCount: 29 },
])

const mapRef = ref<LeafletMap | null>(null)
const markersRef = ref<Record<string, LeafletMarker>>({})
const heatmapLayer = ref<any>(null)
const showHeatmap = ref(true)

function statusPillClass(status: BarangayInfo['status']): string {
  switch (status) {
    case 'Approved': return 'bg-emerald-100 text-emerald-700 border border-emerald-200'
    case 'Pending': return 'bg-amber-100 text-amber-700 border border-amber-200'
    case 'Revision': return 'bg-blue-100 text-blue-700 border border-blue-200'
    default: return 'bg-gray-100 text-gray-700 border border-gray-200'
  }
}

function initMap() {
  if (mapRef.value) return
  console.log('Initializing map...')
  
  const centerPanabo: [number, number] = [7.3081, 125.6842]
  const map = L.map('map', { zoomControl: true }).setView(centerPanabo, 13)
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '© OpenStreetMap contributors'
  }).addTo(map)
  mapRef.value = map

  console.log('Map created, adding heatmap...')
  // Add heatmap layer
  addHeatmapLayer()

  const newMarkers: Record<string, LeafletMarker> = {}
  barangays.value.forEach(b => {
    const marker = L.marker([b.lat, b.lng])
      .addTo(map)
      .bindPopup(`<div style="min-width:180px">
        <div style="font-weight:600;color:#065f46">${b.name}</div>
        <div style="font-size:12px;color:#065f46">Status: ${b.status}</div>
        <div style="font-size:12px;color:#065f46">Users: ${b.userCount}</div>
        <div style="margin-top:6px;height:6px;background:#d1fae5;border-radius:9999px;overflow:hidden">
          <div style="height:6px;background:#10b981;width:${b.progress}%"></div>
        </div>
        <div style="font-size:11px;color:#047857;margin-top:4px">${b.progress}% complete</div>
      </div>`)
    newMarkers[b.name] = marker
  })
  markersRef.value = newMarkers
}

function addHeatmapLayer() {
  if (!mapRef.value) return
  
  console.log('Adding heatmap layer...')
  
  // Create heatmap data points based on user density and progress
  const heatmapData = barangays.value.map(b => [
    b.lat, 
    b.lng, 
    (b.userCount * (b.progress / 100)) // Intensity based on user count × progress percentage
  ])
  
  // Also create multiple data points around each barangay to simulate user distribution
  const expandedHeatmapData: [number, number, number][] = []
  barangays.value.forEach(b => {
    const baseIntensity = b.userCount * (b.progress / 100)
    
    // Add main point
    expandedHeatmapData.push([b.lat, b.lng, baseIntensity])
    
    // Add surrounding points to simulate user distribution (more users = more spread)
    const spreadRadius = Math.min(b.userCount / 10, 0.01) // Spread based on user count
    const numPoints = Math.min(b.userCount / 5, 8) // Number of points based on user count
    
    for (let i = 0; i < numPoints; i++) {
      const angle = (i / numPoints) * 2 * Math.PI
      const radius = Math.random() * spreadRadius
      const lat = b.lat + radius * Math.cos(angle)
      const lng = b.lng + radius * Math.sin(angle)
      const intensity = baseIntensity * (0.3 + Math.random() * 0.7) // Vary intensity slightly
      
      expandedHeatmapData.push([lat, lng, intensity])
    }
  })
  
  console.log('Heatmap data points:', expandedHeatmapData)
  
  // Remove existing heatmap if present
  if (heatmapLayer.value) {
    mapRef.value.removeLayer(heatmapLayer.value)
  }
  
  // Create new heatmap layer
  try {
    console.log('Attempting to create heatmap with data:', expandedHeatmapData)
    
    // Check if heatmap plugin is available
    if (typeof (L as any).heatLayer === 'function') {
      console.log('Heatmap plugin found, creating heatmap...')
      heatmapLayer.value = (L as any).heatLayer(expandedHeatmapData, {
        radius: 30,
        blur: 20,
        maxZoom: 15,
        gradient: {
          0.0: '#00ff00',   // Green for low user density/progress
          0.2: '#ffff00',   // Yellow for medium
          0.5: '#ff8000',   // Orange for high
          0.8: '#ff4000',   // Dark orange for very high
          1.0: '#ff0000'    // Red for extremely high user density/progress
        }
      })
      console.log('Heatmap created successfully:', heatmapLayer.value)
    } else {
      console.log('Heatmap plugin not found, using fallback circles')
      throw new Error('Heatmap plugin not available')
    }
  } catch (error) {
    console.error('Error creating heatmap:', error)
    console.log('Creating fallback circle layer...')
    
    // Fallback: create a simple circle layer instead
    heatmapLayer.value = L.layerGroup()
    barangays.value.forEach(b => {
      const intensity = b.progress / 100
      const color = intensity < 0.3 ? '#00ff00' : 
                   intensity < 0.7 ? '#ffff00' : 
                   intensity < 0.9 ? '#ff8000' : '#ff0000'
      
      const circle = L.circle([b.lat, b.lng], {
        radius: 800,
        color: color,
        fillColor: color,
        fillOpacity: intensity * 0.8,
        weight: 2
      }).addTo(heatmapLayer.value)
      
      console.log(`Added circle for ${b.name}:`, circle)
    })
    
    console.log('Fallback layer created:', heatmapLayer.value)
  }
  
  if (showHeatmap.value) {
    heatmapLayer.value.addTo(mapRef.value)
  }
}

function toggleHeatmap() {
  showHeatmap.value = !showHeatmap.value
  if (showHeatmap.value) {
    addHeatmapLayer()
  } else if (heatmapLayer.value && mapRef.value) {
    mapRef.value.removeLayer(heatmapLayer.value)
  }
}

function focusBarangay(b: BarangayInfo) {
  if (!mapRef.value) return
  mapRef.value.setView([b.lat, b.lng], 15, { animate: true })
  const marker = markersRef.value[b.name]
  if (marker) marker.openPopup()
}

onMounted(() => {
  initMap()
})

onBeforeUnmount(() => {
  console.log('Cleaning up map...')
  try {
    if (mapRef.value) {
      // Remove all layers first
      if (heatmapLayer.value) {
        mapRef.value.removeLayer(heatmapLayer.value)
        heatmapLayer.value = null
      }
      
      // Clear markers
      Object.values(markersRef.value).forEach(marker => {
        if (marker && mapRef.value) {
          mapRef.value.removeLayer(marker)
        }
      })
      markersRef.value = {}
      
      // Remove the map
      mapRef.value.remove()
      mapRef.value = null
      
      console.log('Map cleanup completed')
    }
  } catch (error) {
    console.error('Error during map cleanup:', error)
  }
})
</script>

<style scoped>
#map :deep(.leaflet-pane),
#map :deep(.leaflet-top),
#map :deep(.leaflet-bottom) {
  z-index: 0;
}
</style>


