<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class RegisterController extends Controller
{
    public function showRegistrationForm()
    {
        return Inertia::render('auth/Register');
    }

    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'middle_name' => ['nullable', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'suffix' => ['nullable', 'string', 'max:255'],
            'birthdate' => ['required', 'date'],
            'gender' => ['required', 'string', 'in:male,female,other'],
            'mobile_number' => ['required', 'string', 'max:255'],
            'region' => ['required', 'string'],
            'province' => ['required', 'string'],
            'city' => ['required', 'string'],
            'barangay' => ['required', 'string'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'verification_code' => ['required', 'string', 'size:6'],
            // Removed role validation
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // First, find or create a barangay record
        $barangayId = DB::table('barangays')
            ->where('name', $request->barangay)
            ->where('region', $request->region)
            ->where('province', $request->province)
            ->where('city', $request->city)
            ->value('id');

        if (!$barangayId) {
            $barangayId = DB::table('barangays')->insertGetId([
                'name' => $request->barangay,
                'region' => $request->region,
                'province' => $request->province,
                'city' => $request->city,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $user = User::create([
            'name' => $request->name,
            'middle_name' => $request->middle_name,
            'last_name' => $request->last_name,
            'suffix' => $request->suffix,
            'birthdate' => $request->birthdate,
            'gender' => $request->gender,
            'mobile_number' => $request->mobile_number,
            'region' => $request->region,
            'province' => $request->province,
            'city' => $request->city,
            'barangay' => $request->barangay,
            'barangay_id' => $barangayId, // Set the barangay_id
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'verification_code' => $request->verification_code,
            // Removed role assignment
        ]);

        return redirect()->route('login')->with('success', 'Registration successful! Please login.');
    }
}




