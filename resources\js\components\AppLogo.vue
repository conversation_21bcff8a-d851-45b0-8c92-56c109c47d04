<script setup lang="ts">
import type { User } from '@/types';
import { usePage } from '@inertiajs/vue3';

const page = usePage<{ auth: { user: User } }>();
const user = page.props.auth.user;
</script>

<template>
  <div class="flex items-center gap-2">
    <img src="/logo.png" alt="Logo" style="height: 32px; width: 32px;" />
    <!-- You can replace the above with your actual logo component or image -->
  </div>
</template>
