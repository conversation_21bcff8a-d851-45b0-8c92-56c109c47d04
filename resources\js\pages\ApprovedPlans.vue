<template>
  <div class="px-6 py-4">

    <!-- <PERSON> Header -->
    <div class="mb-6">

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
      <div class="bg-white border border-emerald-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150">
        <div class="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center border-2 border-emerald-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-emerald-800 leading-tight truncate">Approved Plans</div>
          <div class="text-xs text-emerald-600 leading-tight truncate">Total Approved</div>
          <div class="inline-block mt-1 px-3 py-1 rounded-lg bg-emerald-100 text-emerald-800 text-xs font-semibold border border-emerald-200">{{ filteredPlans.length }}</div>
        </div>
      </div>

      <div class="bg-white border border-amber-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150">
        <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center border-2 border-amber-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-amber-800 leading-tight truncate">Barangays</div>
          <div class="text-xs text-amber-600 leading-tight truncate">Total Barangays</div>
          <div class="inline-block mt-1 px-3 py-1 rounded-lg bg-amber-100 text-amber-800 text-xs font-semibold border border-amber-200">{{ [...new Set(approvedPlans.map(plan => plan.barangay_name).filter(Boolean))].length }}</div>
        </div>
      </div>

      <div class="bg-white border border-blue-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150" @click="cycleFiscalYear">
        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center border-2 border-blue-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-blue-800 leading-tight truncate">{{ selectedYear ? `Fiscal Year ${selectedYear}` : `Fiscal Year ${currentYear}` }}</div>
          <div class="text-xs text-blue-600 leading-tight truncate">Budget Allocation</div>
          <div class="inline-block mt-1 px-2 py-0.5 rounded bg-blue-100 text-blue-700 text-xs font-medium">
            {{ formatCurrency(yearBudget) }}
          </div>
          <div class="text-xs text-blue-500 mt-1 italic">Click to change year</div>
        </div>
      </div>
    </div>

    <!-- Approved Plans Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 mb-6">
      <!-- Table header -->
      <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 dark:from-emerald-700 dark:to-emerald-800 px-4 py-3">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div>
            <h2 class="text-white text-lg font-semibold">Approved Budget Plans</h2>
          </div>

          <div class="relative">
            <input
              type="text"
              placeholder="Search approved plans..."
              v-model="searchQuery"
              class="pl-8 pr-3 py-1.5 text-sm rounded-md border border-emerald-400 bg-white/90 text-emerald-800 placeholder-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-500 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-gray-50 dark:bg-gray-800/50 px-4 py-2.5 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center gap-2">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Filter by:</span>

          <!-- Fiscal Year Filter -->
          <select
            v-model="selectedYear"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">All Years</option>
            <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
          </select>
        </div>
      </div>

      <div class="overflow-x-auto max-h-[60vh] py-2">
        <table class="min-w-full">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
            <tr>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Barangay</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Email</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">Focused</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">Gender Issue</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">GAD Mandate</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Status</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden sm:table-cell">Date Submitted</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden sm:table-cell">Date Approved</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="(plan, idx) in paginatedPlans"
                :key="plan.id"
                :class="idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'"
                class="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <td class="px-3 py-2.5 text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ plan.barangay_name || plan.user?.barangay || 'Unknown Barangay' }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 max-w-[180px] truncate">
                {{ plan.user_email || plan.user?.email || 'N/A' }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                {{ plan.focused || '' }}
              </td>
               <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell text-left">
                <span v-if="plan?.gender_issue === 'Gender Issue'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  Gender Issue
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell text-left">
                <span v-if="plan?.gender_issue === 'GAD Mandate'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  GAD Mandate
                </span>
              </td>
              <td class="px-3 py-2.5">
                <span class="px-2.5 py-1 inline-flex items-center text-xs font-medium rounded-md border bg-emerald-100 text-emerald-800 border-emerald-200">
                  Approved
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden sm:table-cell whitespace-nowrap">
                {{ formatDate(plan.created_at) }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden sm:table-cell whitespace-nowrap">
                {{ formatDate(plan.date_approved || plan.approval_date || '') }}
              </td>
              <td class="px-3 py-2.5">
                <button
                  class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors"
                  @click="viewPlan(plan)"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  view
                </button>
              </td>
            </tr>
            <tr v-if="filteredPlans.length === 0">
              <td colspan="9" class="px-4 py-16 text-center">
                <div class="flex flex-col items-center">
                  <div class="rounded-full bg-emerald-50 dark:bg-emerald-900/30 p-5 mb-4 border border-emerald-200 dark:border-emerald-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-emerald-700 dark:text-emerald-300">No approved plans found</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 max-w-md">
                    {{ searchQuery || selectedYear ? 'No results match your search criteria. Try adjusting your filters.' : 'No approved budget plans are available.' }}
                  </p>
                  <button
                    v-if="searchQuery || selectedYear"
                    @click="clearFilters"
                    class="mt-4 px-4 py-2 text-sm font-medium text-emerald-700 dark:text-emerald-300 bg-emerald-50 dark:bg-emerald-900/30 rounded-md border border-emerald-200 dark:border-emerald-800 hover:bg-emerald-100 dark:hover:bg-emerald-900/50 transition-colors"
                  >
                    Clear Filters
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modal for viewing plan -->
    <BarangayBudgetPlanModal
      v-if="showPlanModal"
      :show="showPlanModal"
      :plan="selectedPlan"
      @close="showPlanModal = false"
    />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import axios from 'axios'
import BarangayBudgetPlanModal from '../components/BarangayBudgetPlanModal.vue'

interface User {
  id: number
  name: string
  email: string
  barangay: string
  avatar?: string
}

interface BudgetPlan {
  id: number
  title_desc?: string
  title?: string
  fiscal_year: number
  total_budget: number
  status: string
  created_at: string
  updated_at: string
  approval_date?: string
  date_approved?: string
  user_name?: string
  user_email?: string
  barangay_name?: string
  focused?: string
  gender_issue?: string
  user?: User
}

// Reactive state for real data
const approvedPlans = ref<BudgetPlan[]>([])

// Reactive state
const searchQuery = ref('')
const selectedYear = ref('')
const currentPage = ref(1)
const itemsPerPage = ref(10)

const showPlanModal = ref(false)
const selectedPlan = ref<BudgetPlan | null>(null)

// Fetch approved plans from API
async function fetchApprovedPlans() {
  try {
    console.log('Fetching approved plans...')
    const response = await axios.get('/api/budget-plans')
    console.log('API Response:', response.data)
    
    if (response.data.budgetPlans && response.data.budgetPlans.length > 0) {
      console.log('Number of budget plans found:', response.data.budgetPlans.length)
      
      // Filter only approved plans from the response
      const allPlans = response.data.budgetPlans
      const approvedPlansData = allPlans.filter((plan: BudgetPlan) => 
        plan.status === 'Approved'
      ).map((plan: BudgetPlan) => ({
        ...plan,
        title: plan.title_desc || plan.title || 'Untitled Plan',
        user: {
          id: 0, // We don't have user ID in the response
          name: plan.user_name || 'Unknown User',
          email: plan.user_email || 'No Email',
          barangay: plan.barangay_name || 'Unknown Barangay'
        },
        date_approved: plan.approval_date || plan.date_approved
      }))
      
      approvedPlans.value = approvedPlansData
      console.log('Number of approved plans found:', approvedPlansData.length)
    } else {
      console.log('No budget plans found')
      approvedPlans.value = []
    }
  } catch (error) {
    console.error('Exception fetching approved plans:', error)
    approvedPlans.value = []
  }
}

// Computed properties
const filteredPlans = computed(() => {
  let plans = approvedPlans.value || []

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    plans = plans.filter(plan =>
      plan.title?.toLowerCase().includes(query) ||
      plan.user?.name?.toLowerCase().includes(query) ||
      plan.user?.barangay?.toLowerCase().includes(query) ||
      plan.user?.email?.toLowerCase().includes(query)
    )
  }

  // Year filter
  if (selectedYear.value) {
    plans = plans.filter(plan => plan.fiscal_year?.toString() === selectedYear.value)
  }

  return plans
})

const paginatedPlans = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredPlans.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredPlans.value.length / itemsPerPage.value)
})

const availableYears = computed(() => {
  const years = approvedPlans.value?.map(plan => plan.fiscal_year).filter(Boolean) || []
  return [...new Set(years)].sort((a, b) => b - a)
})

const currentYear = computed(() => {
  return new Date().getFullYear()
})

const yearBudget = computed(() => {
  // Get the target year (selected year or current year)
  const targetYear = selectedYear.value || currentYear.value.toString()

  // Return allocated budget for the fiscal year (not sum of barangay budgets)
  // This could be fetched from an API or stored in a configuration
  const yearlyBudgetAllocations: Record<string, number> = {
    '2025': 5000000, // 5M for FY 2025
    '2024': 2000000, // 2M for FY 2024
    '2023': 1000000, // 1M for FY 2023
    '2022': 9000000,  // 9M for FY 2022
  }

  return yearlyBudgetAllocations[targetYear] || 12000000 // Default to 12M if year not found
})

// Clear all filters
function clearFilters() {
  searchQuery.value = ''
  selectedYear.value = ''
}

// Cycle through fiscal years
function cycleFiscalYear() {
  const years = availableYears.value
  if (years.length === 0) return

  if (!selectedYear.value) {
    // If no year is selected, select the first available year
    selectedYear.value = years[0].toString()
  } else {
    // Find current year index and move to next year
    const currentIndex = years.findIndex(year => year.toString() === selectedYear.value)
    if (currentIndex === -1 || currentIndex === years.length - 1) {
      // If current year not found or is the last year, reset to show all years
      selectedYear.value = ''
    } else {
      // Move to next year
      selectedYear.value = years[currentIndex + 1].toString()
    }
  }
}

// Helper functions
function formatCurrency(value: number): string {
  return '₱' + Math.floor(value).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

function formatDate(dateString: string): string {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

function viewPlan(plan: BudgetPlan): void {
  selectedPlan.value = plan
  showPlanModal.value = true
}

function downloadPlan(plan: BudgetPlan): void {
  // TODO: Implement download plan functionality
  alert(`Download plan: ${plan.title}`)
}

// Load data on component mount
onMounted(() => {
  fetchApprovedPlans()
})
</script>

<style scoped>
/* Add subtle animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.bg-white, .bg-gradient-to-r {
  animation: fadeIn 0.3s ease-out;
}

/* Improve table hover effects */
tr:hover td {
  transition: all 0.2s ease;
}

/* Improve button hover effects */
button {
  transition: all 0.2s ease;
}

/* Add subtle shadow to status badges */
[class*="bg-emerald-500"],
[class*="bg-amber-500"],
[class*="bg-red-500"],
[class*="bg-blue-500"] {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
</style>
