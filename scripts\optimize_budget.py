#!/usr/bin/env python
import sys
import json
import logging
from scipy.optimize import linprog

# Set up logging (reduced verbosity for performance)
logging.basicConfig(
    filename='optimize_budget.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def optimize_budget(data):
    try:
        logging.info(f"Starting optimization with data: {json.dumps(data)}")

        # Extract data
        activities = data['activities']
        scores = data['scores']
        total_budget = data['total_budget']
        manual_allocations = data['manual_allocations']

        # Define constraints
        min_percent = 0.0001  # 0.01%
        max_percent = 0.001094  # 0.1094%
        min_alloc = min_percent * total_budget
        max_alloc = max_percent * total_budget

        logging.info(f"Optimizing {len(activities)} activities with budget {total_budget}")

        num_activities = len(activities)

        # Coefficients for the objective (maximize sum scores*allocations)
        # linprog does minimization, so minimize negative of objective
        c = [-s for s in scores]

        # Bounds per variable (min_alloc, max_alloc)
        bounds = [(min_alloc, max_alloc) for _ in range(num_activities)]

        # Constraints matrix (sum allocations <= total_budget)
        A = [[1] * num_activities]
        b = [total_budget]

        # Run optimization
        res = linprog(c, A_ub=A, b_ub=b, bounds=bounds, method='highs')

        if not res.success:
            logging.error(f"Optimization failed: {res.message}")
            return {"success": False, "message": f"Optimization failed: {res.message}"}

        # Process results
        lp_allocations = {activities[i]: float(alloc) for i, alloc in enumerate(res.x)}
        logging.info(f"Optimization successful, total allocation: {sum(lp_allocations.values()):.2f}")

        # Combine with manual allocations
        combined_results = {}
        for activity in activities:
            manual = manual_allocations.get(activity, {"mooe": 0, "ps": 0, "co": 0})
            manual_total = float(manual["mooe"]) + float(manual["ps"]) + float(manual["co"])
            optimal_alloc = lp_allocations.get(activity, 0)

            combined_results[activity] = {
                "optimal_allocation": optimal_alloc,
                "manual_allocation": manual_total,
                "combined_total": optimal_alloc + manual_total,
                "breakdown": {
                    "mooe": float(manual["mooe"]),
                    "ps": float(manual["ps"]),
                    "co": float(manual["co"])
                }
            }

        return {
            "success": True,
            "results": combined_results,
            "total_optimal_score": float(-res.fun)
        }
    except Exception as e:
        logging.error(f"Exception in optimize_budget: {str(e)}", exc_info=True)
        return {"success": False, "message": f"Error: {str(e)}"}

if __name__ == "__main__":
    try:
        logging.info("Script started")

        # Get input data from command line argument
        if len(sys.argv) < 2:
            logging.error("No input data provided")
            print(json.dumps({"success": False, "message": "No input data provided"}))
            sys.exit(1)

        input_data = json.loads(sys.argv[1])
        logging.info(f"Received input data: {json.dumps(input_data)}")

        # Run optimization
        result = optimize_budget(input_data)

        # Output result as JSON
        output = json.dumps(result)
        logging.info(f"Output result: {output}")
        print(output)

    except Exception as e:
        logging.error(f"Unhandled exception: {str(e)}", exc_info=True)
        print(json.dumps({"success": False, "message": f"Unhandled error: {str(e)}"}))

