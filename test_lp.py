#!/usr/bin/env python3
import sys
import json
import numpy as np
from scipy.optimize import linprog

def test_lp_optimization():
    """Test the LP optimization with sample data"""
    
    # Sample data
    activities = [
        "Capdev/Training - gender sensitivity trainings (GST)",
        "Capdev/Training - gender analysis"
    ]
    scores = [5, 5]
    total_budget = 91407936.05
    min_percent = 0.0001  # 0.01%
    max_percent = 0.001094  # 0.1094%
    
    min_alloc = min_percent * total_budget
    max_alloc = max_percent * total_budget
    
    num_activities = len(activities)
    
    print("=== LP Optimization Test ===")
    print(f"Activities: {activities}")
    print(f"Scores: {scores}")
    print(f"Total Budget: ₱{total_budget:,.2f}")
    print(f"Min allocation per activity: ₱{min_alloc:,.2f}")
    print(f"Max allocation per activity: ₱{max_alloc:,.2f}")
    
    # LP optimization
    c = [-s for s in scores]  # Minimize negative (maximize positive)
    bounds = [(min_alloc, max_alloc) for _ in range(num_activities)]
    A = [[1] * num_activities]  # Sum constraint
    b = [total_budget]
    
    try:
        result = linprog(c, A_ub=A, b_ub=b, bounds=bounds, method='highs')
        
        if result.success:
            print("\n=== Optimization Results ===")
            for i, activity in enumerate(activities):
                allocation = result.x[i]
                print(f"{activity}: ₱{allocation:,.2f}")
            
            total_allocated = sum(result.x)
            optimal_score = -result.fun
            
            print(f"\nTotal Allocated: ₱{total_allocated:,.2f}")
            print(f"Optimal Score: {optimal_score:,.2f}")
            print(f"Remaining Budget: ₱{total_budget - total_allocated:,.2f}")
            
            # Test manual allocations
            manual_allocations = {
                activities[0]: {"mooe": 50000, "ps": 30000, "co": 20000},
                activities[1]: {"mooe": 25000, "ps": 15000, "co": 10000}
            }
            
            print("\n=== Combined Results ===")
            total_combined = 0
            for i, activity in enumerate(activities):
                lp_alloc = result.x[i]
                manual = manual_allocations[activity]
                manual_total = manual["mooe"] + manual["ps"] + manual["co"]
                combined = lp_alloc + manual_total
                total_combined += combined
                
                print(f"{activity}:")
                print(f"  LP Allocation: ₱{lp_alloc:,.2f}")
                print(f"  Manual (MOOE: ₱{manual['mooe']:,}, PS: ₱{manual['ps']:,}, CO: ₱{manual['co']:,}): ₱{manual_total:,}")
                print(f"  Combined Total: ₱{combined:,.2f}")
            
            print(f"\nTotal Combined Budget: ₱{total_combined:,.2f}")
            
            return {
                "success": True,
                "lp_allocations": dict(zip(activities, result.x)),
                "manual_allocations": manual_allocations,
                "combined_totals": total_combined
            }
            
        else:
            print(f"Optimization failed: {result.message}")
            return {"success": False, "error": result.message}
            
    except Exception as e:
        print(f"Error during optimization: {str(e)}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    result = test_lp_optimization()
    print(f"\n=== Final Result ===")
    print(json.dumps(result, indent=2, default=str))
