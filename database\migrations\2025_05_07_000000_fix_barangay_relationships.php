<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Create barangays table if it doesn't exist
        if (!Schema::hasTable('barangays')) {
            Schema::create('barangays', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('region')->nullable();
                $table->string('province')->nullable();
                $table->string('city')->nullable();
                $table->timestamps();
            });
        }

        // 2. Create default barangay with ID 1
        DB::table('barangays')->updateOrInsert(
            ['id' => 1],
            [
                'name' => 'Default Barangay',
                'region' => 'Default Region',
                'province' => 'Default Province',
                'city' => 'Default City',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // 3. Ensure users table has barangay_id column
        if (!Schema::hasColumn('users', 'barangay_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->foreignId('barangay_id')->nullable()->after('barangay');
            });
        }

        // 4. Update all users with null barangay_id to use default barangay
        DB::table('users')
            ->whereNull('barangay_id')
            ->update(['barangay_id' => 1]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse these changes as they're fixing data integrity
    }
};