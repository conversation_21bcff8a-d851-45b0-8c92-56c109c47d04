<?php

use App\Helpers\LocationHelper;
use Illuminate\Support\Facades\Route;

Route::prefix('locations')->group(function () {
    Route::get('regions', function () {
        return response()->json(LocationHelper::getRegions());
    });

    Route::get('provinces/{region}', function ($region) {
        return response()->json(LocationHelper::getProvinces($region));
    });

    Route::get('cities/{region}/{province}', function ($region, $province) {
        return response()->json(LocationHelper::getCities($region, $province));
    });

    Route::get('barangays/{region}/{province}/{city}', function ($region, $province, $city) {
        return response()->json(LocationHelper::getBarangays($region, $province, $city));
    });
});
