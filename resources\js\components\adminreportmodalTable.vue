<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-1 sm:p-4">
    <div class="bg-white shadow-lg w-full max-w-7xl mx-auto overflow-hidden border border-gray-400 rounded-xl max-h-[98vh] sm:max-h-[95vh] flex flex-col">
      <!-- Title -->
      <div class="text-center py-2 sm:py-4 relative px-2 sm:px-4 flex-shrink-0">
        <h1 class="text-xs sm:text-xl font-bold text-gray-700 pr-8 sm:pr-0 leading-tight">BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY {{ currentYear }}</h1>
        <button @click="$emit('close')" class="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 px-2 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-md shadow-sm hover:shadow-md hover:from-red-600 hover:to-red-700 transition-all duration-200 text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-1.5">
          <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span class="hidden sm:inline">Close</span>
        </button>
      </div>

      <!-- Status Badges -->
      <div class="flex flex-wrap items-center gap-1 sm:gap-2 px-2 sm:px-4 pt-2 sm:pt-3 pb-1 sm:pb-2 flex-shrink-0">
        <span v-if="report.status === 'pending'" class="bg-amber-100 text-amber-800 border-amber-200 text-xs px-2 sm:px-3 py-1 sm:py-1.5 font-semibold rounded-lg border">Pending</span>
        <span v-if="report.status === 'approved'" class="bg-emerald-100 text-emerald-800 border-emerald-200 text-xs px-2 sm:px-3 py-1 sm:py-1.5 font-semibold rounded-lg border">Approved</span>
        <span v-if="report.status === 'revision'" class="bg-blue-100 text-blue-800 border-blue-200 text-xs px-2 sm:px-3 py-1 sm:py-1.5 font-semibold rounded-lg border">Needs Revision</span>
        <span v-if="report.status === 'rejected' || report.status === 'disapproved'" class="bg-red-100 text-red-800 border-red-200 text-xs px-2 sm:px-3 py-1 sm:py-1.5 font-semibold rounded-lg border">Disapproved</span>
        <span class="text-xs text-gray-500 hidden sm:inline ml-2">{{ getStatusMessage(report.status) }}</span>
        <button @click="exportToExcel" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">
          <span class="hidden sm:inline">📈</span>
          <span class="text-xs">Export to Excel</span>
        </button>
        <button @click="openComments" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">
          <span class="hidden sm:inline">💬</span>
          <span class="text-xs">Comments</span>
        </button>
        <button @click="printpreview" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">
          <span class="hidden sm:inline">🖨️</span>
          <span class="text-xs">Print Print Preview</span>
        </button>
      </div>

      <!-- Main Content -->
      <div class="flex-1 overflow-auto">
        <!-- Mobile Card View -->
        <div class="block md:hidden p-2 space-y-4">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
            <div class="space-y-3">
              <div class="border-b pb-2">
                <h3 class="font-semibold text-sm text-gray-800 mb-1">{{ report.focused || 'CLIENT-FOCUSED' }}</h3>
                <p class="text-xs text-gray-600">{{ report.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue' }}</p>
              </div>

              <div class="grid grid-cols-1 gap-3 text-xs">
                <div>
                  <span class="font-semibold text-gray-700">Gender Issues/GAD Mandate:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.title || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Objective:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.gadObjective || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">LGU Program/Project:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.lguPpa || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Activity:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.gadActivity || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Performance Indicator:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.performanceIndicator || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Actual Results:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.actualResults || '-' }}</p>
                </div>

                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <span class="font-semibold text-gray-700">Approved Budget:</span>
                    <p class="mt-1 text-gray-600 font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</p>
                  </div>
                  <div>
                    <span class="font-semibold text-gray-700">Actual Cost:</span>
                    <p class="mt-1 text-gray-600">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</p>
                  </div>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Variance/Remarks:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ report.remarks || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Supporting Documents:</span>
                  <div class="mt-1">
                    <span v-if="attachmentFile" class="text-red-500 text-xs">{{ truncateFilename(attachmentFile.name, 20) }}</span>
                    <div v-else-if="report.attachment_path" class="flex flex-col gap-1">
                      <div v-for="(path, index) in getAttachmentPaths(report.attachment_path)" :key="index">
                        <span class="text-blue-500 cursor-pointer hover:underline text-xs" @click="downloadAttachment(path)" :title="getAttachmentName(path)">
                          {{ truncateFilename(getAttachmentName(path), 20) }}
                        </span>
                      </div>
                    </div>
                    <span v-else class="text-red-500 text-xs">No file(s) attached</span>
                  </div>
                </div>
              </div>

              <!-- Mobile Grand Total -->
              <div class="bg-emerald-700 text-white p-3 rounded mt-4">
                <div class="flex justify-between items-center">
                  <span class="font-bold text-sm">GRAND TOTAL</span>
                </div>
                <div class="grid grid-cols-2 gap-2 mt-2 text-xs">
                  <div>
                    <span>Approved: </span>
                    <span class="font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</span>
                  </div>
                  <div>
                    <span>Actual: </span>
                    <span class="font-bold">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Desktop Table View -->
        <div class="hidden md:block p-4">
          <div class="overflow-x-auto">
            <table class="w-full border border-gray-400 text-xs min-w-[1200px]">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Gender Issues or GAD Mandate</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">GAD Objective</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Relevant LGU Program or Project</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">GAD Activity</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Performance Indicator and Target</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Actual Results</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Approved GAD Budget</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Actual Cost or Expenditure</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[100px]">Variance or Remarks</th>
                  <th class="border border-gray-400 px-2 py-2 min-w-[120px]">Supporting Documents</th>
                </tr>
                <tr>
                  <th colspan="10" class="border border-gray-400 font-bold text-left px-2 py-2 bg-gray-100">{{ report.focused || 'CLIENT-FOCUSED' }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td colspan="10" class="border border-gray-400 bg-white text-left px-2 py-2">{{ report.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue' }}</td>
                </tr>
                <tr>
                  <td class="border border-gray-400 px-2 py-2">{{ report.title || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.gadObjective || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.lguPpa || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.gadActivity || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.performanceIndicator || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.actualResults || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right font-bold">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">{{ report.remarks || '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2">
                    <div class="flex items-center space-x-1.5 text-sm text-gray-600 hover:text-gray-800">
                      <!-- No files text -->
                      <div class="text-sm pl-6 text-left">
                        <span v-if="attachmentFile" class="text-red-500">{{ truncateFilename(attachmentFile.name, 20) }}</span>
                        <div v-else-if="report.attachment_path" class="flex flex-col gap-1">
                          <div v-for="(path, index) in getAttachmentPaths(report.attachment_path)" :key="index">
                            <span class="text-blue-500 cursor-pointer hover:underline" @click="downloadAttachment(path)" :title="getAttachmentName(path)">
                              {{ truncateFilename(getAttachmentName(path), 20) }}
                            </span>
                          </div>
                        </div>
                        <span v-else class="text-red-500">No file(s) attached</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr class="bg-emerald-700 text-white">
                  <td colspan="6" class="border border-gray-400 px-2 py-2 font-bold text-left">GRAND TOTAL</td>
                  <td class="border border-gray-400 px-2 py-2 font-bold text-right">{{ report.approvedBudget ? report.approvedBudget.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2 text-right">{{ report.actualCost ? report.actualCost.toLocaleString() : '-' }}</td>
                  <td class="border border-gray-400 px-2 py-2"></td>
                  <td class="border border-gray-400 px-2 py-2"></td>
                </tr>
                <tr>
                  <td colspan="3" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Prepared by:</div>
                    <div class="font-bold text-base mb-0">{{ props.report.user_name || 'UNKNOWN USER' }}</div>
                    <div class="text-xs mt-1">Barangay Gad Focal</div>
                  </td>
                  <td colspan="3" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Approved by:</div>
                                     <div class="font-bold text-base mb-0">
                <br>
                  <span class="text-gray-400"></span>

              </div>
                    <div class="text-xs mt-1">Punong Barangay</div>
                  </td>
                  <td colspan="4" class="border border-gray-400 p-3">
                    <div class="font-semibold mb-1">Date:</div>
                                     <div class="font-bold text-base mb-0">
                <br>
                  <span class="text-gray-400"></span>

              </div>
                    <div class="text-xs mt-1">DD/MM/YEAR</div>
                    </td>
                </tr>
                <tr>
                  <td colspan="6" class="border-b border-t border-l border-gray-400 px-2 py-2">
      <div class="font-semibold mb-1">Verified and Endorsed by:</div>
        <div class="font-bold text-base mb-0">
                <br>
                  <span v-if="props.report.status === 'approved' && props.report.admin_approver" class="text-black">{{ props.report.admin_approver }}</span>
                  <span v-else class="text-gray-400"></span>

              </div>
        <div class="text-xs mt-1">Chairperson, GFPS TWG/secretary</div>
      </td>
      <td colspan="3" class="border-b border-t border-gray-400 px-2 py-2">
        <div class="font-semibold mb-1">Date Verified:</div>
              <div class="font-bold text-base mb-0">
                <br>
                  <span v-if="props.report.status === 'approved' && props.report.date_approved" class="text-black">{{ formatApprovalDate(props.report.date_approved) }}</span>
                  <span v-else class="text-gray-400"></span>

              </div>
              <div class="text-xs mt-1">DD/MM/YEAR</div>
    </td>
<td class="border-gray-400 px-2 py-2 flex flex-col items-center justify-center gap-3">
  <button
    @click="approveReport"
    :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
    class="bg-gradient-to-r from-emerald-500 to-emerald-600 border border-emerald-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-emerald-600 hover:to-emerald-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px] whitespace-nowrap"
  >
    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    <span>Approved</span>
  </button>

  <button
    @click="disapproved"
    :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
    class="bg-gradient-to-r from-red-500 to-red-600 border border-red-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-red-600 hover:to-red-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px] whitespace-nowrap"
  >
    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
    <span>Disapproved</span>
  </button>

  <button
    @click="reviseReport"
    :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
    class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px] whitespace-nowrap"
  >
    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
    <span>Revised</span>
  </button>
</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Signature Section -->
        <div class="block md:hidden p-2">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm space-y-4">
            <div class="grid grid-cols-1 gap-4 text-xs">
              <div class="border-b pb-3">
                <span class="font-semibold text-gray-700">Prepared by:</span>
                <p class="mt-1 font-bold text-sm">{{ report.user_name || 'UNKNOWN USER' }}</p>
                <p class="text-xs text-gray-600">Barangay Gad Focal</p>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="font-semibold text-gray-700">Approved by:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Punong Barangay</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 border-t pt-3">
                <div>
                  <span class="font-semibold text-gray-700">Verified and Endorsed by:</span>
                  <p v-if="props.report.status === 'approved' && props.report.admin_approver" class="mt-1 font-bold text-sm text-black">{{ props.report.admin_approver }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Chairperson, GFPS TWG/secretary</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date Verified:</span>
                  <p v-if="props.report.status === 'approved' && props.report.date_approved" class="mt-1 font-bold text-sm text-black">{{ formatApprovalDate(props.report.date_approved) }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>

              <!-- Mobile Action Buttons -->
              <div class="flex flex-col gap-3 pt-3 border-t">
                <button @click="approveReport" 
                :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
                class="bg-gradient-to-r from-emerald-500 to-emerald-600 border border-emerald-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-emerald-600 hover:to-emerald-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-full">
                  <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>Approved</span>
                </button>
                <button @click="disapproved" 
                :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
                class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-full">
                  <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
                  <span>Disapproved</span>
                </button>
                <button @click="reviseReport" 
                :disabled="report.status === 'approved' || report.status === 'disapproved' || report.status === 'revision'"
                class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-full">
                <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
                  <span>Revised</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Custom Alert/Confirm Modal -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button
            v-if="modalType === 'confirm'"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            @click="modalType === 'confirm' ? confirmAction() : closeModal()"
            class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
          >
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, onMounted, computed } from 'vue';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';
import * as XLSX from 'xlsx';

const props = defineProps<{ show: boolean, report: any }>();
const emit = defineEmits(['close', 'update']);
const page = usePage<{ auth: { user: { name: string, first_name?: string, middle_name?: string, last_name?: string, suffix?: string, role?: string, id?: number, email?: string } } }>();
const submitterFullName = ref('');
const attachmentFile = ref(null);

// Modal state variables
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref('info'); // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref<(() => void) | null>(null);

// Use the fiscal year from the report or default to current year
const currentYear = computed(() => props.report?.fiscalYear || new Date().getFullYear());

function formatApprovalDate(dateString: string) {
  if (!dateString) return 'DD/MM/YEAR';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

function formatLongDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function getStatusMessage(status: string) {
  if (!status) return '';

  const messages: { [key: string]: string } = {
    'pending': 'The report is awaiting review',
    'approved': 'The report has been approved',
    'rejected': 'The revision has been requested by the reviewer',
    'revision': 'The revision has been requested by the reviewer'
  };

  return messages[status.toLowerCase()] || '';
}

function getFullSubmitterName() {
  // If report has user_name, use that
  if (props.report && props.report.user_name) {
    return props.report.user_name.toUpperCase();
  }

  // If all else fails, use the current user's name
  const user = page.props.auth.user;
  if (user && user.name) {
    return user.name.toUpperCase();
  }

  return 'UNKNOWN USER';
}

onMounted(() => {
  // Initialize submitter name if report has user data
  if (props.report && props.report.user_name) {
    submitterFullName.value = props.report.user_name.toUpperCase();
  }
});

// Add this function to get admin's full name
function getAdminFullName() {
  // Get the current authenticated user from page props
  const user = page.props.auth.user;

  if (!user) return 'Unknown Admin';

  // Build the full name properly
  let fullName = '';

  // Check if we have name parts
  if (user.first_name || user.middle_name || user.last_name) {
    if (user.first_name) {
      fullName += user.first_name;
    }

    if (user.middle_name) {
      fullName += ' ' + user.middle_name.charAt(0) + '.';
    }

    if (user.last_name) {
      fullName += ' ' + user.last_name;
    }

    if (user.suffix) {
      fullName += ', ' + user.suffix;
    }
  } else if (user.name) {
    // If we don't have name parts, use the full name field
    fullName = user.name;
  }

  // If we still don't have a name, return a default
  if (!fullName.trim()) {
    fullName = 'Unknown Admin';
  }

  // Return the full name in uppercase
  return fullName.trim().toUpperCase();
}

// Modal functions
function showModal(title: string, message: string, type: string = 'info') {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = type;
  modalConfirmCallback.value = null;
  modalVisible.value = true;
}

function showConfirmModal(title: string, message: string, onConfirm: () => void) {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = 'confirm';
  modalConfirmCallback.value = onConfirm;
  modalVisible.value = true;
}

function closeModal() {
  modalVisible.value = false;
  modalConfirmCallback.value = null;
}

function confirmAction() {
  if (modalConfirmCallback.value) {
    modalConfirmCallback.value();
  }
  closeModal();
}

function approveReport() {
  showConfirmModal(
    'Approve Report',
    'Are you sure you want to approve this accomplishment report? This action cannot be undone.',
    () => {
      performApproval();
    }
  );
}

function performApproval() {
  // Get the current admin's full name
  const adminFullName = getAdminFullName();
  const currentDate = new Date().toISOString();

  // Create data for the update
  const updateData = {
    status: 'approved',
    approver_name: adminFullName,
    admin_approver: adminFullName,
    date_approved: currentDate
  };

  // Debug logging
  console.log('Admin approval data:', {
    adminFullName,
    currentDate,
    updateData,
    userObject: page.props.auth.user,
    userObjectKeys: Object.keys(page.props.auth.user || {}),
    userObjectValues: page.props.auth.user
  });

  // Update the report using PUT request
  axios.put(`/api/accomplishment-reports/${props.report.id}`, updateData)
    .then(response => {
      console.log('Approval response:', response.data);
      if (response.data.success) {
        // Update the local report object to reflect changes
        props.report.status = 'approved';
        props.report.approver_name = adminFullName;
        props.report.admin_approver = adminFullName;
        props.report.date_approved = currentDate;

        // Force reactivity update
        Object.assign(props.report, {
          status: 'approved',
          approver_name: adminFullName,
          admin_approver: adminFullName,
          date_approved: currentDate
        });

        console.log('Updated report object:', props.report);
        console.log('Report admin_approver:', props.report.admin_approver);
        console.log('Report date_approved:', props.report.date_approved);

        // Emit update event to notify parent component
        emit('update', props.report);

        showModal('Success', 'Report approved successfully!', 'success');
        setTimeout(() => {
          emit('close'); // Close the modal after showing success
        }, 1500);
      } else {
        throw new Error(response.data.message || 'Unknown error');
      }
    })
    .catch(error => {
      console.error('Error approving report:', error);
      showModal('Error', 'Failed to approve report. Please try again.', 'error');
    });
}
function getAttachmentPaths(attachmentPath: string): string[] {
  if (!attachmentPath) return [];

  // Handle JSON array of paths
  if (attachmentPath.startsWith('[')) {
    try {
      return JSON.parse(attachmentPath);
    } catch (e) {
      console.error('Error parsing attachment paths:', e);
      return [attachmentPath];
    }
  }

  // Handle single path
  return [attachmentPath];
}

function getAttachmentName(path: string): string {
  if (!path) return '';
  return path.split('/').pop() || '';
}

function downloadAttachment(path: string) {
  if (!path) return;
  window.open('/storage/' + path, '_blank');
}

function truncateFilename(filename: string, maxLength: number): string {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;

  const extension = filename.split('.').pop() || '';
  const nameWithoutExtension = filename.substring(0, filename.length - extension.length - 1);

  // Calculate how many characters to keep from the name
  const charsToKeep = maxLength - extension.length - 3; // 3 for "..." and "."

  if (charsToKeep <= 0) return filename; // If maxLength is too small, return the original

  return nameWithoutExtension.substring(0, charsToKeep) + '...' + '.' + extension;
}

function reviseReport() {
  showConfirmModal(
    'Request Revision',
    'Are you sure you want to request revision for this accomplishment report? The submitter will be notified to make changes.',
    () => {
      performRevision();
    }
  );
}

function performRevision() {
  // Get the current admin's full name
  const adminFullName = getAdminFullName();

  // Create data for the update
  const updateData = {
    status: 'revision',
    reviewer_name: adminFullName
  };

  // Update the report using PUT request
  axios.put(`/api/accomplishment-reports/${props.report.id}`, updateData)
    .then(response => {
      if (response.data.success) {
        // Update the local report object to reflect changes
        props.report.status = 'revision';
        props.report.reviewer_name = adminFullName;

        showModal('Success', 'Report marked for revision successfully!', 'success');
        setTimeout(() => {
          emit('close'); // Close the modal after showing success
        }, 1500);
      } else {
        throw new Error(response.data.message || 'Unknown error');
      }
    })
    .catch(error => {
      console.error('Error requesting revision:', error);
      showModal('Error', 'Failed to request revision. Please try again.', 'error');
    });
}

function disapproved() {
  showConfirmModal(
    'Disapprove Report',
    'Are you sure you want to disapprove this accomplishment report? The submitter will be notified.',
    () => {
      performDisapprove();
    }
  );
}

function performDisapprove() {
  const adminFullName = getAdminFullName();

  const updateData = {
    status: 'disapproved',
    reviewer_name: adminFullName
  };

  axios.put(`/api/accomplishment-reports/${props.report.id}`, updateData)
    .then(response => {
      if (response.data.success) {
        props.report.status = 'disapproved';
        props.report.reviewer_name = adminFullName;

        showModal('Success', 'Report disapproved successfully!', 'success');
        setTimeout(() => {
          emit('close');
        }, 1500);
      } else {
        throw new Error(response.data.message || 'Unknown error');
      }
    })
    .catch(error => {
      showModal('Error', 'Failed to disapprove report. Please try again.', 'error');
    });
}

function exportToExcel() {
  // Create structured data that matches the print preview format
  const structuredData = [
    // Header row
    ['BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY ' + currentYear.value],
    [''], // Empty row for spacing

    // Main table headers
    ['Gender Issues or GAD Mandate', 'GAD Objective', 'Relevant LGU Program or Project', 'GAD Activity', 'Performance Indicator and Target', 'Actual Results', 'Approved GAD Budget', 'Actual Cost or Expenditure', 'Variance or Remarks', 'Supporting Documents'],

    // Focused type row
    [props.report?.focused || 'CLIENT-FOCUSED', '', '', '', '', '', '', '', '', ''],

    // Issue/Mandate type row
    [props.report?.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue', '', '', '', '', '', '', '', '', ''],

    // Data row
    [
      props.report?.title || '-',
      props.report?.gadObjective || '-',
      props.report?.lguPpa || '-',
      props.report?.gadActivity || '-',
      props.report?.performanceIndicator || '-',
      props.report?.actualResults || '-',
      props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-',
      props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-',
      props.report?.remarks || '-',
      getAttachmentDisplayText()
    ],

    // Grand Total row
    [
      'GRAND TOTAL', '', '', '', '', '',
      props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-',
      props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-',
      '', ''
    ],

    [''], // Empty row for spacing

    // Other details section
    ['Other Details:'],
    ['Prepared by:', props.report?.user_name || 'UNKNOWN USER'],
    ['Approved by:', ''],
    ['Date:', ''],
    ['Verified and Endorsed by:', props.report?.status === 'approved' && props.report?.admin_approver ? props.report.admin_approver : ''],
    ['Date Verified:', props.report?.status === 'approved' && props.report?.date_approved ? formatApprovalDate(props.report.date_approved) : '']
  ];

  // Create worksheet
  const ws = XLSX.utils.aoa_to_sheet(structuredData);

  // Set column widths
  ws['!cols'] = [
    { wch: 25 }, { wch: 20 }, { wch: 25 }, { wch: 20 }, { wch: 25 },
    { wch: 20 }, { wch: 15 }, { wch: 15 }, { wch: 20 }, { wch: 20 }
  ];

  // Create workbook and add the worksheet
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'GAD Report FY ' + currentYear.value);

  // Generate filename with current year
  const filename = `Barangay_GAD_Accomplishment_Report_FY${currentYear.value}.xlsx`;
  XLSX.writeFile(wb, filename);
}

function openComments() {
  // Comments functionality can be implemented here
  console.log('Comments clicked');
}

function printpreview() {
  // Create a new window for printing only the modal content
  const printWindow = window.open('', '_blank', 'width=800,height=600');

  if (!printWindow) {
    alert('Please allow popups to enable printing');
    return;
  }

  // Get the print content
  const printContent = generatePrintContent();

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>GAD Accomplishment Report FY ${currentYear.value}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          font-size: 12px;
        }

        .header {
          text-align: center;
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 20px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }

        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
          vertical-align: top;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .text-right {
          text-align: right;
        }

        .font-bold {
          font-weight: bold;
        }

        .bg-emerald-700 {
          background-color: #047857;
          color: white;
        }

        .signature-section {
          margin-top: 20px;
        }

        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `);

  printWindow.document.close();

  // Wait for content to load, then print
  printWindow.onload = function() {
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
}

function generatePrintContent() {
  const currentYearValue = currentYear.value;
  const userName = props.report?.user_name || 'UNKNOWN USER';

  return `
    <div class="header">
      BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) ACCOMPLISHMENT REPORT FY ${currentYearValue}
    </div>

    <!-- Main Report Table -->
    <table>
      <thead>
        <tr>
          <th>Gender Issues or GAD Mandate</th>
          <th>GAD Objective</th>
          <th>Relevant LGU Program or Project</th>
          <th>GAD Activity</th>
          <th>Performance Indicator and Target</th>
          <th>Actual Results</th>
          <th>Approved GAD Budget</th>
          <th>Actual Cost or Expenditure</th>
          <th>Variance or Remarks</th>
          <th>Supporting Documents</th>
        </tr>
        <tr>
          <th colspan="10" class="font-bold">${props.report?.focused || 'CLIENT-FOCUSED'}</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="10" class="font-bold">${props.report?.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : 'Gender Issue'}</td>
        </tr>
        <tr>
          <td>${props.report?.title || '-'}</td>
          <td>${props.report?.gadObjective || '-'}</td>
          <td>${props.report?.lguPpa || '-'}</td>
          <td>${props.report?.gadActivity || '-'}</td>
          <td>${props.report?.performanceIndicator || '-'}</td>
          <td>${props.report?.actualResults || '-'}</td>
          <td class="text-right font-bold">${props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-'}</td>
          <td class="text-right">${props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-'}</td>
          <td>${props.report?.remarks || '-'}</td>
          <td>${getAttachmentDisplayText()}</td>
        </tr>
        <tr class="bg-emerald-700">
          <td colspan="6" class="font-bold">GRAND TOTAL</td>
          <td class="text-right font-bold">${props.report?.approvedBudget ? props.report.approvedBudget.toLocaleString() : '-'}</td>
          <td class="text-right">${props.report?.actualCost ? props.report.actualCost.toLocaleString() : '-'}</td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>

    <!-- Signature Section -->
    <table class="signature-section">
      <tr>
        <td colspan="3" style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Prepared by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${userName}</div>
          <div style="font-size: 10px;">Barangay Gad Focal</div>
        </td>
        <td colspan="3" style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Approved by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">Punong Barangay</div>
        </td>
        <td colspan="4" style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
      <tr>
        <td colspan="6" style="width: 66%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Verified and Endorsed by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.report?.status === 'approved' && props.report?.admin_approver ? props.report.admin_approver : '_________________'}</div>
          <div style="font-size: 10px;">Chairperson, GFPS TWG/secretary</div>
        </td>
        <td colspan="3" style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date Verified:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.report?.status === 'approved' && props.report?.date_approved ? formatApprovalDate(props.report.date_approved) : '_________________'}</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
    </table>
  `;
}

function getAttachmentDisplayText(): string {
  if (attachmentFile.value) {
    return truncateFilename((attachmentFile.value as any).name, 30);
  }

  if (props.report?.attachment_path) {
    const paths = getAttachmentPaths(props.report.attachment_path);
    return paths.map(path => getAttachmentName(path)).join(', ');
  }

  return 'No file(s) attached';
}
</script>

<style scoped>
.bg-pink-200 { background-color: #e9b7e7 !important; }
.bg-yellow-200 { background-color: #fff9c4 !important; }
.bg-blue-700 { background-color: #1976d2 !important; }
</style>
















