<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;

class SettingController extends Controller
{
    // Get the budget plan deadline
    public function getDeadline()
    {
        $setting = Setting::where('key', 'gad_budget_plan_deadline')->first();
        return response()->json([
            'success' => true,
            'deadline' => $setting ? $setting->value : null
        ]);
    }

    // Set the budget plan deadline
    public function setDeadline(Request $request)
    {
        $request->validate([
            'deadline' => 'required|date',
        ]);
        $setting = Setting::updateOrCreate(
            ['key' => 'gad_budget_plan_deadline'],
            ['value' => $request->deadline]
        );
        return response()->json([
            'success' => true,
            'deadline' => $setting->value
        ]);
    }

    // Get the accomplishment report deadline
    public function getAccomplishmentDeadline()
    {
        $setting = Setting::where('key', 'gad_accomplishment_deadline')->first();
        return response()->json([
            'success' => true,
            'deadline' => $setting ? $setting->value : null
        ]);
    }

    // Set the accomplishment report deadline
    public function setAccomplishmentDeadline(Request $request)
    {
        $request->validate([
            'deadline' => 'required|date',
        ]);
        $setting = Setting::updateOrCreate(
            ['key' => 'gad_accomplishment_deadline'],
            ['value' => $request->deadline]
        );
        return response()->json([
            'success' => true,
            'deadline' => $setting->value
        ]);
    }
}
