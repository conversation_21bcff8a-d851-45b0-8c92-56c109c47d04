<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Ready-to-use admin user
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('adminpassword'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );
        
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => ' User',
                'password' => bcrypt('anotherpassword'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );
        
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ErvinAdmin User',
                'password' => bcrypt('<EMAIL>'),
                'role' => 'barangay',
                'email_verified_at' => now(),
            ]
        );
    }
}
