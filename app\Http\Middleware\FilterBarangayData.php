<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FilterBarangayData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }
        
        // Add user info to the request for controllers to use
        $request->attributes->add([
            'user_id' => $user->id,
            'user_barangay_id' => $user->barangay_id,
            'user_barangay' => $user->barangay,
            'user_role' => $user->role
        ]);
        
        // Log user access for debugging
        \Log::info('User accessing data', [
            'user_id' => $user->id,
            'barangay_id' => $user->barangay_id,
            'barangay' => $user->barangay,
            'role' => $user->role,
            'url' => $request->url(),
            'method' => $request->method()
        ]);
        
        return $next($request);
    }
}

