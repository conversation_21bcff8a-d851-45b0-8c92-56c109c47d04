<template>
  <div class="space-y-8">
    <!-- Summary Cards -->

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Location Card -->
      <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200 dark:bg-gray-800 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
        <div class="flex flex-col space-y-4">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
              </svg>
            </div>
            <h2 class="text-sm font-medium text-emerald-800 dark:text-emerald-200">Location Information</h2>
          </div>

          <div class="pl-11 space-y-2">
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Barangay:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userBarangay }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">City:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userCity }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Province:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userProvince }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Region:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userRegion }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- User Card -->
      <div class="bg-white rounded-lg shadow-md p-6 border border-teal-200 dark:border-teal-800 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="text-sm font-medium text-teal-800 dark:text-teal-200">User Information</h2>
        </div>
        <div class="pl-11 space-y-2">
          <div class="flex">
            <span class="text-xs text-teal-700 dark:text-teal-300 w-20">Name:</span>
            <span class="text-xs font-medium text-teal-900 dark:text-teal-100">{{ getUserFullName() }}</span>
          </div>
          <div class="flex">
            <span class="text-xs text-teal-700 dark:text-teal-300 w-20">Role:</span>
            <span class="text-xs font-medium text-teal-900 dark:text-teal-100">Barangay Gad Focal</span>
          </div>
        </div>
      </div>

      <!-- Budget Card -->
            <!-- Budget Card -->
      <div class="bg-white rounded-lg shadow-md p-5 border border-green-300 dark:border-green-700 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center mb-3">
          <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="text-sm font-medium text-green-800 dark:text-green-200">Budget Information</h2>
        </div>
        <div class="pl-10">
          <div class="flex items-center">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Annual Barangay Budget:</span>
            <span class="ml-2 text-sm font-bold text-green-600 dark:text-green-400">PHP {{ displayTotalBudget.toLocaleString() }}</span>
          </div>
          <div class="flex items-center mt-1">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Fiscal Year:</span>
            <div ref="yearPickerRef" class="ml-2 relative inline-block">
              <button
                type="button"
                class="text-xs font-medium text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 min-w-[4.5rem] text-left flex items-center justify-between gap-1"
                @click.stop="yearPickerOpen = !yearPickerOpen"
              >
                <span>{{ selectedFiscalYear }}</span>
                <svg class="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
              </button>

              <div v-if="yearPickerOpen" class="absolute z-50 mt-1 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2">
                <div class="flex items-center justify-between mb-2">
                  <button class="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700" @click.stop="prevYearPage" aria-label="Previous years">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                  </button>
                  <div class="text-xs font-semibold text-gray-700 dark:text-gray-200">{{ yearPageStart }} - {{ yearPageStart + 11 }}</div>
                  <button class="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700" @click.stop="nextYearPage" aria-label="Next years">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                  </button>
                </div>
                <div class="grid grid-cols-4 gap-1">
                  <button
                    v-for="y in yearGrid"
                    :key="y"
                    type="button"
                    class="text-xs px-2 py-1 rounded border transition-colors"
                    :class="[
                      y === selectedFiscalYear ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700'
                    ]"
                    @click.stop="selectYear(y)"
                  >
                    {{ y }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mt-1">
            <span class="text-xs font-medium text-red-500 dark:text-gray-300">REQUIREMENTS BEFORE SUBMITTING THE GPB IS MINIMUM OF 5% OF YOUR ANNUAL BUDGET</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Budget Plans Table -->
    <BarangayBudgetPlanModal :show="showModal" :plan="selectedPlan" @close="showModal = false" />

    <!-- Budget Plans Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 mb-6">
      <!-- Table header -->
      <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 dark:from-emerald-700 dark:to-emerald-800 px-4 py-3">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div>
            <h2 class="text-white text-lg font-semibold">Budget Plans</h2>
          </div>

          <div class="relative">
            <input
              type="text"
              placeholder="Search budget plans..."
              v-model="searchQuery"
              class="pl-8 pr-3 py-1.5 text-sm rounded-md border border-emerald-400 bg-white/90 text-emerald-800 placeholder-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-500 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-gray-50 dark:bg-gray-800/50 px-4 py-2.5 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center gap-2">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Filter by:</span>

          <!-- Status Filter Dropdown -->
          <div class="relative">
            <button
              @click="toggleStatusDropdown"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200 flex items-center gap-1',
                statusFilter
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              {{ statusFilter || 'All Status' }}
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Dropdown Menu -->
            <div v-if="showStatusDropdown" class="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 rounded shadow-lg border border-gray-200 dark:border-gray-700 z-50 min-w-[120px]">
              <button
                @click="selectStatus('')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t"
              >
                All Status
              </button>
              <button
                @click="selectStatus('Pending')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Pending
              </button>
              <button
                @click="selectStatus('Revision')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Revision
              </button>
              <button
                @click="selectStatus('Approved')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Approved
              </button>
              <button
                @click="selectStatus('Draft')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-b"
              >
                Draft
              </button>
              <button
                @click="selectStatus('Pending Revision')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Pending Revision
              </button>
            </div>
          </div>

          <select
            v-model="focusedFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Focused</option>
            <option value="CLIENT-FOCUSED">Client-Focused</option>
            <option value="ORGANIZATION-FOCUSED">Organization-Focused</option>
          </select>

          <select
            v-model="issueFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Issues or Mandate</option>
            <option value="Gender Issue">Gender Issue</option>
            <option value="GAD Mandate">GAD Mandate</option>
          </select>
        </div>
      </div>

      <div class="overflow-y-auto max-h-[400px] py-2">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
            <tr>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Gender Issue</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">GAD Mandate</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Focused</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Status</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Date submitted</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Date Approved</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Total gad Budget</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Fiscal Year</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="(plan, idx) in filteredPlans" :key="plan.id"
                :class="idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'"
                class="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <!-- Gender Issue column -->
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">
                <span v-if="plan.gender_issue === 'Gender Issue' || plan.genderIssue === 'Gender Issue'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  Gender Issue
                </span>
              </td>
              <!-- GAD Mandate column -->
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">
                <span v-if="plan.gender_issue === 'GAD Mandate' || plan.genderIssue === 'GAD Mandate'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  GAD Mandate
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">{{ plan.focused || '-' }}</td>
              <td class="px-3 py-2.5">
                <span :class="getStatusBadgeClass(plan.status)"
                  class="px-3 py-1.5 text-xs font-semibold rounded-md border min-w-[110px] min-h-[32px] flex items-center justify-center whitespace-nowrap">
                  {{ plan.status || 'Pending' }}
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">{{ formatDate(plan.created_at) }}</td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">
                {{ plan.date_approved ? formatDate(plan.date_approved) : (plan.status === 'Approved' ? formatDate(plan.updated_at) : '') }}
              </td>
              <td class="px-3 py-2.5 text-sm whitespace-nowrap">
                <div :class="getCombinedBudget(plan) >= displayTotalBudget * 0.05 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" class="font-medium">
                  {{ formatCurrency(getCombinedBudget(plan)) }}
                  <span class="ml-1 text-xs font-normal opacity-75">
                    ({{ displayTotalBudget > 0 ? Math.round((getCombinedBudget(plan) / displayTotalBudget) * 100) : 0 }}%)
                  </span>
                </div>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">
                {{ plan.fiscal_year || new Date().getFullYear() }}
              </td>
              <td class="px-3 py-2.5">
                <!-- Clean action buttons -->
                <div class="flex flex-nowrap gap-1.5 whitespace-nowrap">
                  <!-- View button (always visible) -->
                  <button
                    class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors relative"
                    @click="openViewModal(plan)"
                  >
                    <!-- Red dot for unread -->
                    <span v-if="!isPlanRead(plan.id)" class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white"></span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View
                  </button>

                  <!-- More options button (for draft status) -->
                  <div v-if="plan.status === 'draft' || plan.status === 'Draft' || plan.is_draft === true || plan.status === 'Revision' || plan.status === 'revision'" class="relative">
                    <button
                      class="dropdown-container inline-flex items-center justify-center gap-1 px-2.5 py-1.5 text-xs font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-md transition-colors min-w-[70px] min-h-[32px]"
                      @click="toggleMoreOptions(plan.id, $event)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                      </svg>
                    </button>

                    <!-- Dropdown menu rendered via Teleport -->
                    <Teleport to="body" v-if="activeMoreOptions === plan.id && dropdownPosition">
                      <div
                        class="dropdown-container absolute bg-white dark:bg-gray-800 rounded-md shadow-lg z-[9999] border border-gray-200 dark:border-gray-600 w-36"
                        :style="{ position: 'absolute', top: dropdownPosition.top + 'px', left: dropdownPosition.left + 'px' }"
                      >
                        <div class="py-1">
                          <button
                            class="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                            @click="editPlan(plan)"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                          </button>
                          <button
                            class="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                            @click="submitPlan(plan)"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Submit
                          </button>
                          <button
                            v-if="plan.status === 'draft' || plan.status === 'Draft' || plan.is_draft === true"
                            class="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                            @click="deletePlan(plan)"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete
                          </button>
                        </div>
                      </div>
                    </Teleport>
                  </div>
                </div>
              </td>
            </tr>
            <!-- Clean empty state -->
            <tr v-if="!filteredPlans.length">
              <td colspan="9" class="px-3 py-8 text-center">
                <div class="flex flex-col items-center">
                  <div class="rounded-full bg-emerald-50 dark:bg-emerald-900/30 p-3 mb-3 border border-emerald-200 dark:border-emerald-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p class="text-sm font-medium text-emerald-700 dark:text-emerald-300">No budget plans found</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Budget plans will appear here once created</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modal for delete confirmation and notifications (copied from BudgetPlan.vue) -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Confirm Icon -->
            <svg v-else-if="modalType === 'confirm'" class="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button
            v-if="modalType === 'confirm'"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            @click="modalType === 'confirm' ? confirmAction() : closeModal()"
            class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
          >
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { usePage } from '@inertiajs/vue3';
import BarangayBudgetPlanModal from './BarangayBudgetPlanModal.vue';
import axios from 'axios';

interface User {
  barangay: string;
  region: string;
  province: string;
  city: string;
  name: string;
  middle_name?: string;
  last_name?: string;
  suffix?: string;
  // add other user properties if needed
}

interface PageProps {
  auth: {
    user: User;
  };
}

const page = usePage();
const props = defineProps<{
  plans: any[]
}>();

const emit = defineEmits(['edit', 'delete', 'submit']);

// Add this computed property to access user data
const userBarangay = computed(() => (page.props as any).auth.user.barangay);
const userRegion = computed(() => (page.props as any).auth.user.region);
const userProvince = computed(() => (page.props as any).auth.user.province);
const userCity = computed(() => (page.props as any).auth.user.city);
const userName = computed(() => (page.props as any).auth.user.name);
const showModal = ref(false);
const selectedPlan = ref<any>(null);

// Add this new ref for tracking which dropdown is open
const activeMoreOptions = ref<number | null>(null);
// Add this ref for dropdown position
const dropdownPosition = ref<{ top: number; left: number } | null>(null);

// Modal state for delete confirmation and notifications (same as parent)
const modalVisible = ref(false)
const modalTitle = ref('')
const modalMessage = ref('')
const modalType = ref<'info' | 'error' | 'success' | 'confirm'>('info')
let modalAction: (() => void) | null = null

function showModalAlert(title: string, message: string, type: 'info' | 'error' | 'success' | 'confirm', action?: () => void) {
  modalTitle.value = title
  modalMessage.value = message
  modalType.value = type
  modalVisible.value = true
  modalAction = action || null
}
function closeModal() {
  modalVisible.value = false
  modalAction = null
}
function confirmAction() {
  if (modalAction) {
    modalVisible.value = false
    modalAction()
    modalAction = null
  }
}

// Annual Barangay Budget with Fiscal Year filter
const totalBudget = ref(0);
const selectedFiscalYear = ref<number>(new Date().getFullYear());
const availableFiscalYears = computed<number[]>(() => {
  const years = new Set<number>();
  (props.plans || []).forEach((p: any) => {
    const y = Number(p.fiscal_year ?? p.fiscalYear);
    if (y) years.add(y);
  });
  if (years.size === 0) years.add(new Date().getFullYear());
  return Array.from(years).sort((a, b) => b - a);
});

// Year picker popover state
const yearPickerOpen = ref(false);
const yearPickerRef = ref<HTMLElement | null>(null);
const yearPageStart = ref<number>(selectedFiscalYear.value - 6);
const yearGrid = computed<number[]>(() => Array.from({ length: 12 }, (_, i) => yearPageStart.value + i));

function prevYearPage() {
  yearPageStart.value -= 12;
}
function nextYearPage() {
  yearPageStart.value += 12;
}
function selectYear(y: number) {
  selectedFiscalYear.value = y;
  // Keep the selected year centered in the page range
  yearPageStart.value = y - 6;
  yearPickerOpen.value = false;
}

// Close picker when clicking outside
onMounted(() => {
  document.addEventListener('click', (e: MouseEvent) => {
    const target = e.target as Node;
    if (yearPickerOpen.value && yearPickerRef.value && !yearPickerRef.value.contains(target)) {
      yearPickerOpen.value = false;
    }
  });
});

const displayTotalBudget = computed(() => {
  // Prefer latest plan's total_gad_budget for the selected fiscal year
  const plansForYear = [...(sortedPlans.value || [])].filter((p: any) => Number(p.fiscal_year ?? p.fiscalYear) === selectedFiscalYear.value);
  const latestForYear = plansForYear.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
  const backendTotal = latestForYear && (latestForYear.total_gad_budget ?? latestForYear.totalGadBudget);
  if (backendTotal && Number(backendTotal) > 0) return Number(backendTotal);

  // Fallback: year-specific localStorage only; if not found, show 0
  try {
    const yearKey = `gad_total_budget_${selectedFiscalYear.value}`;
    const storedYear = localStorage.getItem(yearKey);
    if (storedYear && Number(storedYear) > 0) return Number(storedYear);
  } catch {}
  return 0;
});
onMounted(() => {
  const storedBudget = localStorage.getItem('gad_total_budget');
  totalBudget.value = storedBudget ? Number(storedBudget) : 12000000;
  loadReadPlanIds();
  // Default fiscal year to the most recent available
  if (availableFiscalYears.value.length) {
    selectedFiscalYear.value = availableFiscalYears.value[0];
    yearPageStart.value = selectedFiscalYear.value - 6;
  }
});

// Compute total budget from all plans (if needed elsewhere)
const plansTotalBudget = computed(() => {
  return props.plans.reduce((sum, plan) => sum + (plan.totalBudget || 0), 0);
});

// In the script section, update the sortedPlans computed property to sort by created_at descending
const sortedPlans = computed(() => {
  return [...props.plans].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
});

// Filter variables
const statusFilter = ref('');
const focusedFilter = ref('');
const issueFilter = ref('');
const searchQuery = ref('');

// Dropdown state
const showStatusDropdown = ref(false);

const filteredPlans = computed(() => {
  if (!sortedPlans.value || sortedPlans.value.length === 0) {
    return [];
  }

  return sortedPlans.value.filter(plan => {
    // Search filter - improved to search through multiple fields
    const searchMatch = !searchQuery.value || (() => {
      const query = searchQuery.value.toLowerCase();
      const searchableFields = [
        // Basic info fields
        plan.title_desc,
        plan.titleDesc,
        plan.focused,
        plan.status,
        plan.gender_issue,
        plan.genderIssue,
        // Additional searchable terms
        plan.remarks,
        plan.description,
        plan.gad_objective,
        plan.gadObjective,
        plan.gad_activity,
        plan.gadActivity
      ];

      return searchableFields.some(field =>
        field && field.toString().toLowerCase().includes(query)
      );
    })();

    // Status filter
    const statusMatch = !statusFilter.value || plan.status === statusFilter.value;

    // Focused filter
    const focusedMatch = !focusedFilter.value ||
      (plan.focused && plan.focused.toUpperCase() === focusedFilter.value);

    // Issue filter
    const issueMatch = !issueFilter.value ||
      plan.gender_issue === issueFilter.value ||
      plan.genderIssue === issueFilter.value;

    // Fiscal year filter (strict match to selected year)
    const fiscalYear = Number(plan.fiscal_year ?? plan.fiscalYear);
    const fiscalYearMatch = !selectedFiscalYear.value || fiscalYear === selectedFiscalYear.value;

    return searchMatch && statusMatch && focusedMatch && issueMatch && fiscalYearMatch;
  });
});

// Clear all filters
function clearFilters() {
  searchQuery.value = '';
  focusedFilter.value = '';
  issueFilter.value = '';
  statusFilter.value = '';
}

// Dropdown functions
function toggleStatusDropdown() {
  showStatusDropdown.value = !showStatusDropdown.value;
}

function selectStatus(status: string) {
  statusFilter.value = status;
  showStatusDropdown.value = false;
}

// --- Read/Unread logic for plans ---
const readPlanIds = ref<number[]>([]);

function loadReadPlanIds() {
  const stored = localStorage.getItem('barangayBudgetReadPlanIds');
  readPlanIds.value = stored ? JSON.parse(stored) : [];
}

function saveReadPlanIds() {
  localStorage.setItem('barangayBudgetReadPlanIds', JSON.stringify(readPlanIds.value));
}

function isPlanRead(planId: number | undefined) {
  if (!planId) return false;
  return readPlanIds.value.includes(planId);
}

function markPlanAsRead(planId: number | undefined) {
  if (!planId) return;
  if (!readPlanIds.value.includes(planId)) {
    readPlanIds.value.push(planId);
    saveReadPlanIds();
  }
}
// Expose isPlanRead for debugging
// @ts-ignore
window.isPlanRead = isPlanRead;
// --- End read/unread logic ---

function openViewModal(plan: any) {
  // Make sure we're passing the complete plan object
  console.log("Selected plan:", plan); // Add this for debugging

  // Create a deep copy of the plan to ensure all properties are included
  selectedPlan.value = JSON.parse(JSON.stringify(plan));
  showModal.value = true;
  markPlanAsRead(plan.id); // Mark as read when viewing
}

function editPlan(plan: any) {
  // Create a deep copy of the plan to ensure all properties are preserved
  const planCopy = JSON.parse(JSON.stringify(plan));

  // Map all snake_case properties to camelCase for the form
  const mappings = {
    'gender_issue': 'genderIssue',
    'title_desc': 'titleDesc',
    'supporting_stats': 'supportingStats',
    'source_stats': 'sourceStats',
    'ppa_si': 'ppaSi',
    'gad_objective': 'gadObjective',
    'lgu_program': 'lguProgram',
    'gad_activity': 'gadActivity',
    'date_implementation_start': 'dateImplementationStart',
    'date_implementation_end': 'dateImplementationEnd',
    'performance_target': 'performanceTarget',
    'performance_indicator': 'performanceIndicator',
    'lead_office': 'leadOffice',
    'total_budget': 'totalBudget',
    'responsible_office': 'responsibleOffice'
  };

  // Convert all snake_case properties to camelCase
  Object.entries(mappings).forEach(([snakeCase, camelCase]) => {
    if (plan[snakeCase] !== undefined && planCopy[camelCase] === undefined) {
      planCopy[camelCase] = plan[snakeCase];
    }
  });

  // Ensure numeric values are properly formatted
  ['mooe', 'ps', 'co', 'totalBudget'].forEach(field => {
    if (planCopy[field]) {
      planCopy[field] = Number(planCopy[field]);
    }
  });

  // Handle arrays that might be stored as JSON strings
  ['ppaSi', 'activity'].forEach(field => {
    if (typeof planCopy[field] === 'string' && planCopy[field].startsWith('[')) {
      try {
        planCopy[field] = JSON.parse(planCopy[field]);
      } catch (e) {
        console.error(`Failed to parse ${field}:`, e);
      }
    }
  });

  // Handle attachments
  planCopy.attachments = []; // Initialize empty array for new attachments

  // Set up existing attachments if available
  if (plan.attachment_path) {
    try {
      // Check if it's a JSON array
      if (typeof plan.attachment_path === 'string' && plan.attachment_path.startsWith('[')) {
        planCopy.existingAttachments = JSON.parse(plan.attachment_path);
      } else {
        // Single path
        planCopy.existingAttachments = [plan.attachment_path];
      }
    } catch (e) {
      console.error('Error parsing attachment paths:', e);
      planCopy.existingAttachments = [];
    }
  } else {
    planCopy.existingAttachments = [];
  }

  // Initialize empty array for attachments to remove
  planCopy.removeAttachments = [];

  console.log('Emitting plan for edit:', planCopy);
  emit('edit', planCopy);
}

function deletePlan(plan: any) {
  showModalAlert(
    'Delete Budget Plan',
    `Are you sure you want to delete the budget plan "${plan.title_desc || plan.titleDesc || 'Untitled Plan'}"?`,
    'confirm',
    () => {
      axios.delete(`/api/budget-plans/${plan.id}`)
        .then(response => {
          if (response.data.success) {
            const index = props.plans.findIndex(p => p.id === plan.id);
            if (index !== -1) {
              props.plans.splice(index, 1);
            }
            activeMoreOptions.value = null;
            dropdownPosition.value = null;
            // emit('delete', plan); // REMOVE this emit so parent does not show its modal
            showModalAlert('Deleted', 'Budget plan draft deleted successfully.', 'success');
          } else {
            throw new Error(response.data.message || 'Unknown error');
          }
        })
        .catch(error => {
          if (error.response?.data?.error && error.response.data.error.includes('No query results for model')) {
            const index = props.plans.findIndex(p => p.id === plan.id);
            if (index !== -1) {
              props.plans.splice(index, 1);
            }
            activeMoreOptions.value = null;
            dropdownPosition.value = null;
            // emit('delete', plan); // REMOVE this emit so parent does not show its modal
            showModalAlert('Not Found', 'The budget plan was already deleted or does not exist. The list has been updated.', 'info');
          } else {
            const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || 'Unknown error occurred';
            showModalAlert('Failed', 'Failed to delete budget plan: ' + errorMessage, 'error');
          }
        });
    }
  );
}

// Add this function to display the full name like in the modal
function getUserFullName() {
  const user = (page.props as any).auth.user;

  if (!user) return 'Unknown User';

  let fullName = user.name || '';

  if (user.middle_name) {
    // Add middle initial
    fullName += ' ' + user.middle_name.charAt(0) + '.';
  }

  if (user.last_name) {
    fullName += ' ' + user.last_name;
  }

  if (user.suffix) {
    fullName += ', ' + user.suffix;
  }

  return fullName;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(Math.floor(amount));
}

// Consistent status badge functions for both tables
function getStatusBadgeClass(status: string): string {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';
  const normalizedStatus = (typeof status === 'string' ? status.trim().toLowerCase() : '');
  console.log('Status for badge:', status, 'Normalized:', normalizedStatus); // Debug log
  switch (normalizedStatus) {
    case 'approved':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case 'pending revision':
      return 'bg-amber-100 text-blue-800 border-blue-200';
    case 'revision':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'rejected':
    case 'disapproved':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

function getStatusLabel(status: string): string {
  if (!status) return 'UNKNOWN';

  switch (status.toLowerCase()) {
    case 'approved':
      return 'APPROVED';
    case 'pending':
      return 'PENDING';
    case 'revision':
      return 'REVISION';
    case 'rejected':
      return 'REJECTED';
    case 'disapproved':
      return 'DISAPPROVED';
    case 'draft':
      return 'DRAFT';
    default:
      return status.toUpperCase();
  }
}

function toggleMoreOptions(planId: number, event?: MouseEvent) {
  if (event) {
    event.stopPropagation();
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();
    // 144px = dropdown width (w-36)
    dropdownPosition.value = {
      top: rect.bottom + window.scrollY,
      left: rect.right - 144 + window.scrollX,
    };
  }
  if (activeMoreOptions.value === planId) {
    activeMoreOptions.value = null;
    dropdownPosition.value = null;
  } else {
    activeMoreOptions.value = planId;
  }
}

function submitPlan(plan: any) {
  // Close the dropdown
  activeMoreOptions.value = null;
  dropdownPosition.value = null; // Clear dropdown position

  // Validate required fields before submission
  const requiredFields = [
    { field: 'focused', label: 'Focused' },
    { field: 'gender_issue', label: 'Gender Issue/GAD Mandate', altField: 'genderIssue' },
    { field: 'title_desc', label: 'Title/Description', altField: 'titleDesc' },
    { field: 'gad_objective', label: 'GAD Objective', altField: 'gadObjective' },
    { field: 'lgu_program', label: 'LGU Program', altField: 'lguProgram' },
    { field: 'gad_activity', label: 'GAD Activity', altField: 'gadActivity' },
    { field: 'date_implementation_start', label: 'Implementation Start Date', altField: 'dateImplementationStart' },
    { field: 'date_implementation_end', label: 'Implementation End Date', altField: 'dateImplementationEnd' },
    { field: 'performance_target', label: 'Performance Target', altField: 'performanceTarget' },
    { field: 'lead_office', label: 'Lead Office', altField: 'leadOffice' }
  ];

  const missingFields = requiredFields.filter(field => {
    const value = plan[field.field] || (field.altField ? plan[field.altField] : null);
    return !value || value.trim() === '';
  });

  if (missingFields.length > 0) {
    const missingFieldNames = missingFields.map(f => f.label).join(', ');
    showModalAlert(
      'Missing Required Fields',
      `Please fill in all required fields before submitting: ${missingFieldNames}`,
      'info'
    );
    return;
  }

  // Use modal confirmation instead of browser confirm
  showModalAlert(
    'Submit Budget Plan',
    'Are you sure you want to submit this budget plan?',
    'confirm',
    () => {
      const formData = new FormData();
      Object.keys(plan).forEach(key => {
        // Do not send status for draft, but set to Pending Revision if revising
        if (key !== 'status') {
          formData.append(key, plan[key]);
        }
      });
      formData.set('is_draft', 'false');
      // If plan is in Revision status, set status to Pending Revision
      if (plan.status === 'Revision') {
        formData.set('status', 'Pending Revision');
      }

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Requested-With': 'XMLHttpRequest'
        }
      };

      axios.post(`/api/budget-plans/${plan.id}?_method=PUT`, formData, config)
        .then(response => {
          if (response.data.success) {
            // Update the plan status locally to Pending Revision
            const index = props.plans.findIndex(p => p.id === plan.id);
            if (index !== -1) {
              props.plans[index].status = plan.status === 'Revision' ? 'Pending Revision' : 'Pending';
              props.plans[index].is_draft = false;
            }
            emit('submit', plan);
            showModalAlert('Submitted', 'Budget plan submitted successfully to GAD Admin!', 'success');
          }
        })
        .catch(error => {
          const serverErrors = error.response?.data?.errors
            ? Object.values(error.response.data.errors).flat().join('\n')
            : (error.response?.data?.message || error.message || 'Failed to submit budget plan. Please try again.');
          showModalAlert('Failed', serverErrors, 'error');
        });
    }
  );
}

// Add a click outside handler to close the dropdown when clicking elsewhere
onMounted(() => {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (activeMoreOptions.value !== null && !target.closest('.dropdown-container')) {
      activeMoreOptions.value = null;
      dropdownPosition.value = null;
    }
  });
});

// Helper function to get manual total (MOOE + PS + CO)
function getManualTotal(plan: any): number {
  return (Number(plan.mooe || 0) + Number(plan.ps || 0) + Number(plan.co || 0));
}

// Helper function to get combined budget (LP allocation + manual total)
function getCombinedBudget(plan: any): number {
  const lpAllocation = Number(plan.lp_allocation || 0);
  const manualTotal = getManualTotal(plan);

  // If total_budget is set (from backend calculation), use it
  // Otherwise, calculate as LP allocation + manual total
  return plan.total_budget || (lpAllocation + manualTotal);
}
</script>


