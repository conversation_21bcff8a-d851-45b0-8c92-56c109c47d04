<template>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Submitted</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Budget</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr v-for="plan in plans" :key="plan.id">
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ plan.title || 'Untitled Plan' }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <span :class="getStatusClass(plan.status)">{{ plan.status || 'Pending' }}</span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(plan.dateSubmitted) }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(plan.totalBudget) }}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button @click="$emit('view', plan)" class="text-green-600 hover:text-green-900 mr-3">View</button>
            <button @click="$emit('edit', plan)" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
            <button @click="$emit('delete', plan)" class="text-red-600 hover:text-red-900 mr-3">Delete</button>
            <button @click="$emit('download', plan)" class="text-blue-600 hover:text-blue-900">Download</button>
          </td>
        </tr>
        <tr v-if="plans.length === 0">
          <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No budget plans found</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  plans: {
    type: Array,
    default: () => []
  }
});

defineEmits(['view', 'edit', 'delete', 'download']);

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

function formatCurrency(value) {
  if (!value && value !== 0) return '₱0.00';
  return '₱' + Math.floor(Number(value)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

function getStatusClass(status) {
  if (!status) return 'px-3 py-1.5 text-xs rounded-lg bg-gray-100 text-gray-800 border border-gray-200 font-semibold';

  const statusClasses = {
    'Pending': 'px-3 py-1.5 text-xs rounded-lg bg-amber-100 text-amber-800 border border-amber-200 font-semibold',
    'Approved': 'px-3 py-1.5 text-xs rounded-lg bg-emerald-100 text-emerald-800 border border-emerald-200 font-semibold',
    'Rejected': 'px-3 py-1.5 text-xs rounded-lg bg-red-100 text-red-800 border border-red-200 font-semibold',
    'Revision': 'px-3 py-1.5 text-xs rounded-lg bg-red-100 text-red-800 border border-red-200 font-semibold',
    'draft': 'px-3 py-1.5 text-xs rounded-lg bg-blue-100 text-blue-800 border border-blue-200 font-semibold'
  };

  return statusClasses[status] || 'px-3 py-1.5 text-xs rounded-lg bg-gray-100 text-gray-800 border border-gray-200 font-semibold';
}
</script>
