<script setup lang="ts">
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useInitials } from '@/composables/useInitials';
import type { User } from '@/types';
import { computed } from 'vue';

interface Props {
    user: User;
    showEmail?: boolean;
    showDetails?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showEmail: false,
    showDetails: false,
});

const { getInitials } = useInitials();

// Compute whether we should show the avatar image
const showAvatar = computed(() => props.user.avatar && props.user.avatar !== '');
</script>

<template>
    <div class="flex items-center">
        <Avatar class="h-12 w-12 overflow-hidden rounded-lg">
            <AvatarImage v-if="showAvatar" :src="user.avatar" :alt="user.name || ''" />
            <AvatarFallback class="rounded-lg flex items-center justify-center bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-10 h-10">
                  <circle cx="12" cy="8" r="4" fill="currentColor" fill-opacity=".2"/>
                  <rect x="6" y="14" width="12" height="6" rx="3" fill="currentColor" fill-opacity=".2"/>
                  <circle cx="12" cy="8" r="3" fill="currentColor" />
                  <rect x="8" y="14" width="8" height="4" rx="2" fill="currentColor" />
                </svg>
            </AvatarFallback>
        </Avatar>
        <div v-if="showDetails" class="ml-2 grid flex-1 text-left text-sm leading-tight">
            <span class="truncate font-medium">{{ user.name }}</span>
            <span v-if="showEmail" class="truncate text-xs text-muted-foreground">{{ user.email }}</span>
        </div>
    </div>
</template>
