<template>
  <div class="px-6 py-4">


    <!-- <PERSON> Header -->
    <div class="mb-6">

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
      <div class="bg-white border border-amber-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150">
        <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center border-2 border-amber-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-amber-800 leading-tight truncate">Pending Plans</div>
          <div class="text-xs text-amber-600 leading-tight truncate">Total Pending</div>
          <div class="inline-block mt-1 px-3 py-1 rounded-lg bg-amber-100 text-amber-800 text-xs font-semibold border border-amber-200">{{ getStatusCount('Pending') }}</div>
        </div>
      </div>

      <div class="bg-white border border-blue-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150">
        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center border-2 border-blue-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-blue-800 leading-tight truncate">Revision Plans</div>
          <div class="text-xs text-blue-600 leading-tight truncate">Total Revision</div>
          <div class="inline-block mt-1 px-3 py-1 rounded-lg bg-blue-100 text-blue-800 text-xs font-semibold border border-blue-200">{{ getStatusCount('Revision') }}</div>
        </div>
      </div>

      <div class="bg-white border border-emerald-100 rounded-xl shadow-sm p-4 flex items-center gap-4 cursor-pointer hover:shadow-md transition-shadow duration-150" @click="openSetBudgetDeadlineModal()">
        <div class="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center border-2 border-emerald-200 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="flex flex-col justify-center min-w-0 flex-1">
          <div class="font-semibold text-emerald-800 leading-tight truncate">Submission Deadline</div>
          <div class="text-xs text-emerald-600 leading-tight truncate">Due Date</div>
          <div class="inline-block mt-1 px-2 py-0.5 rounded bg-emerald-100 text-emerald-700 text-xs font-medium">
            {{ budgetPlanDeadline ? formatDeadlineDate(budgetPlanDeadline) : 'Set Now' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Barangay Budgets Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 mb-6">
      <!-- Table header -->
      <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 dark:from-emerald-700 dark:to-emerald-800 px-4 py-3">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div>
            <h2 class="text-white text-lg font-semibold">Barangay Budgets Submission</h2>
          </div>

          <div class="relative">
            <input
              type="text"
              placeholder="Search barangay..."
              v-model="searchQuery"
              class="pl-8 pr-3 py-1.5 text-sm rounded-md border border-emerald-400 bg-white/90 text-emerald-800 placeholder-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-500 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-gray-50 dark:bg-gray-800/50 px-4 py-2.5 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center gap-2">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Filter by:</span>

          <!-- Status Filter Buttons -->
          <div class="flex flex-wrap gap-1.5">
            <button
              @click="statusFilter = ''"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200',
                statusFilter === ''
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              All
            </button>
            <button
              @click="statusFilter = 'Pending'"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200',
                statusFilter === 'Pending'
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              Pending
            </button>
            <button
              @click="statusFilter = 'Revision'"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200',
                statusFilter === 'Revision'
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              Revision
            </button>
            <button
              @click="statusFilter = 'Approved'"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200',
                statusFilter === 'Approved'
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              Approved
            </button>
          </div>
          <select
            v-model="focusedFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Focused</option>
            <option value="CLIENT-FOCUSED">Client-Focused</option>
            <option value="ORGANIZATION-FOCUSED">Organization-Focused</option>
          </select>
          <select
            v-model="issueFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Issues or Mandate</option>
            <option value="Gender Issue">Gender Issue</option>
            <option value="GAD Mandate">GAD Mandate</option>
          </select>
        </div>
      </div>

      <div class="overflow-x-auto max-h-[60vh] py-2">
        <table class="min-w-full">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
            <tr>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Barangay</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Email</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">Focused</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">Gender Issue</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden md:table-cell">GAD Mandate</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Status</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700 hidden sm:table-cell">Date Submitted</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="(row, idx) in filteredBudgetRows"
                :key="row?.id || idx"
                :class="idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'"
                class="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <td class="px-3 py-2.5 text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ row?.barangay_name || 'Unknown Barangay' }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 max-w-[180px] truncate">
                {{ row?.user_email || 'N/A' }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                {{ row?.focused || '' }}
              </td>
               <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell text-left">
                <span v-if="row?.gender_issue === 'Gender Issue'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  Gender Issue
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell text-left">
                <span v-if="row?.gender_issue === 'GAD Mandate'" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  GAD Mandate
                </span>
              </td>
              <td class="px-3 py-2.5">
                <span :class="getStatusBadgeClass(row?.status || '')"
                  class="px-3 py-1.5 text-xs font-semibold rounded-md border min-w-[110px] min-h-[32px] flex items-center justify-center whitespace-nowrap">
                  {{ row?.status || 'Pending' }}
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 hidden sm:table-cell whitespace-nowrap">
                {{ formatDate(row?.created_at) }}
              </td>
              <td class="px-3 py-2.5">
                <button
                  class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors relative"
                  @click="openAdminBudgetPlanModal(row)"
                >
                  <!-- Red dot for unread -->
                  <span v-if="!isPlanRead(row.id)" class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white"></span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Review
                </button>
              </td>
            </tr>
            <tr v-if="filteredBudgetRows.length === 0">
              <td colspan="8" class="px-4 py-16 text-center">
                <div class="flex flex-col items-center">
                  <div class="rounded-full bg-emerald-50 dark:bg-emerald-900/30 p-5 mb-4 border border-emerald-200 dark:border-emerald-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-emerald-700 dark:text-emerald-300">No budget plans found</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 max-w-md">
                    {{ searchQuery || focusedFilter || issueFilter ? 'No results match your search criteria. Try adjusting your filters.' : 'No budget plans with the selected status are available.' }}
                  </p>
                  <button
                    v-if="searchQuery || focusedFilter || issueFilter"
                    @click="clearFilters"
                    class="mt-4 px-4 py-2 text-sm font-medium text-emerald-700 dark:text-emerald-300 bg-emerald-50 dark:bg-emerald-900/30 rounded-md border border-emerald-200 dark:border-emerald-800 hover:bg-emerald-100 dark:hover:bg-emerald-900/50 transition-colors"
                  >
                    Clear Filters
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Set Budget Deadline Modal -->
    <div v-if="showSetBudgetDeadlineModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity" aria-hidden="true" @click="showSetBudgetDeadlineModal = false"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-emerald-100 dark:bg-emerald-900 sm:mx-0 sm:h-10 sm:w-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                  Set Budget Submission Deadline
                </h3>
                <div class="mt-4">
                  <label for="deadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Select Deadline Date and Time
                  </label>
                  <input
                    type="datetime-local"
                    id="deadline"
                    v-model="budgetDeadlineInput"
                    class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  />
                  <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    This deadline will be visible to all barangays for budget submission planning.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-emerald-600 text-base font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:ml-3 sm:w-auto sm:text-sm"
              @click="saveBudgetDeadline"
            >
              Save Deadline
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="showSetBudgetDeadlineModal = false"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
    </div>
    <!-- Admin Budget Plan Modal -->
    <AdminBudgetPlanModalTable
      v-if="showAdminBudgetPlanModal"
      :show="showAdminBudgetPlanModal"
      :plan="selectedBudgetPlan"
      @close="showAdminBudgetPlanModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';
import AppSidebarHeader from '@/components/AppSidebarHeader.vue';
import AdminBudgetPlanModalTable from '@/components/adminbudgetplanmodalTable.vue';

const page = usePage<{ auth: { user: { role: string, id: number, name: string, email: string, barangay?: string, avatar?: string } } }>();

// Budget plan deadline
const budgetPlanDeadline = ref(null);
const showSetBudgetDeadlineModal = ref(false);
const budgetDeadlineInput = ref('');

async function fetchBudgetPlanDeadline() {
  try {
    const response = await axios.get('/api/budget-plan-deadline');
    if (response.data.success) {
      budgetPlanDeadline.value = response.data.deadline;
    }
  } catch (error) {
    budgetPlanDeadline.value = null;
  }
}

function openSetBudgetDeadlineModal() {
  budgetDeadlineInput.value = budgetPlanDeadline.value ? new Date(budgetPlanDeadline.value).toISOString().slice(0, 16) : '';
  showSetBudgetDeadlineModal.value = true;
}

async function saveBudgetDeadline() {
  if (budgetDeadlineInput.value) {
    const isoDeadline = new Date(budgetDeadlineInput.value).toISOString();
    try {
      const response = await axios.post('/api/budget-plan-deadline', { deadline: isoDeadline });
      if (response.data.success) {
        budgetPlanDeadline.value = response.data.deadline;
      }
    } catch (error) {
      // Optionally handle error
    }
  }
  showSetBudgetDeadlineModal.value = false;
}

// Barangay budget rows
interface BarangayBudgetRow {
  id?: number;
  barangay_name?: string;
  user_name?: string;
  user_email?: string;
  gender_issue?: string;
  title_desc?: string;
  status?: string;
  focused?: string;
  created_at?: string;
  remarks?: string;
  description?: string;
}

const barangayBudgetRows = ref<BarangayBudgetRow[]>([]);
const searchQuery = ref('');
const statusFilter = ref(''); // Default to show all
const issueFilter = ref('');
const focusedFilter = ref('');

// Filtered budget rows
const filteredBudgetRows = computed(() => {
  return barangayBudgetRows.value.filter(row => {
    // Search filter - improved to search through multiple fields including status and filters
    const searchMatch = !searchQuery.value || (() => {
      const query = searchQuery.value.toLowerCase();
      const searchableFields = [
        // Basic info fields
        row.barangay_name,
        row.user_name,
        row.user_email,
        row.title_desc,
        // Status field
        row.status,
        // Filter fields
        row.focused,
        row.gender_issue,
        // Additional searchable terms
        row.remarks,
        row.description
      ];

      return searchableFields.some(field =>
        field && field.toString().toLowerCase().includes(query)
      );
    })();

    // Status filter
    const statusMatch = !statusFilter.value || row.status === statusFilter.value;

    // Focused filter
    const focusedMatch = !focusedFilter.value ||
      (row.focused && row.focused.toUpperCase() === focusedFilter.value);

    // Issue filter
    const issueMatch = !issueFilter.value || row.gender_issue === issueFilter.value;

    return searchMatch && statusMatch && focusedMatch && issueMatch;
  });
});

// Clear all filters
function clearFilters() {
  searchQuery.value = '';
  focusedFilter.value = '';
  issueFilter.value = '';
  statusFilter.value = '';
}

// Get count of items by status
function getStatusCount(status: string) {
  return barangayBudgetRows.value.filter(row => row.status === status).length;
}

function getStatusBadgeClass(status: string) {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';
  const normalizedStatus = (typeof status === 'string' ? status.trim().toLowerCase() : '');
  switch (normalizedStatus) {
    case 'approved':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case 'pending revision':
      return 'bg-amber-100 text-blue-800 border-blue-200';
    case 'revision':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'rejected':
    case 'disapproved':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

// Fetch pending submissions
async function fetchPendingSubmissions() {
  try {
    console.log('Fetching pending submissions...');
    const response = await axios.get('/api/budget-plans');
    console.log('API Response:', response.data);

    if (response.data.budgetPlans && response.data.budgetPlans.length > 0) {
      console.log('Number of pending budget plans found:', response.data.budgetPlans.length);

      // Map the plans to the format needed for display
      barangayBudgetRows.value = response.data.budgetPlans.map((plan: any) => {
        return {
          ...plan,
          barangay_name: plan.barangay_name || 'Unknown Barangay',
          user_name: plan.user_name || 'Unknown User',
          user_email: plan.user_email || 'No Email',
          gender_issue: plan.gender_issue || '',
          title_desc: plan.title_desc || 'No Title',
          // Keep original status
          status: plan.status || 'Pending'
        };
      });
    } else {
      console.log('No pending budget plans found');
      barangayBudgetRows.value = [];
    }
  } catch (error) {
    console.error('Exception fetching pending submissions:', error);
    barangayBudgetRows.value = [];
  }
}

function formatDate(date: string | undefined): string {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}
function formatDeadlineDate(dateString: string | undefined) {
  if (!dateString) return 'Set Now';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
}
const showAdminBudgetPlanModal = ref(false);
const selectedBudgetPlan = ref(null);

// --- Read/Unread logic for plans ---
const readPlanIds = ref<number[]>([]);

function loadReadPlanIds() {
  const stored = localStorage.getItem('adminBudgetReadPlanIds');
  readPlanIds.value = stored ? JSON.parse(stored) : [];
}

function saveReadPlanIds() {
  localStorage.setItem('adminBudgetReadPlanIds', JSON.stringify(readPlanIds.value));
}

function isPlanRead(planId: number | undefined) {
  if (!planId) return false;
  return readPlanIds.value.includes(planId);
}

function markPlanAsRead(planId: number | undefined) {
  if (!planId) return;
  if (!readPlanIds.value.includes(planId)) {
    readPlanIds.value.push(planId);
    saveReadPlanIds();
  }
}
// Expose isPlanRead for template
// @ts-ignore
// eslint-disable-next-line
window.isPlanRead = isPlanRead;
// --- End read/unread logic ---

function openAdminBudgetPlanModal(plan: any) {
  selectedBudgetPlan.value = plan;
  showAdminBudgetPlanModal.value = true;
  // Mark as read when opening
  markPlanAsRead(plan.id);
}

onMounted(() => {
  fetchPendingSubmissions();
  fetchBudgetPlanDeadline();
  loadReadPlanIds();
});
</script>

<style scoped>
/* Add subtle animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.bg-white, .bg-gradient-to-r {
  animation: fadeIn 0.3s ease-out;
}

/* Improve table hover effects */
tr:hover td {
  transition: all 0.2s ease;
}

/* Improve button hover effects */
button {
  transition: all 0.2s ease;
}

/* Add subtle shadow to status badges */
[class*="bg-emerald-500"],
[class*="bg-amber-500"],
[class*="bg-red-500"],
[class*="bg-blue-500"] {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
</style>






























