<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Add attachment_path column if it doesn't exist
            if (!Schema::hasColumn('accomplishment_reports', 'attachment_path')) {
                $table->text('attachment_path')->nullable()->after('otherActivity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Drop the column if it exists
            if (Schema::hasColumn('accomplishment_reports', 'attachment_path')) {
                $table->dropColumn('attachment_path');
            }
        });
    }
};
