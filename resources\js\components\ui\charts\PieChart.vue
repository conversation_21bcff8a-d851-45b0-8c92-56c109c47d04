<template>
  <PieChart :chart-data="chartData" :options="chartOptions" />
</template>

<script setup>
import { PieChart } from 'vue-chart-3';
import { Chart, ArcElement, Too<PERSON><PERSON>, Legend, PieController } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(PieController, ArcElement, Tooltip, Legend, ChartDataLabels);

const props = defineProps({
  chartData: { type: Object, required: true },
  chartOptions: { type: Object, default: () => ({ responsive: true, maintainAspectRatio: false }) }
});
</script>

<style scoped>
:deep(canvas) {
  width: 100% !important;
  height: 250px !important;
}
</style>