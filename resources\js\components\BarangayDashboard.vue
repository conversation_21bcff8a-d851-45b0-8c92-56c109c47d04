<template>
  <div class="flex-1 overflow-y-auto">
    <!-- Dashboard Title -->

    <!-- Stats Cards Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 p-3 sm:p-4 lg:p-6">
      <!-- Budget Plans Card -->
      <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="$emit('switch-tab', 'budget')">
        <!-- Hover overlay -->
        <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Content -->
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <rect width="18" height="18" x="3" y="3" rx="2"/>
                  <path d="M9 3v18"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Budget Plans</h3>
                <p class="text-xs text-gray-500">Manage budget plans</p>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-emerald-600">{{ budgetPlansCount }}</div>
              <div class="text-xs text-gray-500">Total Plans</div>
            </div>
          </div>

          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-500">{{ submittedPlansCount }} submitted</span>
            <span class="text-emerald-600 font-medium">View Plans →</span>
          </div>
        </div>
      </div>

      <!-- Accomplishment Reports Card -->
      <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1" @click="$emit('switch-tab', 'accomplishment')">
        <!-- Hover overlay -->
        <div class="absolute inset-0 bg-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Content -->
        <div class="relative p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path d="M4 19.5V5a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v14.5M4 19.5l4-4 4 4 4-4"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Accomplishment Reports</h3>
                <p class="text-xs text-gray-500">Manage accomplishments reports</p>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-emerald-600">{{ reportsCount }}</div>
              <div class="text-xs text-gray-500">Total Reports</div>
            </div>
          </div>

          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-500">{{ submittedReportsCount }} submitted</span>
            <span class="text-emerald-600 font-medium">View Reports →</span>
          </div>
        </div>
      </div>

      <!-- Budget Utilization Card -->
      <div class="group relative bg-white border border-emerald-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-visible">
        <!-- Content -->
        <div class="relative p-4">
          <!-- Fiscal Year Filter -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-2">
              <label class="text-xs font-medium text-gray-700">Fiscal Year:</label>
              <div ref="yearPickerRef" class="ml-2 relative inline-block">
                <button
                  type="button"
                  class="text-xs font-medium text-gray-900 bg-white border border-emerald-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-emerald-500 min-w-[4.5rem] text-left flex items-center justify-between gap-1"
                  @click.stop="yearPickerOpen = !yearPickerOpen"
                >
                  <span>{{ selectedFiscalYear }}</span>
                  <svg class="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                </button>

                <div v-if="yearPickerOpen" class="absolute z-50 mt-1 w-56 bg-white rounded-lg shadow-lg border border-gray-200 p-2 left-1/2 -translate-x-1/2 transform max-h-64 overflow-auto">
                  <div class="flex items-center justify-between mb-2">
                    <button class="p-1 rounded hover:bg-gray-100" @click.stop="prevYearPage" aria-label="Previous years">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <div class="text-xs font-semibold text-gray-700">{{ yearPageStart }} - {{ yearPageStart + 11 }}</div>
                    <button class="p-1 rounded hover:bg-gray-100" @click.stop="nextYearPage" aria-label="Next years">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                    </button>
                  </div>
                  <div class="grid grid-cols-4 gap-1">
                    <button
                      v-for="y in yearGrid"
                      :key="y"
                      type="button"
                      class="text-xs px-2 py-1 rounded border transition-colors"
                      :class="[
                        y === selectedFiscalYear ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'
                      ]"
                      @click.stop="selectYear(y)"
                    >
                      {{ y }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M12 6v6l4 2"/>
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900">Budget Status</h3>
                <p class="text-xs text-gray-500">FY {{ selectedFiscalYear }} utilization</p>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-emerald-600">{{ budgetUtilization }}%</div>
              <div class="text-xs text-gray-500">Utilized</div>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div class="bg-emerald-500 h-2 rounded-full transition-all duration-300" :style="{ width: budgetUtilization + '%' }"></div>
          </div>

          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-500">{{ formatCurrency(totalAllocated) }} allocated</span>
            <span class="text-emerald-600 font-medium">{{ formatCurrency(totalBudget) }} total</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Visualizations -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 px-3 sm:px-4 lg:px-6 mb-6">
      <!-- Activity Status Breakdown -->
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3 rounded-t-lg -mx-4 -mt-4 mb-4">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 class="text-sm font-bold text-white">Activity Status Overview</h3>
              <p class="text-emerald-100 text-xs">Fiscal Year {{ selectedPieYear }}</p>
            </div>
            <div class="flex gap-1">
              <button
                :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', selectedPieType === 'budget' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                @click="selectedPieType = 'budget'"
              >Budget Plan</button>
              <button
                :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', selectedPieType === 'accomplishment' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                @click="selectedPieType = 'accomplishment'"
              >Accomplishment</button>
            </div>
            <div>
              <select v-model="selectedPieYear" class="text-xs border border-emerald-200 rounded-md px-2 py-1 bg-white focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500">
                <option v-for="year in fiscalYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="flex flex-col lg:flex-row gap-4 items-center">
          <div class="flex-1 flex justify-center w-full">
            <PieChart
              :chart-data="activityStatusChartData"
              :chart-options="activityStatusPieChartOptions"
              class="w-full max-w-xs sm:max-w-sm lg:max-w-md"
            />
          </div>
          <div class="flex flex-col gap-2 flex-1 w-full lg:w-auto mt-4 lg:mt-0">
            <div class="flex flex-col gap-2">
              <div
                v-for="(status, idx) in activityStatusLegendData"
                :key="status.label"
                class="flex items-center gap-2 text-sm"
              >
                <span class="w-3 h-3 rounded-full border border-emerald-200" :style="{ background: status.color }"></span>
                <span class="font-medium text-emerald-700 flex-1">{{ status.label }}</span>
                <span class="font-semibold text-emerald-600">{{ status.value }}</span>
              </div>
            </div>
            <div class="mt-3 text-xs text-emerald-800 bg-emerald-50 rounded p-2 sm:p-3 font-medium">
              {{ activityStatusSummary }}
            </div>
          </div>
        </div>
      </div>
      <!-- Budget Plan Distribution by Category -->
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3 rounded-t-lg -mx-4 -mt-4 mb-4">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 class="text-sm font-bold text-white">Plan And Report Category Overview</h3>
              <p class="text-emerald-100 text-xs">Fiscal Year {{ selectedBarYear }}</p>
            </div>
            <div class="flex gap-1">
              <button
                :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', selectedBarType === 'budget' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                @click="selectedBarType = 'budget'"
              >Budget Plan</button>
              <button
                :class="['px-2 py-1 rounded text-xs font-semibold transition-all duration-200', selectedBarType === 'accomplishment' ? 'bg-white text-emerald-600 shadow-md' : 'bg-emerald-400/30 text-white hover:bg-emerald-400/50']"
                @click="selectedBarType = 'accomplishment'"
              >Accomplishment</button>
            </div>
            <div>
              <select v-model="selectedBarYear" class="text-xs border border-emerald-200 rounded-md px-2 py-1 bg-white focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500">
                <option v-for="year in fiscalYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="flex flex-1 justify-center w-full">
          <BarChart :chart-data="budgetCategoryChartData" :chart-options="barChartOptions" class="w-full max-w-xs sm:max-w-sm lg:max-w-md" />
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="px-3 sm:px-4 lg:px-6 pb-6">
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button
            @click="$emit('switch-tab', 'budget')"
            class="flex items-center gap-3 p-3 bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors duration-200 text-left"
          >
            <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"/>
              </svg>
            </div>
            <div>
              <div class="font-medium text-emerald-900">Create Budget Plan</div>
              <div class="text-xs text-emerald-600">Plan your GAD activities</div>
            </div>
          </button>

          <button
            @click="$emit('switch-tab', 'accomplishment')"
            class="flex items-center gap-3 p-3 bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors duration-200 text-left"
          >
            <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"/>
              </svg>
            </div>
            <div>
              <div class="font-medium text-emerald-900">Create Report</div>
              <div class="text-xs text-emerald-600">Document accomplishments</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="px-3 sm:px-4 lg:px-6 pb-6">
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <h3 class="text-lg font-semibold text-emerald-700 mb-4">Recent Activity</h3>

        <div class="space-y-3">
          <div v-if="recentActivities.length === 0" class="text-center py-8 text-gray-500">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <p class="text-sm">No recent activity</p>
            <p class="text-xs text-gray-400">Start by creating a budget plan or report</p>
          </div>

          <div v-for="activity in recentActivities" :key="activity.id" class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 p-3 bg-gray-50 rounded-lg">
            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-emerald-100">
              <svg v-if="activity.id && activity.id.startsWith('budget-')" class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.61 1.98"/>
              </svg>
              <svg v-else class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div class="flex-1">
              <div class="text-sm font-semibold truncate text-emerald-900">{{ activity.title }}</div>
              <div class="text-xs truncate text-emerald-600">{{ activity.userName }} • {{ activity.barangay }}</div>
              <div class="text-xs text-emerald-500">{{ formatDate(activity.date) }}</div>
            </div>
            <div v-if="activity.status" class="text-xs px-3 py-1.5 rounded-lg font-semibold" :class="getStatusClass(activity.status)">
              {{ (activity.status || '').charAt(0).toUpperCase() + (activity.status || '').slice(1) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';
import PieChart from './ui/charts/PieChart.vue';
import BarChart from './ui/charts/BarChart.vue';

// Emits
defineEmits(['switch-tab']);

// Reactive data
const budgetPlans = ref<any[]>([]);
const reports = ref<any[]>([]);
const totalBudget = ref(0);
const recentActivities = ref<any[]>([]);

// Fiscal year data
const currentYear = new Date().getFullYear();
const fiscalYears = ref([
  currentYear - 2,
  currentYear - 1,
  currentYear,
  currentYear + 1,
  currentYear + 2
]);
const selectedFiscalYear = ref(currentYear);
// Year picker state (copied style from BudgetPlan table)
const yearPickerOpen = ref(false);
const yearPickerRef = ref<HTMLElement | null>(null);
const yearPageStart = ref<number>(currentYear - 6);
const yearGrid = computed<number[]>(() => Array.from({ length: 12 }, (_, i) => yearPageStart.value + i));
function prevYearPage() { yearPageStart.value -= 12; }
function nextYearPage() { yearPageStart.value += 12; }
function selectYear(y: number) {
  selectedFiscalYear.value = y;
  yearPickerOpen.value = false;
  updateBudgetData();
}

// Close picker on outside click
if (typeof window !== 'undefined') {
  window.addEventListener('click', (e: MouseEvent) => {
    const target = e.target as Node;
    if (yearPickerOpen.value && yearPickerRef.value && !yearPickerRef.value.contains(target)) {
      yearPickerOpen.value = false;
    }
  });
}

// State for pie chart type and year
const selectedPieType = ref<'budget' | 'accomplishment'>('budget');
const selectedPieYear = ref(selectedFiscalYear.value);

// Watch for fiscal year changes to sync pie chart year
watch(selectedFiscalYear, (newYear) => {
  selectedPieYear.value = newYear;
});

// Computed properties
const budgetPlansCount = computed(() => budgetPlans.value.length);
const submittedPlansCount = computed(() => budgetPlans.value.filter(plan => plan.status !== 'draft').length);
const reportsCount = computed(() => reports.value.length);
const submittedReportsCount = computed(() => reports.value.filter(report => report.status !== 'draft').length);

const totalAllocated = computed(() => {
  return budgetPlans.value.reduce((sum, plan) => sum + (plan.totalBudget || 0), 0);
});

const budgetUtilization = computed(() => {
  if (totalBudget.value === 0) return 0;
  return Math.min(Math.round((totalAllocated.value / totalBudget.value) * 100), 100);
});

// Pie chart data filtered by year and type
const filteredBudgetPlans = computed(() =>
  budgetPlans.value.filter(plan => {
    const planYear = plan.fiscal_year || new Date(plan.created_at).getFullYear();
    return planYear === selectedPieYear.value;
  })
);
const filteredReports = computed(() =>
  reports.value.filter(report => {
    const reportYear = report.fiscal_year || new Date(report.dateSubmitted).getFullYear();
    return reportYear === selectedPieYear.value;
  })
);

// Computed summary string for the pie chart
const activityStatusSummary = computed(() => {
  const d = activityStatusLegendData.value;
  if (d.length === 0) return 'No data available.';
  const typeWord = selectedPieType.value === 'budget' ? 'plans' : 'reports';
  // Build a readable list: "2 Approved plans, 1 Pending Revision plans, 4 Drafts plans"
  const parts = d.map(item => `${item.value} ${item.label.toLowerCase()} ${typeWord}`);
  return `You have ${parts.join(', ')}.`;
});

// Helper functions
function formatCurrency(value: number): string {
  if (!value && value !== 0) return '₱0.00';
  return '₱' + Math.floor(Number(value)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

function getStatusClass(status: string): string {
  switch ((status || '').toLowerCase()) {
    case 'approved': return 'bg-emerald-100 text-emerald-800 border border-emerald-200';
    case 'pending': return 'bg-amber-100 text-amber-800 border border-amber-200';
    case 'revision': return 'bg-blue-100 text-blue-800 border border-blue-200';
    case 'disapproved': return 'bg-red-100 text-red-800 border border-red-200';
    case 'draft': return 'bg-blue-100 text-blue-800 border border-blue-200';
    default: return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
}

function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) return dateStr;
  return date.toLocaleString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
}

// Update budget data based on selected fiscal year
async function updateBudgetData() {
  try {
    // Filter budget plans by selected fiscal year
    const budgetResponse = await axios.get('/api/budget-plans');
    if (budgetResponse.data.budgetPlans) {
      budgetPlans.value = budgetResponse.data.budgetPlans.filter((plan: any) => {
        const planYear = plan.fiscal_year || new Date(plan.created_at).getFullYear();
        return planYear === selectedFiscalYear.value;
      });
    }

    // Filter reports by selected fiscal year
    const reportsResponse = await axios.get('/api/accomplishment-reports');
    if (reportsResponse.data.success) {
      reports.value = reportsResponse.data.reports.filter((report: any) => {
        const reportYear = report.fiscal_year || new Date(report.dateSubmitted).getFullYear();
        return reportYear === selectedFiscalYear.value;
      });
    }

    // Update recent activities
    const activities = [
      ...budgetPlans.value.slice(0, 3).map(plan => ({
        id: `budget-${plan.id}`,
        title: `Budget Plan: ${plan.title_desc || plan.titleDesc || plan.title || 'Untitled'}`,
        userName: plan.user_full_name || plan.user_name || 'Unknown User',
        barangay: plan.barangay_name || (plan.user && plan.user.barangay) || plan.barangay || 'Unknown Barangay',
        date: plan.created_at || 'Recently',
        status: plan.status || 'draft'
      })),
      ...reports.value.slice(0, 3).map(report => ({
        id: `report-${report.id}`,
        title: `Accomplishment Report: ${report.title || 'Untitled'}`,
        userName: report.user_name || 'Unknown User',
        barangay: report.barangay_name || report.barangay || 'Unknown Barangay',
        date: report.dateSubmitted || 'Recently',
        status: report.status || 'draft'
      }))
    ].slice(0, 5);

    recentActivities.value = activities;
  } catch (error) {
    console.error('Error updating budget data:', error);
  }
}

// Load data on mount
onMounted(async () => {
  try {
    // Load budget from localStorage
    const storedBudget = localStorage.getItem('gad_total_budget');
    totalBudget.value = storedBudget ? Number(storedBudget) : 12000000;

    // Fetch budget plans
    const budgetResponse = await axios.get('/api/budget-plans');
    if (budgetResponse.data.budgetPlans) {
      budgetPlans.value = budgetResponse.data.budgetPlans;
    }

    // Fetch reports
    const reportsResponse = await axios.get('/api/accomplishment-reports');
    if (reportsResponse.data.success) {
      reports.value = reportsResponse.data.reports;
    }

    // Create recent activities from both budget plans and reports
    const activities = [
      ...budgetPlans.value.slice(0, 3).map(plan => ({
        id: `budget-${plan.id}`,
        title: `Budget Plan: ${plan.title_desc || plan.titleDesc || plan.title || 'Untitled'}`,
        userName: plan.user_full_name || plan.user_name || 'Unknown User',
        barangay: plan.barangay_name || (plan.user && plan.user.barangay) || plan.barangay || 'Unknown Barangay',
        date: plan.created_at || 'Recently',
        status: plan.status || 'draft'
      })),
      ...reports.value.slice(0, 3).map(report => ({
        id: `report-${report.id}`,
        title: `Accomplishment Report: ${report.title || 'Untitled'}`,
        userName: report.user_name || 'Unknown User',
        barangay: report.barangay_name || report.barangay || 'Unknown Barangay',
        date: report.dateSubmitted || 'Recently',
        status: report.status || 'draft'
      }))
    ].slice(0, 5);

    recentActivities.value = activities;
  } catch (error) {
    console.error('Error loading dashboard data:', error);
  }
});

// Chart Data Preparation

// 1. Activity Status Breakdown (Pie)
// Status color mapping for pie chart
const statusColorMap: Record<string, string> = {
  'approved': '#10b981', // emerald
  'pending': '#f59e0b', // amber
  'pending revision': '#f59e0b', // amber
  'revision': '#1e40af', // blue
  'draft': '#6b7280', // gray
  // No mapping for not submitted, in revision, or unknown
};
// Statuses to always show in the pie chart legend
const alwaysShowStatuses = ['approved', 'pending', 'pending revision', 'revision', 'draft'];

const activityStatusChartData = computed(() => {
  const items = selectedPieType.value === 'budget' ? filteredBudgetPlans.value : filteredReports.value;
  const statusCounts: Record<string, number> = {};
  items.forEach(item => {
    let status = (item.status || 'Unknown').toLowerCase();
    if (status === 'draft' || status === 'drafts') status = 'draft';
    if (statusColorMap[status]) {
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    }
  });
  // Ensure all key statuses are present, even if zero
  alwaysShowStatuses.forEach(status => {
    if (!(status in statusCounts)) statusCounts[status] = 0;
  });
  const labels = alwaysShowStatuses.map(s => s.charAt(0).toUpperCase() + s.slice(1));
  const backgroundColor = alwaysShowStatuses.map(key => statusColorMap[key]);
  return {
    labels,
    datasets: [
      {
        data: alwaysShowStatuses.map(l => statusCounts[l]),
        backgroundColor,
      },
    ],
  };
});

// Pie chart options (copied and adapted from admin dashboard)
const activityStatusPieChartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: true,
  plugins: {
    legend: { display: false },
    tooltip: {
      callbacks: {
        label: (ctx: any) => `${ctx.label}: ${ctx.parsed}`,
      },
    },
    datalabels: {
      color: '#ffffff',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderColor: '#ffffff',
      borderWidth: 1,
      borderRadius: 3,
      padding: 2,
      font: {
        weight: 'bold',
        size: 10
      },
      formatter: (value: number, context: any) => {
        const data = context.chart.data.datasets[0].data;
        const total = data.reduce((a: number, b: number) => a + b, 0);
        const percent = total > 0 ? Math.round((value / total) * 100) : 0;
        return percent + '%';
      },
      display: true,
      anchor: 'center',
      align: 'center',
    },
  },
}));

// Custom legend data for Activity Status Breakdown
const activityStatusLegendData = computed(() => {
  const chart = activityStatusChartData.value;
  return chart.labels.map((label, idx) => ({
    label,
    value: chart.datasets[0].data[idx],
    color: chart.datasets[0].backgroundColor[idx] || '#10b981',
  }));
});

// 2. Budget Plan Distribution by Category (Bar)
// State for bar chart type and year (for future expansion)
const selectedBarType = ref<'budget' | 'accomplishment'>('budget');
const selectedBarYear = ref(selectedFiscalYear.value);

watch(selectedFiscalYear, (newYear) => {
  selectedBarYear.value = newYear;
});

const filteredBarBudgetPlans = computed(() =>
  budgetPlans.value.filter(plan => {
    const planYear = plan.fiscal_year || new Date(plan.created_at).getFullYear();
    return planYear === selectedBarYear.value;
  })
);
const filteredBarReports = computed(() =>
  reports.value.filter(report => {
    const reportYear = report.fiscal_year || new Date(report.dateSubmitted).getFullYear();
    return reportYear === selectedBarYear.value;
  })
);

const budgetCategoryChartData = computed(() => {
  const categoryCounts: Record<string, number> = { 'Client Focus': 0, 'Organization Focus': 0, 'Other': 0 };
  const items = selectedBarType.value === 'budget' ? filteredBarBudgetPlans.value : filteredBarReports.value;
  items.forEach(plan => {
    let focus = plan.focused || plan.category || plan.type || '';
    // Normalize focus/category
    if (typeof focus === 'string') {
      const f = focus.trim().toLowerCase();
      if (f === 'client focus' || f === 'client-focused' || f === 'client') {
        focus = 'Client Focus';
      } else if (f === 'organization focus' || f === 'organization-focused' || f === 'organization') {
        focus = 'Organization Focus';
      } else {
        focus = 'Other';
      }
    } else {
      focus = 'Other';
    }
    categoryCounts[focus] = (categoryCounts[focus] || 0) + 1;
  });
  // Ensure all values are integers
  const data = [categoryCounts['Client Focus'], categoryCounts['Organization Focus'], categoryCounts['Other']].map(v => Math.round(v));
  return {
    labels: ['Client Focus', 'Organization Focus', 'Other'],
    datasets: [
      {
        label: selectedBarType.value === 'budget' ? 'No. of Plans' : 'No. of Reports',
        data,
        backgroundColor: ['#3b82f6', '#10b981', '#fbbf24'],
      },
    ],
  };
});

// Bar chart options to force integer Y-axis ticks
const barChartOptions = {
  responsive: true,
  maintainAspectRatio: true,
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        stepSize: 1,
        callback: function(value: number) {
          return Number.isInteger(value) ? value : null;
        }
      }
    }
  }
};
</script>
