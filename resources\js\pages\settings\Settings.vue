<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { settingsActiveTab } from '@/settingsTabState';
import { Settings as SettingsIcon, User, Lock, Palette } from 'lucide-vue-next';

// Set active tab
settingsActiveTab.value = 'general';

const settingsCategories = [
    {
        title: 'Profile',
        description: 'Manage your personal information and account details',
        icon: User,
        spaTab: 'profile',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50 dark:bg-blue-950/30',
    },
    {
        title: 'Password',
        description: 'Update your password and security settings',
        icon: Lock,
        spaTab: 'password',
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-50 dark:bg-emerald-950/30',
    },
    {
        title: 'Appearance',
        description: 'Customize the look and feel of your application',
        icon: Palette,
        spaTab: 'appearance',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50 dark:bg-purple-950/30',
    },
];

const handleCategoryClick = (spaTab: string) => {
    settingsActiveTab.value = spaTab as any;
};
</script>

<template>
    <div class="p-8 space-y-8">
        <!-- Settings Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-emerald-100 dark:bg-emerald-900/30 rounded-full mb-4">
                <SettingsIcon class="h-8 w-8 text-emerald-600" />
            </div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">General Settings</h1>
            <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Manage your account settings, preferences, and application configuration.
            </p>
        </div>

        <!-- Settings Overview -->
        <Card class="max-w-6xl mx-auto border-0 shadow-lg">
            <CardHeader>
                <CardTitle class="flex items-center gap-2 text-xl">
                    <SettingsIcon class="h-5 w-5 text-emerald-600" />
                    Account Settings
                </CardTitle>
                <CardDescription>
                    Choose a category to manage your settings and preferences
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <button
                        v-for="category in settingsCategories"
                        :key="category.title"
                        @click="handleCategoryClick(category.spaTab)"
                        class="group block text-left"
                    >
                        <div class="p-6 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-emerald-300 dark:hover:border-emerald-600 transition-all duration-200 hover:shadow-md">
                            <div class="flex items-start gap-4">
                                <div :class="['p-3 rounded-lg', category.bgColor]">
                                    <component :is="category.icon" :class="['h-6 w-6', category.color]" />
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-emerald-600 transition-colors">
                                        {{ category.title }}
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                        {{ category.description }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </button>
                </div>
            </CardContent>
        </Card>

        <!-- System Information -->
        <Card class="max-w-6xl mx-auto border-0 shadow-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
            <CardHeader>
                <CardTitle class="text-xl">System Information</CardTitle>
                <CardDescription>
                    Information about your account and system
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <div class="space-y-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">Account Type</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Standard User</p>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">Last Login</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Today at 2:30 PM</p>
                    </div>
                    <div class="space-y-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">System Version</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">GAD System v1.0</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    </div>
</template>
