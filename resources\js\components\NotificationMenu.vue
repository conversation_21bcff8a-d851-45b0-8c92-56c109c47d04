<template>
  <div class="relative">
    <!-- <PERSON> -->
    <button @click="toggleDropdown" class="relative p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400">
      <span class="sr-only">Open notifications</span>
      <span class="icon-bell" aria-label="Notifications">
        <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 22c1.1 0 2-.9 2-2h-4a2 2 0 0 0 2 2zm6-6V11c0-3.07-1.63-5.64-4.5-6.32V4a1.5 1.5 0 0 0-3 0v.68C7.63 5.36 6 7.92 6 11v5l-1.7 1.7A1 1 0 0 0 5 20h14a1 1 0 0 0 .7-1.7L18 16zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z" fill="#000" stroke="#fff" stroke-width="0.5"/>
        </svg>
      </span>
      <span v-if="unreadCount > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5">{{ unreadCount }}</span>
    </button>
    <!-- Modal Overlay & Content -->
    <transition name="fade">
      <div v-if="dropdown" class="absolute right-0 mt-2 z-50 w-80 h-[700px] bg-white rounded-[10px] shadow-xl flex flex-col outline-none border border-green-100" @keydown.esc.window="closeDropdown" tabindex="-1">
        <!-- Header -->
        <div class="flex items-center justify-between px-6 py-4 border-b border-green-100">
          <span class="text-lg font-bold text-green-800">Notifications</span>
          <button @click="closeDropdown" class="text-green-700 hover:text-green-900">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <!-- Notification List -->
        <div class="flex-1 overflow-y-auto max-h-[696px]">
          <ul v-if="notifications.length > 0" class="divide-y divide-green-50">
            <li v-for="(notif, idx) in notifications" :key="idx" @click="handleMarkAsRead(notif, idx)" :class="[ 'px-6 py-4 cursor-pointer transition', !notif.read ? 'bg-green-50 font-semibold' : 'hover:bg-green-50' ]">
              <div class="text-base font-medium text-green-900">{{ notif.title }}</div>
              <div class="text-sm text-gray-700 mt-1">{{ notif.description }}</div>
              <div class="text-xs text-gray-400 mt-1">{{ notif.time }}</div>
            </li>
          </ul>
          <div v-else class="text-center text-gray-400 py-8">No notifications</div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useNotifications } from '../composables/useNotifications';

// Assume userId is available from props or global store
defineProps({ userId: Number });

const dropdown = ref(false);
const { notifications, unreadCount, markAsRead, init, cleanup } = useNotifications();

function closeDropdown() {
  dropdown.value = false;
}

function toggleDropdown() {
  dropdown.value = !dropdown.value;
}

function handleMarkAsRead(notif, idx) {
  if (notif.unread) {
    markAsRead(notif.id);
  }
}

onMounted(() => {
  // Replace with actual userId source if needed
  init(userId, false); // false disables polling, uses real-time only
});

onUnmounted(() => {
  cleanup();
});
</script>

