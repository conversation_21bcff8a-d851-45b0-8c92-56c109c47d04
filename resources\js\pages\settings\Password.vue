<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { settingsActiveTab } from '@/settingsTabState';
import { Lock, Eye, EyeOff, CheckCircle, Shield } from 'lucide-vue-next';

const passwordInput = ref<HTMLInputElement | null>(null);
const currentPasswordInput = ref<HTMLInputElement | null>(null);
const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: '',
});

const updatePassword = () => {
    form.put(route('password.update'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
        onError: (errors: any) => {
            if (errors.password) {
                form.reset('password', 'password_confirmation');
                if (passwordInput.value instanceof HTMLInputElement) {
                    passwordInput.value.focus();
                }
            }

            if (errors.current_password) {
                form.reset('current_password');
                if (currentPasswordInput.value instanceof HTMLInputElement) {
                    currentPasswordInput.value.focus();
                }
            }
        },
    });
};

// Set active tab
settingsActiveTab.value = 'password';
</script>

<template>
    <div class="p-8 space-y-8">
        <!-- Password Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-emerald-100 dark:bg-emerald-900/30 rounded-full mb-4">
                <Shield class="h-8 w-8 text-emerald-600" />
            </div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Password Security</h1>
            <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Keep your account secure by using a strong password that you don't use elsewhere.
            </p>
        </div>

        <!-- Password Form -->
        <Card class="max-w-2xl mx-auto border-0 shadow-lg">
            <CardHeader>
                <CardTitle class="flex items-center gap-2 text-xl">
                    <Lock class="h-5 w-5 text-emerald-600" />
                    Update Password
                </CardTitle>
                <CardDescription>
                    Ensure your account is using a long, random password to stay secure
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form @submit.prevent="updatePassword" class="space-y-6">
                    <!-- Current Password -->
                    <div class="space-y-2">
                        <Label for="current_password" class="text-sm font-medium">Current Password</Label>
                        <div class="relative">
                            <Input
                                id="current_password"
                                ref="currentPasswordInput"
                                v-model="form.current_password"
                                :type="showCurrentPassword ? 'text' : 'password'"
                                class="pr-10 border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                                autocomplete="current-password"
                                placeholder="Enter your current password"
                            />
                            <button
                                type="button"
                                @click="showCurrentPassword = !showCurrentPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                                <Eye v-if="!showCurrentPassword" class="h-4 w-4" />
                                <EyeOff v-else class="h-4 w-4" />
                            </button>
                        </div>
                        <InputError :message="form.errors.current_password" />
                    </div>

                    <Separator />

                    <!-- New Password -->
                    <div class="space-y-2">
                        <Label for="password" class="text-sm font-medium">New Password</Label>
                        <div class="relative">
                            <Input
                                id="password"
                                ref="passwordInput"
                                v-model="form.password"
                                :type="showNewPassword ? 'text' : 'password'"
                                class="pr-10 border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                                autocomplete="new-password"
                                placeholder="Enter your new password"
                            />
                            <button
                                type="button"
                                @click="showNewPassword = !showNewPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                                <Eye v-if="!showNewPassword" class="h-4 w-4" />
                                <EyeOff v-else class="h-4 w-4" />
                            </button>
                        </div>
                        <InputError :message="form.errors.password" />
                    </div>

                    <!-- Confirm Password -->
                    <div class="space-y-2">
                        <Label for="password_confirmation" class="text-sm font-medium">Confirm New Password</Label>
                        <div class="relative">
                            <Input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                :type="showConfirmPassword ? 'text' : 'password'"
                                class="pr-10 border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                                autocomplete="new-password"
                                placeholder="Confirm your new password"
                            />
                            <button
                                type="button"
                                @click="showConfirmPassword = !showConfirmPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                                <Eye v-if="!showConfirmPassword" class="h-4 w-4" />
                                <EyeOff v-else class="h-4 w-4" />
                            </button>
                        </div>
                        <InputError :message="form.errors.password_confirmation" />
                    </div>

                    <!-- Password Requirements -->
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Password Requirements</h4>
                        <ul class="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            <li class="flex items-center gap-2">
                                <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                                At least 8 characters long
                            </li>
                            <li class="flex items-center gap-2">
                                <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                                Contains uppercase and lowercase letters
                            </li>
                            <li class="flex items-center gap-2">
                                <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                                Contains at least one number
                            </li>
                            <li class="flex items-center gap-2">
                                <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                                Contains at least one special character
                            </li>
                        </ul>
                    </div>

                    <Separator />

                    <!-- Submit Button -->
                    <div class="flex items-center gap-4">
                        <Button 
                            type="submit" 
                            :disabled="form.processing" 
                            class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2"
                        >
                            <span v-if="form.processing">Updating...</span>
                            <span v-else>Update Password</span>
                        </Button>

                        <Transition
                            enter-active-class="transition ease-in-out duration-300"
                            enter-from-class="opacity-0 transform translate-y-1"
                            enter-to-class="opacity-100 transform translate-y-0"
                            leave-active-class="transition ease-in-out duration-300"
                            leave-from-class="opacity-100 transform translate-y-0"
                            leave-to-class="opacity-0 transform translate-y-1"
                        >
                            <div v-show="form.recentlySuccessful" class="flex items-center gap-2 text-sm text-emerald-600 dark:text-emerald-400">
                                <CheckCircle class="h-4 w-4" />
                                Password updated successfully!
                            </div>
                        </Transition>
                    </div>
                </form>
            </CardContent>
        </Card>
    </div>
</template>
