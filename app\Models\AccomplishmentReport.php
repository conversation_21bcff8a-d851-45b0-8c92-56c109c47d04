<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccomplishmentReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'barangay_id',
        'barangay',
        'title',
        'genderIssue',
        'gadMandate',
        'focused',
        'gadObjective',
        'lguPpa',
        'gadActivity',
        'performanceIndicator',
        'actualResults',
        'approvedBudget',
        'actualCost',
        'remarks',
        'status',
        'admin_approver',
        'date_approved',
        'is_draft',
        'dateSubmitted',
        'supportingData',
        'dataSource',
        'ppa',
        'otherActivity',
        'attachment_path',
        'activity',
        'ppaSi',
    ];

    protected $casts = [
        'approvedBudget' => 'float',
        'actualCost' => 'float',
        'dateSubmitted' => 'date',
        'date_approved' => 'datetime',
        'is_draft' => 'boolean',
        'activity' => 'array',
        'ppaSi' => 'array',
    ];

    // Relationship with User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relationship with Barangay
    public function barangay()
    {
        return $this->belongsTo(Barangay::class);
    }
}
