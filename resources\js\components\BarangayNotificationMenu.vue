<template>
  <div class="notification-menu">
    <button class="notification-btn" @click="toggleDropdown">
      <span class="icon-bell" aria-label="Barangay Notifications">
        <!-- SVG for green bell icon -->
        <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 22c1.1 0 2-.9 2-2h-4a2 2 0 0 0 2 2zm6-6V11c0-3.07-1.63-5.64-4.5-6.32V4a1.5 1.5 0 0 0-3 0v.68C7.63 5.36 6 7.92 6 11v5l-1.7 1.7A1 1 0 0 0 5 20h14a1 1 0 0 0 .7-1.7L18 16zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z" fill="#047857" stroke="#fff" stroke-width="0.5"/>
        </svg>
      </span>
      <span v-if="unreadCount > 0" class="badge">{{ unreadCount }}</span>
    </button>
    <div v-if="dropdown" class="notification-dropdown-responsive">
      <div class="notification-dropdown-container">
        <div class="notification-dropdown">
          <div v-if="notifications.length === 0" class="empty">No barangay notifications</div>
          <ul v-else>
              <div class="notif-section-title">Barangay Notifications</div>
              <div class="br"></div>
            <li v-for="(notif, idx) in notifications" :key="idx" :class="{ unread: !notif.read }">
              <span class="notif-title">{{ notif.title }}</span>
              <span class="notif-desc">{{ notif.description }}</span>
              <span class="notif-time">{{ notif.time }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { router } from '@inertiajs/vue3';

const dropdown = ref(false);

function closeDropdown() {
  dropdown.value = false;
}

const notifications = ref([
  {
    title: 'Barangay Meeting',
    description: 'Monthly barangay meeting scheduled for April 25, 2025.',
    time: '2 hours ago',
    read: false
  },
  {
    title: 'Document Reminder',
    description: 'Submit your barangay clearance documents before April 30.',
    time: '5 hours ago',
    read: false
  },
  {
    title: 'Community Clean-up',
    description: 'Community clean-up drive this Saturday at 7:00AM.',
    time: '1 day ago',
    read: true
  }
]);

const unreadCount = computed(() => notifications.value.filter(n => !n.read).length);

function toggleDropdown() {
  dropdown.value = !dropdown.value;
}

function handleClickOutside(event) {
  const overlay = document.querySelector('.notification-dropdown-responsive');
  const panel = document.querySelector('.notification-dropdown');
  const menu = document.querySelector('.notification-menu');
  const path = event.composedPath ? event.composedPath() : [];
  if (dropdown.value) {
    if (overlay && panel) {
      // Mobile: close if click is outside the notification panel
      if (!path.includes(panel)) {
        closeDropdown();
      }
    } else if (menu && !path.includes(menu)) {
      // Desktop: close if click is outside the menu
      closeDropdown();
    }
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
  // Listen for Inertia navigation
  if (router && router.on) {
    router.on('navigate', closeDropdown);
  }
});

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside);
  if (router && router.off) {
    router.off('navigate', closeDropdown);
  }
});
</script>

<style scoped>
/***** Same styles as NotificationMenu.vue, but green bell *****/
.notification-dropdown-responsive {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

@media (max-width: 600px) {
  .notification-dropdown-responsive {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.12);
    z-index: 2000;
    align-items: flex-start;
    justify-content: flex-end;
  }
  .notification-dropdown-container {
    width: 100vw;
    height: 80vh;
    margin: 0;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }
  .notification-dropdown {
    width: 100vw;
    height: 80vh;
    max-width: 100vw;
    max-height: 80vh;
    border-radius: 0 0 12px 12px;
    box-sizing: border-box;
    position: relative;
    padding-top: 40px;
    overflow-y: auto;
  }
}

.notification-menu {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: right;
}
.notification-btn {
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  font-size: 36px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-bell {
  font-size: 36px;
  color: #047857;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}
.icon-bell svg {
  display: block;
  width: 36px;
  height: 36px;
}

.badge {
  position: absolute;
  top: -2px;
  right: 1px;
  background: #22c55e;
  color: #fff;
  border-radius: 50%;
  font-size: 11px;
  padding: 1px 6px;
}
.notification-dropdown {
  position: absolute;
  right: 0;
  left: auto;
  top: 38px;
  background: #fff;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  width: 400px;
  max-width: 90vw;
  box-sizing: border-box;
  margin-right: 8px;
  z-index: 1000;
  border-radius: 10px;
  padding: 10px 0;
  font-size: 18px;
  /* Responsive max height for desktop */
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}
.notification-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
  /* Scroll only the list if too many notifications */
  overflow-y: auto;
  max-height: 100vh;
  height:500px
}

@media (max-width: 600px) {
  .notification-dropdown {
    width: 95vw;
    left: 2.5vw;
    right: 2.5vw;
    min-width: auto;
    border-radius: 0 0 12px 12px;
    font-size: 17px;
    max-height: 70vh;
    padding: 18px 0;
    box-sizing: border-box;
  }
  .notification-dropdown ul {
    max-height: 85vh;
  }
  .notification-dropdown li, .notification-dropdown ul {
    padding-left: 14px;
    padding-right: 14px;
  }
  .notification-menu {
    width: auto;
  }
}
.notification-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.notification-dropdown li {
  padding: 8px 16px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background 0.2s;
  border-radius: 4px;
}
.notification-dropdown li:hover {
  background: #f0f4fa;
}
.notif-title {
  display: block;
  font-weight: bold;
  margin-bottom: 2px;
  padding: 4px 0;
}
.notification-dropdown li:last-child {
  border-bottom: none;
}
.notification-dropdown li.unread {
  font-weight: 600;
}
.br {
  border-bottom: 1.5px solid #151414;
  margin: 0;
  height: 0;
  list-style: none;
}

.notif-section-title {
  font-size: 18px;
  color: #047857;
  padding: 12px 16px 8px 16px;
  border-bottom: 1px solid #eee;
  text-align: left;
  user-select: none;
  background: none;
}

.notif-title {
  display: block;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 2px;
}
.notif-desc {
  display: block;
  font-size: 13px;
  color: #555;
}
.notif-time {
  display: block;
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}
.empty {
  text-align: center;
  color: #888;
  padding: 18px 0;
}
</style>
