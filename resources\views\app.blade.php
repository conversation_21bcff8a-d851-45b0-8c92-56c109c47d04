<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"  @class(['dark' => ($appearance ?? 'system') == 'dark'])>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        {{-- Inline script to detect system dark mode preference and apply it immediately --}}
        <script>
            (function() {
                const appearance = '{{ $appearance ?? "system" }}';

                if (appearance === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (prefersDark) {
                        document.documentElement.classList.add('dark');
                    }
                }
            })();
        </script>

        {{-- Inline style to set the HTML background color based on our theme in app.css --}}
        <style>
            html {
                background-color: oklch(1 0 0);
            }

            html.dark {
                background-color: oklch(0.145 0 0);
            }
            
            /* Prevent layout shifts from font loading */
            .text-lg.font-semibold.text-green-900 {
                font-display: swap;
                visibility: visible !important;
            }
        </style>

        <title inertia>{{ config('app.name', 'GAD System') }}</title>

        <!-- Preload critical assets -->
        <link rel="preload" href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" as="style" />
        <link rel="stylesheet" href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" media="print" onload="this.media='all'" />
        <link rel="icon" type="image/png" href="/images/panabo-city-logo-1.png">

        @routes
        @vite(['resources/js/app.ts'])
        @inertiaHead
    </head>
    <body class="font-sans antialiased">
        @inertia
    </body>
</html>

