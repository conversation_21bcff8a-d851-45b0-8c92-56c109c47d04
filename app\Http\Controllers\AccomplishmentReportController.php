<?php

namespace App\Http\Controllers;

use App\Models\AccomplishmentReport;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class AccomplishmentReportController extends Controller
{
    public function index(Request $request)
    {
        try {
            // Get authenticated user
            $userId = Auth::id();

            if (!$userId) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            $user = Auth::user();

            $query = AccomplishmentReport::query()
                ->select(
                    'accomplishment_reports.*',
                    'users.name as user_name',
                    'users.email as user_email',
                    'users.middle_name',
                    'users.last_name',
                    'users.suffix',
                    'barangays.name as barangay_name'
                )
                ->leftJoin('users', 'accomplishment_reports.user_id', '=', 'users.id')
                ->leftJoin('barangays', 'accomplishment_reports.barangay_id', '=', 'barangays.id');

            // Filter by user_id if provided
            if ($request->has('user_id')) {
                $query->where('accomplishment_reports.user_id', $request->user_id);
            }

            // Filter by barangay_id if provided
            if ($request->has('barangay_id')) {
                $query->where('accomplishment_reports.barangay_id', $request->barangay_id);
            }

            // If not admin, only show user's own data or their barangay's data
            if ($user && $user->role === 'barangay') {
                $query->where('accomplishment_reports.user_id', $user->id)
                      ->where('accomplishment_reports.barangay_id', $user->barangay_id);
            } elseif ($user && $user->role !== 'admin') {
                // For other non-admins, keep the old logic if needed
                $query->where(function($q) use ($user) {
                    $q->where('accomplishment_reports.user_id', $user->id)
                      ->orWhere('accomplishment_reports.barangay_id', $user->barangay_id);
                });
            }
         // Exclude drafts only for admin users
if ($user && $user->role === 'admin') {
    $query->where('accomplishment_reports.status', '!=', 'draft');
}

            // Custom sorting: Pending first, then by submission time (oldest first)
            $reports = $query->orderByRaw("
                CASE
                    WHEN accomplishment_reports.status = 'pending' THEN 1
                    WHEN accomplishment_reports.status = 'pending revision' THEN 2
                    WHEN accomplishment_reports.status = 'revision' THEN 3
                    WHEN accomplishment_reports.status = 'approved' THEN 4
                    ELSE 5
                END
            ")->orderBy('accomplishment_reports.created_at', 'asc')->get();

            // Transform the reports to match the expected format in the frontend
            $transformedReports = $reports->map(function($report) {
                // Construct a formatted full name
                $fullName = $report->user_name ?? '';

                // If we have last_name, we can construct a more formal name
                if ($report->last_name) {
                    // Start with the name field (which is likely the first name)
                    $fullName = $report->user_name;

                    // Add middle initial if available
                    if ($report->middle_name) {
                        $fullName .= ' ' . strtoupper(substr($report->middle_name, 0, 1)) . '.';
                    }

                    // Add last name
                    $fullName .= ' ' . strtoupper($report->last_name);

                    // Add suffix if available
                    if ($report->suffix) {
                        $fullName .= ', ' . $report->suffix;
                    }
                }

                // Fallback: If user_email is missing, try to fetch from relationship
                $userEmail = $report->user_email;
                if (!$userEmail && method_exists($report, 'user') && $report->user) {
                    $userEmail = $report->user->email;
                }

                return [
                    'id' => $report->id,
                    'title' => $report->title,
                    'genderIssue' => $report->genderIssue,
                    'gadMandate' => $report->gadMandate,
                    'focused' => $report->focused,
                    'gadObjective' => $report->gadObjective,
                    'lguPpa' => $report->lguPpa,
                    'gadActivity' => $report->gadActivity,
                    'performanceIndicator' => $report->performanceIndicator,
                    'actualResults' => $report->actualResults,
                    'approvedBudget' => $report->approvedBudget,
                    'actualCost' => $report->actualCost,
                    'remarks' => $report->remarks,
                    'status' => $report->status,
                    'admin_approver' => $report->admin_approver,
                    'date_approved' => $report->date_approved,
                    'dateSubmitted' => $report->dateSubmitted,
                    'created_at' => $report->created_at,
                    'updated_at' => $report->updated_at,
                    'user_name' => strtoupper($fullName),
                    // Always provide user_email if possible
                    'user_email' => $userEmail,
                    'barangay_name' => $report->barangay_name ?? $report->barangay ?? null,
                    'middle_name' => $report->middle_name,
                    'last_name' => $report->last_name,
                    'suffix' => $report->suffix,
                    'attachment_path' => $report->attachment_path,
                    // Include any other fields needed by the frontend
                ];
            });

            return response()->json([
                'success' => true,
                'reports' => $transformedReports
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching accomplishment reports: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching accomplishment reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            Log::info('Accomplishment report store request received', $request->all());

            // Get authenticated user
            $userId = Auth::id();

            if (!$userId) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            $user = Auth::user();

            $report = new AccomplishmentReport();
            $report->user_id = $userId;

            // Set barangay information based on budget_plan_id if provided
            if ($request->has('budget_plan_id') && $request->budget_plan_id) {
                // Get the budget plan
                $budgetPlan = \App\Models\BudgetPlan::find($request->budget_plan_id);

                if ($budgetPlan) {
                    // Get the barangay information from the barangays table
                    $barangay = \App\Models\Barangay::find($budgetPlan->barangay_id);

                    if ($barangay) {
                        // Set the barangay_id to match the budget plan
                        $report->barangay_id = $barangay->id;

                        // If the barangay name column exists, set it too
                        if (Schema::hasColumn('accomplishment_reports', 'barangay')) {
                            $report->barangay = $barangay->name;
                        }

                        // Set region, province, city if those columns exist
                        if (Schema::hasColumn('accomplishment_reports', 'region')) {
                            $report->region = $barangay->region;
                        }

                        if (Schema::hasColumn('accomplishment_reports', 'province')) {
                            $report->province = $barangay->province;
                        }

                        if (Schema::hasColumn('accomplishment_reports', 'city')) {
                            $report->city = $barangay->city;
                        }
                    } else {
                        // Fallback to user's barangay if barangay not found
                        $report->barangay_id = $user->barangay_id;
                        if (Schema::hasColumn('accomplishment_reports', 'barangay')) {
                            $report->barangay = $user->barangay;
                        }
                    }
                } else {
                    // Fallback to user's barangay if budget plan not found
                    $report->barangay_id = $user->barangay_id;
                    if (Schema::hasColumn('accomplishment_reports', 'barangay')) {
                        $report->barangay = $user->barangay;
                    }
                }
            } else {
                // No budget plan ID provided, use user's barangay
                $report->barangay_id = $user->barangay_id;
                if (Schema::hasColumn('accomplishment_reports', 'barangay')) {
                    $report->barangay = $user->barangay;
                }
            }

            // Basic fields
            $report->title = $request->title;
            $report->genderIssue = $request->genderIssue ?? '';
            $report->gadMandate = $request->gadMandate ?? '';
            $report->focused = $request->focused;
            $report->gadObjective = $request->gadObjective;
            $report->lguPpa = $request->lguPpa;
            $report->gadActivity = $request->gadActivity;
            $report->performanceIndicator = $request->performanceIndicator;
            $report->actualResults = $request->actualResults;

            // Budget fields
            $report->approvedBudget = floatval($request->approvedBudget ?? 0);
            $report->actualCost = floatval($request->actualCost ?? 0);

            // Additional fields
            $report->remarks = $request->remarks ?? '';
            $report->status = $request->status ?? 'pending';
            $report->dateSubmitted = $request->dateSubmitted ?? now()->toDateString();

            // Optional fields if they exist in the schema
            if (Schema::hasColumn('accomplishment_reports', 'supportingData')) {
                $report->supportingData = $request->supportingData ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'dataSource')) {
                $report->dataSource = $request->dataSource ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppa')) {
                $report->ppa = $request->ppa ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'otherActivity')) {
                $report->otherActivity = $request->otherActivity ?? '';
            }

            // Handle activity and ppaSi fields
            if (Schema::hasColumn('accomplishment_reports', 'activity')) {
                $report->activity = $request->activity ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppaSi')) {
                $report->ppaSi = $request->ppaSi ?? '';
            }

            // Save the report first to get an ID
            $report->save();

            // Handle multiple file uploads after saving to get the report ID
            $attachmentPaths = [];

            // Check for multiple files
            if ($request->hasFile('attachments')) {
                $files = $request->file('attachments');
                foreach ($files as $file) {
                    try {
                        Log::info('Processing attachment file', [
                            'name' => $file->getClientOriginalName(),
                            'size' => $file->getSize()
                        ]);

                        // Use a more explicit filename to avoid conflicts
                        $filename = time() . '_' . $file->getClientOriginalName();
                        $path = $file->storeAs('accomplishment-report-attachments/' . $report->id, $filename, 'public');

                        if ($path) {
                            Log::info('File stored successfully', ['path' => $path]);
                            $attachmentPaths[] = $path;
                        } else {
                            Log::error('Failed to store file - path is empty');
                        }
                    } catch (\Exception $e) {
                        Log::error('Error storing file: ' . $e->getMessage());
                        Log::error('Error trace: ' . $e->getTraceAsString());
                    }
                }
            }

            // Update attachment paths if we have any attachments
            if (!empty($attachmentPaths)) {
                $report->attachment_path = json_encode($attachmentPaths);
                $report->save(); // Save again to update attachment paths
            }

            Log::info('Accomplishment report created successfully', ['id' => $report->id]);

            // Create notification for accomplishment report status change
            $this->createReportNotification($report, $report->status, "Accomplishment report '{$report->title}' has been created.");

            return response()->json([
                'success' => true,
                'message' => 'Accomplishment report created successfully',
                'report' => $report
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating accomplishment report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating accomplishment report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $report = AccomplishmentReport::findOrFail($id);
            return response()->json(['report' => $report]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching accomplishment report',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            $report = AccomplishmentReport::findOrFail($id);

            // Check if user has permission to update this report
            // Only allow barangay user to update their own report if status is draft, revision, or pending revision
            if ($user->role === 'barangay') {
                if ($report->user_id !== $user->id || !in_array($report->status, ['draft', 'revision', 'pending revision'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unauthorized: Only the barangay user who owns this report and if it is in draft, revision, or pending revision status can update/submit.'
                    ], 403);
                }
            } else if ($user->role !== 'admin' && $user->barangay_id != $report->barangay_id && $user->barangay != $report->barangay) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            // Update basic fields
            if ($request->has('title')) $report->title = $request->title;
            if ($request->has('genderIssue')) $report->genderIssue = $request->genderIssue;
            if ($request->has('gadMandate')) $report->gadMandate = $request->gadMandate;
            if ($request->has('focused')) $report->focused = $request->focused;
            if ($request->has('gadObjective')) $report->gadObjective = $request->gadObjective;
            if ($request->has('lguPpa')) $report->lguPpa = $request->lguPpa;
            if ($request->has('gadActivity')) $report->gadActivity = $request->gadActivity;
            if ($request->has('performanceIndicator')) $report->performanceIndicator = $request->performanceIndicator;
            if ($request->has('actualResults')) $report->actualResults = $request->actualResults;

            // Update budget fields
            if ($request->has('approvedBudget')) $report->approvedBudget = floatval($request->approvedBudget);
            if ($request->has('actualCost')) $report->actualCost = floatval($request->actualCost);

            // Update additional fields
            if ($request->has('remarks')) $report->remarks = $request->remarks;
            if ($request->has('status')) {
                // If barangay user is submitting a revision, set status to 'pending revision'
                if ($user->role === 'barangay' && $report->status === 'revision' && $request->status === 'pending') {
                    $report->status = 'pending revision';
                } else {
                    $report->status = $request->status;
                }
            }
            if ($request->has('dateSubmitted')) $report->dateSubmitted = $request->dateSubmitted;

            // Handle admin approval fields
            if ($request->has('admin_approver')) {
                \Log::info('Setting admin_approver', ['value' => $request->admin_approver]);
                $report->admin_approver = $request->admin_approver;
            }
            if ($request->has('date_approved')) {
                \Log::info('Setting date_approved', ['value' => $request->date_approved]);
                $report->date_approved = $request->date_approved;
            }

            // Update optional fields if they exist
            if (Schema::hasColumn('accomplishment_reports', 'supportingData') && $request->has('supportingData')) {
                $report->supportingData = $request->supportingData;
            }

            if (Schema::hasColumn('accomplishment_reports', 'dataSource') && $request->has('dataSource')) {
                $report->dataSource = $request->dataSource;
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppa') && $request->has('ppa')) {
                $report->ppa = $request->ppa;
            }

            if (Schema::hasColumn('accomplishment_reports', 'otherActivity') && $request->has('otherActivity')) {
                $report->otherActivity = $request->otherActivity;
            }

            // Update activity and ppaSi fields
            if (Schema::hasColumn('accomplishment_reports', 'activity') && $request->has('activity')) {
                $report->activity = $request->activity;
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppaSi') && $request->has('ppaSi')) {
                $report->ppaSi = $request->ppaSi;
            }

            $report->save();

            // Create notification for accomplishment report status change
            $this->createReportNotification($report, $report->status, "Accomplishment report '{$report->title}' has been updated.");

            return response()->json([
                'success' => true,
                'message' => 'Accomplishment report updated successfully',
                'report' => $report
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating accomplishment report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $user = auth()->user();
            $report = AccomplishmentReport::findOrFail($id);

            // Check if user has permission to delete this report
            if ($user->role !== 'admin' &&
                $user->barangay_id != $report->barangay_id &&
                $user->id != $report->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $report->delete();

            return response()->json([
                'success' => true,
                'message' => 'Accomplishment report deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting accomplishment report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store an accomplishment report as a draft.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeDraft(Request $request)
    {
        try {
            // Get authenticated user
            $userId = Auth::id();

            if (!$userId) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            $user = Auth::user();

            $report = new AccomplishmentReport();
            $report->user_id = $userId;
            $report->barangay_id = $user->barangay_id;

            if (Schema::hasColumn('accomplishment_reports', 'barangay')) {
                $report->barangay = $user->barangay;
            }

            // Basic fields
            $report->title = $request->title;
            $report->genderIssue = $request->genderIssue ?? '';
            $report->gadMandate = $request->gadMandate ?? '';
            $report->focused = $request->focused;
            $report->gadObjective = $request->gadObjective;
            $report->lguPpa = $request->lguPpa;
            $report->gadActivity = $request->gadActivity;
            $report->performanceIndicator = $request->performanceIndicator;
            $report->actualResults = $request->actualResults;

            // Budget fields
            $report->approvedBudget = floatval($request->approvedBudget ?? 0);
            $report->actualCost = floatval($request->actualCost ?? 0);

            // Additional fields
            $report->remarks = $request->remarks ?? '';
            $report->status = 'draft'; // Set status as draft
            $report->is_draft = true; // Set is_draft flag
            $report->dateSubmitted = null; // No submission date for drafts

            // Optional fields if they exist in the schema
            if (Schema::hasColumn('accomplishment_reports', 'supportingData')) {
                $report->supportingData = $request->supportingData ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'dataSource')) {
                $report->dataSource = $request->dataSource ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppa')) {
                $report->ppa = $request->ppa ?? '';
            }

            if (Schema::hasColumn('accomplishment_reports', 'otherActivity')) {
                $report->otherActivity = $request->otherActivity ?? '';
            }

            // Handle activity and ppaSi fields
            if (Schema::hasColumn('accomplishment_reports', 'activity')) {
                $activity = $request->activity ?? '';
                // If it's a JSON string, keep it as is; otherwise convert array to JSON
                if (is_array($activity)) {
                    $report->activity = json_encode($activity);
                } else {
                    $report->activity = $activity;
                }
            }

            if (Schema::hasColumn('accomplishment_reports', 'ppaSi')) {
                $ppaSi = $request->ppaSi ?? '';
                // If it's a JSON string, keep it as is; otherwise convert array to JSON
                if (is_array($ppaSi)) {
                    $report->ppaSi = json_encode($ppaSi);
                } else {
                    $report->ppaSi = $ppaSi;
                }
            }

            // Save the report first to get an ID
            $report->save();

            // Handle multiple file uploads after saving to get the report ID
            $attachmentPaths = [];

            // Check for multiple files
            if ($request->hasFile('attachments')) {
                $files = $request->file('attachments');
                foreach ($files as $file) {
                    try {
                        $filename = time() . '_' . $file->getClientOriginalName();
                        $path = $file->storeAs('accomplishment-report-attachments/' . $report->id, $filename, 'public');

                        if ($path) {
                            $attachmentPaths[] = $path;
                        }
                    } catch (\Exception $e) {
                        Log::error('Error storing file: ' . $e->getMessage());
                    }
                }
            }

            // Update attachment paths if we have any attachments
            if (!empty($attachmentPaths)) {
                $report->attachment_path = json_encode($attachmentPaths);
                $report->save(); // Save again to update attachment paths
            }

            Log::info('Accomplishment report draft saved successfully', ['id' => $report->id]);

            return response()->json([
                'success' => true,
                'message' => 'Accomplishment report draft saved successfully',
                'report' => $report
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving accomplishment report draft: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error saving accomplishment report draft',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create notification for accomplishment report status change
     */
    private function createReportNotification($report, $status, $message = null)
    {
        try {
            // Load the relationships to ensure we have the data
            $report->load(['user', 'barangay']);
            
            $title = '';
            $notificationMessage = '';
            $audience = null;
            $targetUserId = null;
            $targetBarangayId = null;

            switch ($status) {
                case 'pending':
                    // Create notification for admin
                    $title = 'New Accomplishment Report Submitted';
                    $userName = $report->user ? $report->user->name : 'Unknown User';
                    $barangayName = $report->barangay ? $report->barangay->name : 'Unknown Barangay';
                    $notificationMessage = $message ?? "{$userName} from Barangay {$barangayName} has submitted a new accomplishment report for review.";
                    $audience = 'admin';
                    $targetUserId = null; // Admin notification
                    $targetBarangayId = null;
                    
                    // Also create a notification for the submitting user
                    $userTitle = "You Submitted an Accomplishment Report";
                    $userMessage = "{$userName} from Barangay {$barangayName} has submitted a new accomplishment report for review.";
                    
                    Notification::create([
                        'user_id' => $report->user_id,
                        'barangay_id' => $report->barangay_id,
                        'type' => 'accomplishment',
                        'audience' => 'barangay',
                        'title' => $userTitle,
                        'message' => $userMessage,
                        'data' => [
                            'report_id' => $report->id,
                            'status' => $status,
                            'submitted_by' => $userName,
                            'barangay_name' => $barangayName,
                            'report_title' => $report->title
                        ],
                        'is_read' => false
                    ]);
                    break;
                    
                case 'approved':
                    $title = 'Accomplishment Report Approved';
                    $notificationMessage = $message ?? "Your accomplishment report '{$report->title}' has been approved by the admin.";
                    $audience = 'barangay';
                    $targetUserId = $report->user_id;
                    $targetBarangayId = $report->barangay_id;
                    // Add admin notification
                    $admin = \Auth::user();
                    if ($admin && $admin->role === 'admin') {
                        Notification::create([
                            'user_id' => $admin->id,
                            'barangay_id' => null,
                            'type' => 'accomplishment',
                            'audience' => 'admin',
                            'title' => 'You approved an accomplishment report',
                            'message' => "You approved the accomplishment report '{$report->title}'.",
                            'data' => [
                                'report_id' => $report->id,
                                'status' => $status
                            ],
                            'is_read' => false
                        ]);
                    }
                    break;
                    
                case 'revision':
                    $title = 'Accomplishment Report Requires Revision';
                    $notificationMessage = $message ?? "Your accomplishment report '{$report->title}' requires revision. Please review and resubmit.";
                    $audience = 'barangay';
                    $targetUserId = $report->user_id;
                    $targetBarangayId = $report->barangay_id;
                    // Add admin notification
                    $admin = \Auth::user();
                    if ($admin && $admin->role === 'admin') {
                        Notification::create([
                            'user_id' => $admin->id,
                            'barangay_id' => null,
                            'type' => 'accomplishment',
                            'audience' => 'admin',
                            'title' => 'You requested a revision for an accomplishment report',
                            'message' => "You requested a revision for the accomplishment report '{$report->title}'.",
                            'data' => [
                                'report_id' => $report->id,
                                'status' => $status
                            ],
                            'is_read' => false
                        ]);
                    }
                    break;
                case 'rejected':
                    $title = 'Accomplishment Report Requires Revision';
                    $notificationMessage = $message ?? "Your accomplishment report '{$report->title}' requires revision. Please review and resubmit.";
                    $audience = 'barangay';
                    $targetUserId = $report->user_id;
                    $targetBarangayId = $report->barangay_id;
                    // Add admin notification
                    $admin = \Auth::user();
                    if ($admin && $admin->role === 'admin') {
                        Notification::create([
                            'user_id' => $admin->id,
                            'barangay_id' => null,
                            'type' => 'accomplishment',
                            'audience' => 'admin',
                            'title' => 'You disapproved an accomplishment report',
                            'message' => "You disapproved the accomplishment report '{$report->title}'.",
                            'data' => [
                                'report_id' => $report->id,
                                'status' => $status
                            ],
                            'is_read' => false
                        ]);
                    }
                    break;
                case 'pending revision':
                    $title = 'Accomplishment Report Pending Revision';
                    $notificationMessage = $message ?? "Your accomplishment report '{$report->title}' has been revised and is pending admin review.";
                    $audience = 'admin';
                    $targetUserId = null;
                    $targetBarangayId = null;
                    // Also create a notification for the submitting user
                    $userTitle = "You Submitted a Revised Accomplishment Report";
                    $userMessage = "Your accomplishment report '{$report->title}' has been revised and is pending admin review.";
                    Notification::create([
                        'user_id' => $report->user_id,
                        'barangay_id' => $report->barangay_id,
                        'type' => 'accomplishment',
                        'audience' => 'barangay',
                        'title' => $userTitle,
                        'message' => $userMessage,
                        'data' => [
                            'report_id' => $report->id,
                            'status' => $status,
                            'report_title' => $report->title
                        ],
                        'is_read' => false
                    ]);
                    break;
            }

            // Create notification for other statuses (Approved, Revision, Rejected)
            if ($title && $notificationMessage && $status !== 'pending') {
                Notification::create([
                    'user_id' => $targetUserId,
                    'barangay_id' => $targetBarangayId,
                    'type' => 'accomplishment',
                    'audience' => $audience,
                    'title' => $title,
                    'message' => $notificationMessage,
                    'data' => [
                        'report_id' => $report->id,
                        'status' => $status,
                        'report_title' => $report->title
                    ],
                    'is_read' => false
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error creating accomplishment report notification: ' . $e->getMessage());
        }
    }
}























