<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use App\Models\Barangay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Notifications\SystemNotice;

class SystemNoticeController extends Controller
{
    /**
     * Create a system-wide notice/announcement
     */
    public function createNotice(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Only admin can create system notices
            if ($user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access. Only administrators can create system notices.'
                ], 403);
            }

            $request->validate([
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'type' => 'required|in:announcement,maintenance,update,reminder,urgent',
                'audience' => 'required|in:all,admin,barangay',
                'priority' => 'in:low,normal,high,urgent'
            ]);

            // Create a single system notice record
            $noticeData = [
                'title' => $request->title,
                'message' => $request->message,
                'type' => 'system',
                'audience' => $request->audience,
                'data' => [
                    'notice_type' => $request->type,
                    'priority' => $request->priority ?? 'normal',
                    'created_by' => $user->name,
                    'created_by_id' => $user->id,
                    'target_audience' => $request->audience
                ],
                'is_read' => false,
                'user_id' => null, // System notice, not tied to specific user
                'barangay_id' => null // System notice, not tied to specific barangay
            ];

            // Create only ONE notification record
            $notification = Notification::create($noticeData);

            // Broadcast to all users (system-wide notice)
            $allUsers = User::all();
            foreach ($allUsers as $targetUser) {
                $broadcastData = $noticeData;
                $broadcastData['user_id'] = $targetUser->id;
                $targetUser->notify(new SystemNotice($broadcastData));
            }

            Log::info('System notice created', [
                'id' => $notification->id,
                'title' => $request->title,
                'type' => $request->type,
                'audience' => $request->audience,
                'created_by' => $user->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'System notice created and sent successfully',
                'notice_id' => $notification->id
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating system notice: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating system notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a notice for specific barangay
     */
    public function createBarangayNotice(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Only admin can create barangay-specific notices
            if ($user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access. Only administrators can create barangay notices.'
                ], 403);
            }

            $request->validate([
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'barangay_id' => 'required|exists:barangays,id',
                'type' => 'required|in:announcement,reminder,urgent',
                'priority' => 'in:low,normal,high,urgent'
            ]);

            // Get all users from the specific barangay
            $barangayUsers = User::where('barangay_id', $request->barangay_id)->get();
            
            if ($barangayUsers->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No users found in the specified barangay'
                ], 404);
            }

            $barangay = Barangay::find($request->barangay_id);
            $barangayName = $barangay ? $barangay->name : 'Unknown Barangay';

            $noticeData = [
                'title' => $request->title,
                'message' => $request->message,
                'type' => 'system',
                'audience' => 'barangay',
                'data' => [
                    'notice_type' => $request->type,
                    'priority' => $request->priority ?? 'normal',
                    'created_by' => $user->name,
                    'created_by_id' => $user->id,
                    'target_barangay' => $barangayName,
                    'target_barangay_id' => $request->barangay_id
                ],
                'is_read' => false
            ];

            // Create notifications for each user in the barangay
            foreach ($barangayUsers as $barangayUser) {
                $noticeData['user_id'] = $barangayUser->id;
                $noticeData['barangay_id'] = $barangayUser->barangay_id;
                Notification::create($noticeData);
                // Broadcast to each barangay user
                $barangayUser->notify(new SystemNotice($noticeData));
            }

            Log::info('Barangay notice created', [
                'title' => $request->title,
                'barangay_id' => $request->barangay_id,
                'barangay_name' => $barangayName,
                'created_by' => $user->name
            ]);

            return response()->json([
                'success' => true,
                'message' => "Notice sent to {$barangayName} successfully"
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating barangay notice: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating barangay notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system notices for the authenticated user
     */
    public function getSystemNotices(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $query = Notification::where('type', 'system');

            // If the request is from SystemNoticeManager (all_audiences=1), show all audiences for admin
            if ($user->role === 'admin' && $request->has('all_audiences') && $request->all_audiences) {
                $query->whereIn('audience', ['admin', 'all', 'barangay']);
            } else if ($user->role === 'admin') {
                // Admin can see all system notices for admin/all
                $query->where(function ($q) {
                    $q->where('audience', 'admin')
                      ->orWhere('audience', 'all');
                });
            } else {
                // Barangay users can see barangay and all notices
                $query->where(function ($q) {
                    $q->where('audience', 'barangay')
                      ->orWhere('audience', 'all');
                });
            }

            // Filter by read status
            if ($request->has('unread_only') && $request->unread_only) {
                $query->where('is_read', false);
            }

            // Filter by notice type
            if ($request->has('notice_type')) {
                $query->where('data->notice_type', $request->notice_type);
            }

            // Filter by priority
            if ($request->has('priority')) {
                $query->where('data->priority', $request->priority);
            }

            $notices = $query->orderBy('created_at', 'desc')
                           ->with(['user', 'barangay'])
                           ->get()
                           ->map(function ($notification) {
                               return [
                                   'id' => $notification->id,
                                   'type' => $notification->type,
                                   'audience' => $notification->audience,
                                   'title' => $notification->title,
                                   'message' => $notification->message,
                                   'data' => $notification->data,
                                   'is_read' => $notification->is_read,
                                   'created_at' => $notification->created_at,
                                   'user_name' => $notification->user?->name,
                                   'barangay_name' => $notification->barangay?->name,
                               ];
                           });

            return response()->json([
                'success' => true,
                'notices' => $notices
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching system notices: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching system notices',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a system notice (admin only)
     */
    public function deleteNotice($id)
    {
        try {
            $user = Auth::user();
            
            if ($user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access. Only administrators can delete system notices.'
                ], 403);
            }

            $notification = Notification::where('type', 'system')->find($id);
            
            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'System notice not found'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'System notice deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting system notice: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting system notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
