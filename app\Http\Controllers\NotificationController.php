<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\BudgetPlan;
use App\Models\AccomplishmentReport;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Notifications\BudgetPlanStatus;
use App\Notifications\AccomplishmentReportStatus;
use App\Notifications\SystemNotice;
use App\Notifications\UserActivity;

class NotificationController extends Controller
{
    /**
     * Get notifications for the authenticated user
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $query = Notification::query();

            // Filter by user role and audience
            if ($user->role === 'admin') {
                // Admin sees admin notifications, system notices for admin/all, and their own notifications
                $query->where(function ($q) {
                    $q->where('audience', 'admin')
                      ->orWhere('audience', 'all')
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'admin')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            } else {
                // Barangay users see barangay notifications, system notices for barangay/all, and their own notifications
                $query->where(function ($q) use ($user) {
                    $q->where('audience', 'barangay')
                      ->orWhere('audience', 'all')
                      ->orWhere('barangay_id', $user->barangay_id)
                      ->orWhere('user_id', $user->id)
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'barangay')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            }

            // Filter by read status
            if ($request->has('unread_only') && $request->unread_only) {
                $query->unread();
            }

            // Filter by type
            if ($request->has('type')) {
                $query->ofType($request->type);
            }

            // Get notifications with pagination
            $notifications = $query->orderBy('created_at', 'desc')
                                  ->limit(50)
                                  ->get()
                                  ->map(function ($notification) {
                                      return [
                                          'id' => $notification->id,
                                          'type' => $notification->type,
                                          'audience' => $notification->audience,
                                          'title' => $notification->title,
                                          'message' => $notification->message,
                                          'data' => $notification->data,
                                          'unread' => !$notification->is_read,
                                          'time' => $notification->created_at->diffForHumans(),
                                          'created_at' => $notification->created_at,
                                          'user_name' => $notification->user?->name,
                                          'barangay_name' => $notification->barangay?->name,
                                      ];
                                  });

            $unreadCount = $query->unread()->count();

            return response()->json([
                'success' => true,
                'notifications' => $notifications,
                'unread_count' => $unreadCount
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching notifications: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark a notification as read
     */
    public function markAsRead(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $notification = Notification::findOrFail($id);

            // Check if user has permission to read this notification
            if ($user->role !== 'admin' && 
                $notification->user_id !== $user->id && 
                $notification->barangay_id !== $user->barangay_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $notification->markAsRead();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);

        } catch (\Exception $e) {
            Log::error('Error marking notification as read: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error marking notification as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request)
    {
        try {
            $user = Auth::user();
            
            $query = Notification::query();

            // Filter by user role and audience (same logic as index method)
            if ($user->role === 'admin') {
                // Admin can mark all admin notifications, system notices for admin/all, and their own notifications as read
                $query->where(function ($q) {
                    $q->where('audience', 'admin')
                      ->orWhere('audience', 'all')
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'admin')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            } else {
                // Barangay users can mark barangay notifications, system notices for barangay/all, and their own notifications as read
                $query->where(function ($q) use ($user) {
                    $q->where('audience', 'barangay')
                      ->orWhere('audience', 'all')
                      ->orWhere('barangay_id', $user->barangay_id)
                      ->orWhere('user_id', $user->id)
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'barangay')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            }

            $query->unread()->update([
                'is_read' => true,
                'read_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'All notifications marked as read'
            ]);

        } catch (\Exception $e) {
            Log::error('Error marking all notifications as read: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error marking all notifications as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread count
     */
    public function unreadCount()
    {
        try {
            $user = Auth::user();
            
            $query = Notification::query();

            // Filter by user role and audience (same logic as index method)
            if ($user->role === 'admin') {
                // Admin sees count of admin notifications, system notices for admin/all, and their own notifications
                $query->where(function ($q) {
                    $q->where('audience', 'admin')
                      ->orWhere('audience', 'all')
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'admin')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            } else {
                // Barangay users see count of barangay notifications, system notices for barangay/all, and their own notifications
                $query->where(function ($q) use ($user) {
                    $q->where('audience', 'barangay')
                      ->orWhere('audience', 'all')
                      ->orWhere('barangay_id', $user->barangay_id)
                      ->orWhere('user_id', $user->id)
                      ->orWhereNull('audience')
                      ->orWhere(function ($subQ) {
                          $subQ->where('type', 'system')
                               ->where(function ($sysQ) {
                                   $sysQ->where('audience', 'barangay')
                                        ->orWhere('audience', 'all');
                               });
                      });
                });
            }

            $count = $query->unread()->count();

            return response()->json([
                'success' => true,
                'unread_count' => $count
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching unread count: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching unread count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate notifications from existing data
     */
    public function generateFromExistingData()
    {
        try {
            // Generate notifications from budget plans
            $budgetPlans = BudgetPlan::with(['user', 'barangay'])->get();
            
            foreach ($budgetPlans as $plan) {
                // Check if notification already exists
                $existingNotification = Notification::where('type', 'budget')
                    ->where('data->budget_plan_id', $plan->id)
                    ->first();

                if (!$existingNotification && $plan->user) {
                    $status = $plan->status ?? 'pending';
                    $plan->user->notify(new BudgetPlanStatus($plan, $status));
                }
            }

            // Generate notifications from accomplishment reports
            $reports = AccomplishmentReport::with(['user', 'barangay'])->get();
            
            foreach ($reports as $report) {
                // Check if notification already exists
                $existingNotification = Notification::where('type', 'accomplishment')
                    ->where('data->report_id', $report->id)
                    ->first();

                if (!$existingNotification && $report->user) {
                    $status = $report->status ?? 'pending';
                    $report->user->notify(new AccomplishmentReportStatus($report, $status));
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Notifications generated from existing data'
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating notifications from existing data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error generating notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 