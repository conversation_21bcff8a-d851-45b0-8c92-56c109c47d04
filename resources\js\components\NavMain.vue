<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'
import { type NavItem } from '@/types'
import { Link } from '@inertiajs/vue3'

interface NavMainProps {
  items: (NavItem & { spaTab?: string })[]
}

const props = defineProps<NavMainProps>()
const emit = defineEmits<{
  'spa-tab': [tab: string]
}>()

function handleItemClick(item: NavItem & { spaTab?: string }) {
  if (item.spaTab) {
    emit('spa-tab', item.spaTab)
  }
}
</script>

<template>
  <SidebarGroup>
    <SidebarMenu class="space-y-1">
      <SidebarMenuItem v-for="item in items" :key="item.title">
        <SidebarMenuButton
          v-if="item.href"
          :as-child="true"
          @click="handleItemClick(item)"
          class="w-full text-emerald-700 hover:bg-emerald-200 hover:text-emerald-800 transition-all duration-200 rounded-lg group"
        >
          <Link :href="item.href" class="flex items-center gap-3 p-3">
            <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-emerald-100 group-hover:bg-emerald-300 transition-colors duration-200">
              <component v-if="item.icon" :is="item.icon" class="h-4 w-4" />
            </div>
            <span class="font-medium">{{ item.title }}</span>
          </Link>
        </SidebarMenuButton>
        <SidebarMenuButton
          v-else
          @click="handleItemClick(item)"
          class="w-full text-emerald-700 hover:bg-emerald-200 hover:text-emerald-800 transition-all duration-200 rounded-lg cursor-pointer group"
        >
          <div class="flex items-center gap-3 p-3">
            <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-emerald-100 group-hover:bg-emerald-300 transition-colors duration-200">
              <component v-if="item.icon" :is="item.icon" class="h-4 w-4" />
            </div>
            <span class="font-medium">{{ item.title }}</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
