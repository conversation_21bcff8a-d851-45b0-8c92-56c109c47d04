<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plan_id');
            $table->string('column_key');
            $table->integer('row_index')->default(0);
            $table->string('status')->nullable();
            $table->unsignedBigInteger('author_id');
            $table->text('text');
            $table->string('color')->nullable();
            $table->timestamps();

            $table->index(['plan_id', 'column_key', 'row_index', 'author_id']);
            // Uncomment if you want foreign keys:
            // $table->foreign('plan_id')->references('id')->on('plans')->onDelete('cascade');
            // $table->foreign('author_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
}; 