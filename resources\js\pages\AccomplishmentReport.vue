<template>
  <div class="bg-white flex font-sans w-full">
    <div class="px-6 py-4 w-full">
      <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-4 gap-3">
        <h1 class="text-2xl sm:text-3xl font-bold text-green-900 flex items-center gap-2 flex-shrink-0">
          <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3-1.343-3-3-3zm0 0V4m0 0a8 8 0 100 16v-4"></path></svg>
          <span class="hidden sm:inline">GAD Focal Report</span>
          <span class="hidden">Report</span>
        </h1>
        <div class="relative">
          <div v-if="deadlineLoading" class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-emerald-200">
            <svg class="animate-spin h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
            <span class="text-emerald-700 text-xs font-medium">Loading deadline...</span>
          </div>
          <div v-else-if="!deadlinePassed && deadline"
               class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-emerald-200 hover:shadow-xl hover:shadow-emerald-200/50 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-105">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
              <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="font-medium">Deadline:</span>
            </div>
            <div class="flex flex-col">
              <span class="text-emerald-900 font-bold">{{ deadlineDisplay }}</span>
            </div>
          </div>
          <div v-else-if="deadlinePassed"
               class="inline-flex items-center gap-2 bg-gradient-to-r from-red-50 to-rose-50 text-red-800 px-4 py-3 rounded-xl text-sm font-semibold shadow-lg border border-red-200">
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
              <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              <span class="font-bold text-red-700">Deadline Passed</span>
            </div>
          </div>
          <div v-else
               class="inline-flex items-center gap-2 bg-gradient-to-r from-gray-50 to-slate-50 text-gray-600 px-4 py-3 rounded-xl text-sm font-medium shadow-md border border-gray-200">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>No deadline set</span>
          </div>
        </div>
      </div>

      <!-- Create Report Button -->
      <div class="flex flex-col sm:flex-row mb-4 gap-2">
        <button
          type="button"
          class="w-full sm:w-auto px-4 py-2 text-sm sm:text-base font-semibold bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg shadow-md flex items-center justify-center gap-2 transition-all duration-200 border-none focus:ring-2 focus:ring-green-300 focus:outline-none cursor-pointer"
          @click="showModal = true"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
          <span class="hidden sm:inline">New Accomplishment Report</span>
        </button>
      </div>

      <!-- Report Table -->
      <div class="bg-white">
        <MyAccomplishmentReportTable
          :reports="myReports"
          :loading="loading"
          @download="handleDownloadMyReport"
        />
      </div>

      <!-- Create Report Modal -->
      <div v-if="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-2">
        <div class="bg-gradient-to-br from-emerald-700 to-emerald-900 rounded-xl shadow-xl p-2 sm:p-3 md:p-4 w-full max-w-6xl mx-auto flex flex-col overflow-y-auto min-h-[300px] max-h-[90vh] invisible-scrollbar">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-white">New Accomplishment Report</h2>
            
          </div>

          <form @submit.prevent="submitReport" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-x-3 gap-y-3 w-full max-w-full">
              <!-- Left Column -->
              <div class="flex flex-col gap-3 min-w-0 flex-1">
                <select v-model="reportForm.focused"
                        class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                        required>
                  <option disabled value="">Focused (required)</option>
                  <option>CLIENT-FOCUSED</option>
                  <option>ORGANIZATION-FOCUSED</option>
                </select>

                <select v-model="reportForm.gadMandate"
                        class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                        required>
                  <option disabled value="">Gender Issue or GAD Mandate</option>
                  <option>Gender Issue</option>
                  <option>GAD Mandate</option>
                </select>

                <textarea v-model="reportForm.title"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="Title/Description of Gender Issue or GAD Mandate (required)"></textarea>

                <textarea v-model="reportForm.supportingData"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          placeholder="Supporting Statistics Data (optional)"></textarea>

                <textarea v-model="reportForm.dataSource"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          placeholder="Source of Supporting Statistics Data (optional)"></textarea>

                <div class="relative">
                  <div
                    class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 cursor-pointer flex items-center justify-between transition-all hover:bg-emerald-50"
                    @click="ppaSiDropdownOpen = !ppaSiDropdownOpen"
                    tabindex="0"
                    @blur="ppaSiDropdownOpen = false"
                  >
                    <span class="flex flex-wrap gap-1 min-h-[1.5em]">
                      <template v-if="reportForm.ppaSi && reportForm.ppaSi.length">
                        <span v-for="cat in reportForm.ppaSi" :key="cat" class="inline-flex items-center bg-emerald-500 text-white rounded-md px-2.5 py-1 text-xs mr-1 mb-1 max-w-[120px]">
                          <span class="truncate">{{ cat.charAt(0).toUpperCase() + cat.slice(1) }}</span>
                          <button type="button" class="ml-1 text-white hover:text-red-200 focus:outline-none flex-shrink-0" @click.stop="removePpaSi(cat)">×</button>
                        </span>
                      </template>
                      <template v-else>
                        <span class="text-gray-400">PPA/s</span>
                      </template>
                    </span>
                    <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg>
                  </div>
                  <div
                    v-if="ppaSiDropdownOpen"
                    class="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg p-2 ring-1 ring-emerald-100"
                    @mousedown.prevent
                  >
                    <label class="flex items-center mb-2 text-sm">
                      <input type="checkbox" class="mr-2 rounded text-emerald-500 focus:ring-emerald-500" :checked="isAllPpaSiSelected" @change="toggleAllPpaSi" /> Select All
                    </label>
                    <div class="divide-y divide-emerald-50 max-h-60 overflow-y-auto">
                      <div
                        v-for="opt in ppaSiOptions"
                        :key="opt"
                        class="py-1.5 px-2 cursor-pointer hover:bg-emerald-50 flex items-center text-sm"
                        :class="{'font-medium text-emerald-700': reportForm.ppaSi && reportForm.ppaSi.includes(opt)}"
                        @click="toggleSinglePpaSi(opt)"
                      >
                        <span>{{ opt.charAt(0).toUpperCase() + opt.slice(1) }}</span>
                        <span v-if="reportForm.ppaSi && reportForm.ppaSi.includes(opt)" class="ml-auto text-emerald-600">✓</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Middle Column -->
              <div class="flex flex-col gap-3 min-w-0 flex-1">
                <textarea v-model="reportForm.gadObjective"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="GAD Objective (required)"></textarea>

                <textarea v-model="reportForm.lguPpa"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="Relevant LGU PPA"></textarea>

                <div>
                  <div class="relative">
                    <div
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 cursor-pointer flex items-center justify-between transition-all hover:bg-emerald-50"
                      @click="activityDropdownOpen = !activityDropdownOpen"
                      tabindex="0"
                      @blur="activityDropdownOpen = false"
                    >
                      <span class="flex flex-wrap gap-1 min-h-[1.5em] flex-grow">
                        <template v-if="reportForm.activity && reportForm.activity.length">
                          <span v-for="act in reportForm.activity" :key="act" class="inline-flex items-center bg-emerald-500 text-white rounded-md px-2.5 py-1 text-xs mr-1 mb-1 max-w-[200px]">
                            <span class="truncate">{{ act }}</span>
                            <button type="button" class="ml-1 text-white hover:text-red-200 focus:outline-none flex-shrink-0" @click.stop="removeActivity(act)">×</button>
                          </span>
                        </template>
                        <template v-else>
                          <span class="text-gray-400">Activity</span>
                        </template>
                      </span>
                      <span class="flex items-center space-x-1 flex-shrink-0 self-start">
                        <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg>
                        <button
                          v-if="reportForm.activity && reportForm.activity.length"
                          type="button"
                          class="w-5 h-5 flex items-center justify-center text-emerald-500 hover:text-red-500 rounded-full focus:outline-none"
                          @click.stop="clearActivityDropdown"
                          title="Clear selected activities"
                        >
                          <span class="text-base leading-none">&times;</span>
                        </button>
                      </span>
                    </div>
                    <div
                      v-if="activityDropdownOpen"
                      class="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg p-2 ring-1 ring-emerald-100"
                      @mousedown.prevent
                    >
                      <label class="flex items-center mb-2 text-sm">
                        <input type="checkbox" class="mr-2 rounded text-emerald-500 focus:ring-emerald-500" :checked="isAllActivitySelected" @change="toggleAllActivity" /> Select All
                      </label>
                      <div class="divide-y divide-emerald-50 max-h-60 overflow-y-auto">
                        <div
                          v-for="opt in activityOptions"
                          :key="opt"
                          class="py-1.5 px-2 cursor-pointer hover:bg-emerald-50 flex items-center text-sm"
                          :class="{'font-medium text-emerald-700': reportForm.activity && reportForm.activity.includes(opt)}"
                          @click="toggleSingleActivity(opt)"
                        >
                          <span>{{ opt }}</span>
                          <span v-if="reportForm.activity && reportForm.activity.includes(opt)" class="ml-auto text-emerald-600">✓</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="text-xs text-emerald-200/70 italic mb-1">Specify here if the activity category is not listed on the selection</div>
                  <textarea v-model="reportForm.otherActivity"
                            class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                            placeholder="Other Activity Category Value"></textarea>
                </div>

                <textarea v-model="reportForm.gadActivity"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="GAD Activity"></textarea>

                <textarea v-model="reportForm.performanceIndicator"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="Performance Indicator and Target"></textarea>
              </div>

              <!-- Right Column -->
              <div class="flex flex-col gap-3 min-w-0 flex-1">
                <textarea v-model="reportForm.actualResults"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          required
                          placeholder="Actual Results"></textarea>

                <input v-model="reportForm.approvedBudget"
                       type="text"
                       inputmode="numeric"
                       class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                       placeholder="Approved GAD Budget" />

                <input v-model="reportForm.actualCost"
                       type="text"
                       inputmode="numeric"
                       class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                       placeholder="Actual Cost or Expenditure" />

                <textarea v-model="reportForm.remarks"
                          class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                          placeholder="Variance / Remarks"></textarea>
                <div class="mt-4">
          <label class="block text-sm font-small text-white">Supporting Documents (Up to 10 files)</label>
          <div class="mt-1">
            <label class="flex items-center px-4 py-2 bg-white text-emerald-600 rounded-md shadow-sm border border-gray-300 cursor-pointer hover:bg-emerald-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
              <span>Choose Files</span>
              <input
                type="file"
                id="attachments"
                ref="fileInput"
                class="hidden"
                @change="handleAttachments"
                multiple
              />
            </label>
            <div class="mt-2">
              <ul v-if="form.attachments && form.attachments.length > 0" class="space-y-2">
                <li v-for="(file, index) in form.attachments" :key="index" class="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <div class="flex items-center">
                    <svg class="h-4 w-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="text-sm text-gray-700">{{ file.name }}</span>
                  </div>
                  <button
                    @click="removeAttachment(index)"
                    type="button"
                    class="text-sm text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </li>
              </ul>
              <p v-else class="text-sm text-gray-200">No files selected</p>
            </div>
          </div>
        </div>
              </div>
            </div>

            <!-- Footer Buttons -->
            <div class="flex flex-col sm:flex-row justify-end gap-2 mt-6 shrink-0">
              <button type="button" class="px-3 py-1.5 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors text-sm" @click="closeCreateModal" :disabled="loading">Cancel</button>
              <button
                type="button"
                class="px-3 py-1.5 rounded-lg bg-amber-50/90 hover:bg-amber-100 text-amber-700 font-medium transition-colors text-sm"
                @click="confirmSaveDraft"
                :disabled="loading"
              >
                <span v-if="loading && savingDraft" class="inline-block animate-spin mr-1">↻</span>
                Save as Draft
              </button>
              <button
                type="button"
                class="px-3 py-1.5 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium transition-colors text-sm"
                @click="confirmSubmit"
                :disabled="loading"
              >
                <span v-if="loading && !savingDraft" class="inline-block animate-spin mr-1">↻</span>
                <span v-else>Submit Report</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Custom Alert/Confirm Modal -->
      <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
              <!-- Error Icon -->
              <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              <!-- Success Icon -->
              <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <!-- Info/Confirm Icon -->
              <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
          </div>
          <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
          <div class="flex justify-end gap-3">
            <button
              v-if="modalType === 'confirm'"
              @click="closeModal"
              class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              @click="modalType === 'confirm' ? confirmAction() : closeModal()"
              class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
            >
              {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
            </button>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { usePage } from '@inertiajs/vue3';
import AppSidebarHeaderBarangay from '@/components/AppSidebarHeaderBarangay.vue';
import MyAccomplishmentReportTable from '@/components/BarangayReportTable.vue';
import axios from 'axios';

const showModal = ref(false);
const loading = ref(false);
const savingDraft = ref(false);

// Modal states
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref('info'); // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref(null);

// Initialize with empty array to ensure instant table display
const myReports = ref([]);

// Reports will be loaded in the main onMounted function below

// Add this to save reports to localStorage when they change
function saveReportsToLocalStorage() {
  // Filter out the sample data (IDs 1 and 2)
  const reportsToSave = myReports.value.filter(report => report.id > 2);
  localStorage.setItem('my_accomplishment_reports', JSON.stringify(reportsToSave));
}

const barangayReports = ref([]);

const preparedByInfo = {
  name: 'LIBERTAD VILLEGAS - REGIS, MPA',
  title: 'Chairperson, GFPS TWG'
};

const approvedByInfo = {
  name: 'JOSE E. RELAMPAGOS',
  title: 'Local Chief Executive'
};

const reportForm = reactive({
  user_id: '',
  barangay_id: '',
  focused: '',
  gadMandate: '',
  title: '',
  gadObjective: '',
  lguPpa: '',
  gadActivity: '',
  performanceIndicator: '',
  actualResults: '',
  approvedBudget: null,
  actualCost: null,
  remarks: '',
  status: 'pending',
  supportingData: '',
  dataSource: '',
  ppa: '',
  otherActivity: '',
  activity: '',
  ppaSi: [], // Add this line
});

const activityDropdownOpen = ref(false);

// Initialize activity as an array
if (!Array.isArray(reportForm.activity)) reportForm.activity = [];

// Activity options
const activityOptions = [
  'Capdev/Training - gender sensitivity trainings (GST)',
  'Capdev/Training - gender analysis',
  'Capdev/Training - gender responsive planning and budgeting',
  'Capdev/Training - gad related policies',
  'development of IEC materials',
  'PPAs related to the implementation of republic act no. 10354-reproductive health law',
  'establishment of violence against women and their children (VAWC) Center',
  'establishment / Maintenance of day care center',
  'establishment / Maintenance of Women Crisis Center',
  'establishment / Maintenance of haftway Houses for traficked women and girls',
  'institutional meachanism to implement the MCW - creation and/or strengthening the LGU GFPS',
  'institutional meachanism to implement the MCW - establishment & maintenance of GAD database',
  'institutional meachanism to implement the MCW - GAD planning and budgetting',
  'institutional meachanism to implement the MCW - mainstreaming gender perspective in lolcal development plans',
  'institutional meachanism to implement the MCW - Development plans',
  'institutional meachanism to implement the MCW - formulation/enhancement and implementation of the lgu GAD code',
  'provision of the child and material healthcare programs',
  'establishment / maintenance of the child development center',
  'Establishment of gender based violence (GBV) reporting and referral mechanisms',
  'GAD related laws / policies (with gender issues, clearly stated)',
  'provision of targeted assistance/support such as livelihood programs to vurnerable, unemployed and/ or indigent women heads of families or single mothers',
];

// Computed property to check if all activities are selected
const isAllActivitySelected = computed(() => {
  return activityOptions.every(opt => reportForm.activity.includes(opt));
});

// Function to toggle all activities
function toggleAllActivity() {
  if (isAllActivitySelected.value) {
    reportForm.activity = [];
  } else {
    reportForm.activity = [...activityOptions];
  }
}

// Function to toggle a single activity
function toggleSingleActivity(opt) {
  if (reportForm.activity.includes(opt)) {
    reportForm.activity = reportForm.activity.filter(o => o !== opt);
  } else {
    reportForm.activity = [...reportForm.activity, opt];
  }
}

// Function to remove an activity
function removeActivity(act) {
  reportForm.activity = reportForm.activity.filter(o => o !== act);
}

// Function to clear all activities
function clearActivityDropdown() {
  reportForm.activity = [];
  activityDropdownOpen.value = false;
}

function closeCreateModal() {
  showModal.value = false;
  Object.keys(reportForm).forEach(key => {
    if (key === 'approvedBudget' || key === 'actualCost') {
      reportForm[key] = null;
    } else {
      reportForm[key] = '';
    }
  });
}

// Modal functions
function showModalPopup(title, message, type = 'info') {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = type;
  modalConfirmCallback.value = null;
  modalVisible.value = true;
}

function showConfirmModal(title, message, onConfirm) {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = 'confirm';
  modalConfirmCallback.value = onConfirm;
  modalVisible.value = true;
}

function closeModal() {
  modalVisible.value = false;
  modalConfirmCallback.value = null;
}

function confirmAction() {
  if (modalConfirmCallback.value) {
    modalConfirmCallback.value();
  }
  closeModal();
}

// Confirmation functions for draft and submit
function confirmSaveDraft() {
  showConfirmModal(
    'Save as Draft',
    'Are you sure you want to save this accomplishment report as a draft? You can edit it later before submitting.',
    () => {
      storeDraft();
    }
  );
}

function confirmSubmit() {
  showConfirmModal(
    'Submit Report',
    'Are you sure you want to submit this accomplishment report for review? Once submitted, you cannot edit it until it is reviewed.',
    () => {
      submitReport();
    }
  );
}

async function submitReport() {
  try {
    loading.value = true;
    savingDraft.value = false;

    // Get the authenticated user
    const user = usePage().props.auth.user;

    // Create form data to handle file uploads
    const formData = new FormData();

    // Add all the report fields
    formData.append('user_id', user.id);
    formData.append('barangay', user.barangay || '');
    formData.append('barangay_id', user.id);
    formData.append('title', reportForm.title);
    formData.append('genderIssue', reportForm.gadMandate === 'Gender Issue' ? 'Gender Issue' : '');
    formData.append('gadMandate', reportForm.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : '');
    formData.append('focused', reportForm.focused);
    formData.append('gadObjective', reportForm.gadObjective);
    formData.append('lguPpa', reportForm.lguPpa);
    formData.append('gadActivity', reportForm.gadActivity);
    formData.append('performanceIndicator', reportForm.performanceIndicator);
    formData.append('actualResults', reportForm.actualResults);
    formData.append('approvedBudget', reportForm.approvedBudget ? Number(reportForm.approvedBudget) : 0);
    formData.append('actualCost', reportForm.actualCost ? Number(reportForm.actualCost) : 0);
    formData.append('remarks', reportForm.remarks);
    formData.append('status', 'pending');
    formData.append('dateSubmitted', new Date().toISOString().slice(0, 10));
    formData.append('supportingData', reportForm.supportingData || '');
    formData.append('dataSource', reportForm.dataSource || '');
    formData.append('ppa', reportForm.ppa || '');
    formData.append('otherActivity', reportForm.otherActivity || '');

    // Add attachments
    if (form.attachments && form.attachments.length > 0) {
      form.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file);
      });
    }

    // Send the request
    const response = await axios.post('/api/accomplishment-reports', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.success) {
      // Add the new report to the beginning of the array for immediate display
      myReports.value.unshift(response.data.report);
      // Update localStorage cache
      localStorage.setItem('my_accomplishment_reports', JSON.stringify(myReports.value));
      closeCreateModal();
      showModalPopup('Success', 'Report submitted successfully!', 'success');
    } else {
      throw new Error(response.data.message || 'Unknown error');
    }
  } catch (error) {
    console.error('Error submitting report:', error);
    showModalPopup('Error', 'Error submitting report: ' + (error.response?.data?.message || error.message), 'error');
  } finally {
    loading.value = false;
  }
}

function handleDownloadMyReport(report) {
  // Implement download logic
  console.log('Downloading report:', report);
}

function handleViewBarangayReport(report) {
  selectedReport.value = report;
  showViewModal.value = true;
}

function handleDownloadBarangayReport(report) {
  // Implement download logic
  console.log('Downloading barangay report:', report);
}

const globalDeadline = ref(null);
const deadline = ref(null);
const deadlineLoading = ref(false);
const deadlineDisplay = computed(() => {
  if (!deadline.value) return '-';
  const datePart = deadline.value.toLocaleDateString('en-US', {
    month: 'long', day: 'numeric', year: 'numeric'
  });
  const timePart = deadline.value.toLocaleTimeString('en-US', {
    hour: '2-digit', minute: '2-digit', hour12: true
  });
  return `${datePart} at ${timePart}`;
});
const countdown = ref('');
const deadlinePassed = ref(false);
let intervalId = null;

async function fetchAccomplishmentDeadline() {
  deadlineLoading.value = true;
  try {
    const response = await axios.get('/accomplishment-report-deadline');
    if (response.data.success && response.data.deadline) {
      globalDeadline.value = response.data.deadline;
      deadline.value = new Date(response.data.deadline);
    } else {
      globalDeadline.value = null;
      deadline.value = null;
    }
  } catch (error) {
    globalDeadline.value = null;
    deadline.value = null;
  } finally {
    deadlineLoading.value = false;
  }
}

function updateCountdown() {
  if (!deadline.value) {
    countdown.value = '';
    deadlinePassed.value = false;
    return;
  }
  const now = new Date();
  const diff = deadline.value.getTime() - now.getTime();
  if (diff <= 0) {
    countdown.value = '';
    deadlinePassed.value = true;
    return;
  }
  deadlinePassed.value = false;

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);

  if (days > 0) {
    countdown.value = `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    countdown.value = `${hours}h ${minutes}m`;
  } else {
    countdown.value = `${minutes}m`;
  }
}

// Add storage event listener to sync deadline changes
function syncDeadline(e) {
  if (e.key === 'accomplishment_deadline') {
    globalDeadline.value = localStorage.getItem('accomplishment_deadline');
    deadline.value = globalDeadline.value ? new Date(globalDeadline.value) : null;
    updateCountdown();
  }
}

async function fetchReportsWithDelay() {
  loading.value = true;
  myReports.value = []; // Clear the table while fetching, even if you have cache
  const start = Date.now();
  await fetchReports();
  const elapsed = Date.now() - start;
  if (elapsed < 500) {
    await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
  }
  loading.value = false;
}

onMounted(() => {
  loadReportsFromCache();
  fetchReportsWithDelay();
  fetchAccomplishmentDeadline();
  updateCountdown();
  intervalId = setInterval(updateCountdown, 60000);
});

// Load reports from localStorage for instant display
function loadReportsFromCache() {
  try {
    const savedReports = localStorage.getItem('my_accomplishment_reports');
    if (savedReports) {
      const parsedReports = JSON.parse(savedReports);
      if (Array.isArray(parsedReports) && parsedReports.length > 0) {
        myReports.value = parsedReports;
      }
    }
  } catch (error) {
    console.error('Error loading reports from cache:', error);
  }
}

// Separate function for fetching reports to optimize performance
async function fetchReports() {
  try {
    const response = await axios.get('/api/accomplishment-reports');
    if (response.data.success) {
      myReports.value = response.data.reports;
      // Save to localStorage for instant loading next time
      localStorage.setItem('my_accomplishment_reports', JSON.stringify(response.data.reports));
    }
  } catch (error) {
    console.error('Error fetching reports:', error);
    // Don't clear the array if we have cached data
    if (myReports.value.length === 0) {
      myReports.value = [];
    }
  }
}

// Function to handle report updates efficiently
function handleReportUpdate(updatedReport) {
  const index = myReports.value.findIndex(r => r.id === updatedReport.id);
  if (index !== -1) {
    myReports.value[index] = updatedReport;
  }
}
onUnmounted(() => {
  if (intervalId) clearInterval(intervalId);
});

async function storeDraft() {
  try {
    loading.value = true;
    savingDraft.value = true;

    const user = usePage().props.auth.user;

    // Create form data to handle file uploads
    const formData = new FormData();

    // Add all the report fields
    formData.append('user_id', user.id);
    formData.append('barangay', user.barangay || '');
    formData.append('barangay_id', user.barangay_id || user.id);
    formData.append('title', reportForm.title || '');
    formData.append('genderIssue', reportForm.gadMandate === 'Gender Issue' ? 'Gender Issue' : '');
    formData.append('gadMandate', reportForm.gadMandate === 'GAD Mandate' ? 'GAD Mandate' : '');
    formData.append('focused', reportForm.focused || '');
    formData.append('gadObjective', reportForm.gadObjective || '');
    formData.append('lguPpa', reportForm.lguPpa || '');
    formData.append('gadActivity', reportForm.gadActivity || '');
    formData.append('performanceIndicator', reportForm.performanceIndicator || '');
    formData.append('actualResults', reportForm.actualResults || '');
    formData.append('approvedBudget', reportForm.approvedBudget ? Number(reportForm.approvedBudget) : 0);
    formData.append('actualCost', reportForm.actualCost ? Number(reportForm.actualCost) : 0);
    formData.append('remarks', reportForm.remarks || '');
    formData.append('supportingData', reportForm.supportingData || '');
    formData.append('dataSource', reportForm.dataSource || '');
    formData.append('ppa', reportForm.ppa || '');
    formData.append('otherActivity', reportForm.otherActivity || '');

    // Handle activity array
    if (Array.isArray(reportForm.activity)) {
      formData.append('activity', JSON.stringify(reportForm.activity));
    } else {
      formData.append('activity', reportForm.activity || '');
    }

    // Handle ppaSi array
    if (Array.isArray(reportForm.ppaSi)) {
      formData.append('ppaSi', JSON.stringify(reportForm.ppaSi));
    } else {
      formData.append('ppaSi', reportForm.ppaSi || '');
    }

    // Add attachments
    if (form.attachments && form.attachments.length > 0) {
      form.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file);
      });
    }

    // Send the request
    const response = await axios.post('/api/accomplishment-reports/draft', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.success) {
      // Add the new report to the beginning of the array for immediate display
      myReports.value.unshift(response.data.report);
      // Update localStorage cache
      localStorage.setItem('my_accomplishment_reports', JSON.stringify(myReports.value));
      closeCreateModal();
      showModalPopup('Success', 'Report saved as draft successfully!', 'success');
    } else {
      throw new Error(response.data.message || 'Unknown error');
    }
  } catch (error) {
    console.error('Error saving draft:', error);
    showModalPopup('Error', 'Error saving draft: ' + (error.response?.data?.message || error.message), 'error');
  } finally {
    loading.value = false;
    savingDraft.value = false;
  }
}

const ppaSiDropdownOpen = ref(false);

// PPA SI options
const ppaSiOptions = [
  'economic',
  'infrastructure',
  'environmental',
  'institutional',
];

// Initialize ppaSi as an array
if (!reportForm.ppaSi) reportForm.ppaSi = [];
if (!Array.isArray(reportForm.ppaSi)) reportForm.ppaSi = [];

// Computed property to check if all PPA/s are selected
const isAllPpaSiSelected = computed(() => {
  return ppaSiOptions.every(opt => reportForm.ppaSi.includes(opt));
});

// Function to toggle all PPA/s
function toggleAllPpaSi() {
  if (isAllPpaSiSelected.value) {
    reportForm.ppaSi = [];
  } else {
    reportForm.ppaSi = [...ppaSiOptions];
  }
}

// Function to toggle a single PPA/s
function toggleSinglePpaSi(opt) {
  if (reportForm.ppaSi.includes(opt)) {
    reportForm.ppaSi = reportForm.ppaSi.filter(o => o !== opt);
  } else {
    reportForm.ppaSi = [...reportForm.ppaSi, opt];
  }
}

// Function to remove a PPA/s
function removePpaSi(cat) {
  reportForm.ppaSi = reportForm.ppaSi.filter(o => o !== cat);
}

const form = reactive({
  attachments: []
});

function handleAttachments(event) {
  const files = event.target.files;
  if (files && files.length) {
    // Check if adding these files would exceed the limit
    if (form.attachments.length + files.length > 10) {
      showModalPopup('File Limit Exceeded', 'You can only upload up to 10 files', 'error');
      return;
    }

    // Add the files to the attachments array
    for (let i = 0; i < files.length; i++) {
      form.attachments.push(files[i]);
    }
  }
}

function removeAttachment(index) {
  form.attachments.splice(index, 1);
}

// Function to parse attachment paths from JSON string
function getAttachmentPaths(attachmentPath) {
  if (!attachmentPath) return [];

  // Handle JSON array of paths
  if (typeof attachmentPath === 'string' && attachmentPath.startsWith('[')) {
    try {
      return JSON.parse(attachmentPath);
    } catch (e) {
      console.error('Error parsing attachment paths:', e);
      return [attachmentPath];
    }
  }

  // Handle single path
  return [attachmentPath];
}

// Function to get the filename from a path
function getAttachmentName(path) {
  if (!path) return '';
  return path.split('/').pop();
}

// Function to download an attachment
function downloadAttachment(path) {
  if (!path) return;
  window.open('/storage/' + path, '_blank');
}

const reportTabActive = ref(true); // Assume this is set to true when the report tab is active

function onReportTabClick() {
  fetchReportsWithDelay();
}
</script>

<style scoped>
.invisible-scrollbar::-webkit-scrollbar {
  display: none;
}
.invisible-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>

























