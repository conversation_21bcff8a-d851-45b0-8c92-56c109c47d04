<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class CustomEmailVerificationNotification extends VerifyEmail implements ShouldQueue, ShouldBroadcast
{
    use Queueable;

    public $data;

    public function __construct($data = [])
    {
        $this->data = $data;
    }

    public function via($notifiable)
    {
        return ['mail', 'database', 'broadcast'];
    }

    public function toArray($notifiable)
    {
        return [
            'type' => 'verification',
            'title' => 'Email Verification',
            'message' => 'Please verify your email address.',
            'data' => $this->data,
            'user_id' => $notifiable->id,
            'created_at' => now()->toDateTimeString(),
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->toArray($notifiable));
    }

    public function broadcastOn()
    {
        return ['App.Models.User.' . $notifiable->id];
    }

    public function broadcastType()
    {
        return 'NotificationPushed';
    }
}
