2025-05-24 12:22:23,808 - INFO - <PERSON><PERSON><PERSON> started
2025-05-24 12:22:23,809 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)", "Capdev/Training - gad related policies"], "scores": [5, 4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gad related policies": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-24 12:22:23,809 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)", "Capdev/Training - gad related policies"], "scores": [5, 4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gad related policies": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-24 12:22:23,810 - INFO - Activities: ['Capdev/Training - gender sensitivity trainings (GST)', 'Capdev/Training - gad related policies']
2025-05-24 12:22:23,810 - INFO - Scores: [5, 4]
2025-05-24 12:22:23,810 - INFO - Total Budget: 91407936.05
2025-05-24 12:22:23,810 - INFO - Manual Allocations: {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gad related policies": {"mooe": 50000, "ps": 0, "co": 0}}
2025-05-24 12:22:23,810 - INFO - Min allocation: 9140.793605
2025-05-24 12:22:23,810 - INFO - Max allocation: 100000.2820387
2025-05-24 12:22:23,810 - INFO - Running linprog optimization...
2025-05-24 12:22:23,830 - INFO - Optimization result:         message: Optimization terminated successfully. (HiGHS Status 7: Optimal)
        success: True
         status: 0
            fun: -900002.5383482999
              x: [ 1.000e+05  1.000e+05]
            nit: 0
          lower:  residual: [ 9.086e+04  9.086e+04]
                 marginals: [ 0.000e+00  0.000e+00]
          upper:  residual: [ 0.000e+00  0.000e+00]
                 marginals: [-5.000e+00 -4.000e+00]
          eqlin:  residual: []
                 marginals: []
        ineqlin:  residual: [ 9.121e+07]
                 marginals: [-0.000e+00]
 mip_node_count: 0
 mip_dual_bound: 0.0
        mip_gap: 0.0
2025-05-24 12:22:23,830 - INFO - LP allocations: {'Capdev/Training - gender sensitivity trainings (GST)': 100000.2820387, 'Capdev/Training - gad related policies': 100000.2820387}
2025-05-24 12:22:23,831 - INFO - Combined results: {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gad related policies": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}}
2025-05-24 12:22:23,832 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gad related policies": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 900002.5383482999}
2025-05-24 12:30:28,172 - INFO - Script started
2025-05-24 12:30:28,173 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)", "Capdev/Training - gender responsive planning and budgeting", "Capdev/Training - gender analysis"], "scores": [5, 4, 5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender responsive planning and budgeting": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender analysis": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-24 12:30:28,173 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)", "Capdev/Training - gender responsive planning and budgeting", "Capdev/Training - gender analysis"], "scores": [5, 4, 5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender responsive planning and budgeting": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender analysis": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-24 12:30:28,173 - INFO - Activities: ['Capdev/Training - gender sensitivity trainings (GST)', 'Capdev/Training - gender responsive planning and budgeting', 'Capdev/Training - gender analysis']
2025-05-24 12:30:28,173 - INFO - Scores: [5, 4, 5]
2025-05-24 12:30:28,173 - INFO - Total Budget: 91407936.05
2025-05-24 12:30:28,174 - INFO - Manual Allocations: {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender responsive planning and budgeting": {"mooe": 50000, "ps": 0, "co": 0}, "Capdev/Training - gender analysis": {"mooe": 50000, "ps": 0, "co": 0}}
2025-05-24 12:30:28,174 - INFO - Min allocation: 9140.793605
2025-05-24 12:30:28,174 - INFO - Max allocation: 100000.2820387
2025-05-24 12:30:28,174 - INFO - Running linprog optimization...
2025-05-24 12:30:28,200 - INFO - Optimization result:         message: Optimization terminated successfully. (HiGHS Status 7: Optimal)
        success: True
         status: 0
            fun: -1400003.9485418
              x: [ 1.000e+05  1.000e+05  1.000e+05]
            nit: 0
          lower:  residual: [ 9.086e+04  9.086e+04  9.086e+04]
                 marginals: [ 0.000e+00  0.000e+00  0.000e+00]
          upper:  residual: [ 0.000e+00  0.000e+00  0.000e+00]
                 marginals: [-5.000e+00 -4.000e+00 -5.000e+00]
          eqlin:  residual: []
                 marginals: []
        ineqlin:  residual: [ 9.111e+07]
                 marginals: [-0.000e+00]
 mip_node_count: 0
 mip_dual_bound: 0.0
        mip_gap: 0.0
2025-05-24 12:30:28,201 - INFO - LP allocations: {'Capdev/Training - gender sensitivity trainings (GST)': 100000.2820387, 'Capdev/Training - gender responsive planning and budgeting': 100000.2820387, 'Capdev/Training - gender analysis': 100000.2820387}
2025-05-24 12:30:28,201 - INFO - Combined results: {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gender responsive planning and budgeting": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gender analysis": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}}
2025-05-24 12:30:28,201 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gender responsive planning and budgeting": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gender analysis": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 1400003.9485418}
2025-05-24 23:35:39,239 - INFO - Script started
2025-05-24 23:35:39,239 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 454554, "ps": 0, "co": 0}}}
2025-05-24 23:35:39,240 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 454554, "ps": 0, "co": 0}}}
2025-05-24 23:35:39,240 - INFO - Activities: ['Capdev/Training - gender sensitivity trainings (GST)']
2025-05-24 23:35:39,240 - INFO - Scores: [5]
2025-05-24 23:35:39,240 - INFO - Total Budget: 91407936.05
2025-05-24 23:35:39,240 - INFO - Manual Allocations: {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 454554, "ps": 0, "co": 0}}
2025-05-24 23:35:39,240 - INFO - Min allocation: 9140.793605
2025-05-24 23:35:39,240 - INFO - Max allocation: 100000.2820387
2025-05-24 23:35:39,241 - INFO - Running linprog optimization...
2025-05-24 23:35:39,253 - INFO - Optimization result:         message: Optimization terminated successfully. (HiGHS Status 7: Optimal)
        success: True
         status: 0
            fun: -500001.4101935
              x: [ 1.000e+05]
            nit: 0
          lower:  residual: [ 9.086e+04]
                 marginals: [ 0.000e+00]
          upper:  residual: [ 0.000e+00]
                 marginals: [-5.000e+00]
          eqlin:  residual: []
                 marginals: []
        ineqlin:  residual: [ 9.131e+07]
                 marginals: [-0.000e+00]
 mip_node_count: 0
 mip_dual_bound: 0.0
        mip_gap: 0.0
2025-05-24 23:35:39,253 - INFO - LP allocations: {'Capdev/Training - gender sensitivity trainings (GST)': 100000.2820387}
2025-05-24 23:35:39,254 - INFO - Combined results: {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 454554.0, "combined_total": 554554.2820387, "breakdown": {"mooe": 454554.0, "ps": 0.0, "co": 0.0}}}
2025-05-24 23:35:39,254 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 454554.0, "combined_total": 554554.2820387, "breakdown": {"mooe": 454554.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 500001.4101935}
2025-05-24 23:41:17,648 - INFO - Script started
2025-05-24 23:41:17,649 - INFO - Received input data: {"activities": ["Capdev/Training - gender analysis", "Capdev/Training - gad related policies"], "scores": [5, 4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender analysis": {"mooe": 234234, "ps": 0, "co": 0}, "Capdev/Training - gad related policies": {"mooe": 234234, "ps": 0, "co": 0}}}
2025-05-24 23:41:17,650 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender analysis", "Capdev/Training - gad related policies"], "scores": [5, 4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender analysis": {"mooe": 234234, "ps": 0, "co": 0}, "Capdev/Training - gad related policies": {"mooe": 234234, "ps": 0, "co": 0}}}
2025-05-24 23:41:17,650 - INFO - Optimizing 2 activities with budget 91407936.05
2025-05-24 23:41:17,672 - INFO - Optimization successful, total allocation: 200000.56
2025-05-24 23:41:17,672 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender analysis": {"optimal_allocation": 100000.2820387, "manual_allocation": 234234.0, "combined_total": 334234.2820387, "breakdown": {"mooe": 234234.0, "ps": 0.0, "co": 0.0}}, "Capdev/Training - gad related policies": {"optimal_allocation": 100000.2820387, "manual_allocation": 234234.0, "combined_total": 334234.2820387, "breakdown": {"mooe": 234234.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 900002.5383482999}
2025-05-25 10:29:37,625 - INFO - Script started
2025-05-25 10:29:37,626 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-25 10:29:37,626 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 50000, "ps": 0, "co": 0}}}
2025-05-25 10:29:37,626 - INFO - Optimizing 1 activities with budget 91407936.05
2025-05-25 10:29:37,652 - INFO - Optimization successful, total allocation: 100000.28
2025-05-25 10:29:37,652 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 50000.0, "combined_total": 150000.2820387, "breakdown": {"mooe": 50000.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 500001.4101935}
2025-05-25 10:35:39,584 - INFO - Script started
2025-05-25 10:35:39,585 - INFO - Received input data: {"activities": ["Capdev/Training - gender analysis"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender analysis": {"mooe": 60000, "ps": 0, "co": 0}}}
2025-05-25 10:35:39,585 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender analysis"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender analysis": {"mooe": 60000, "ps": 0, "co": 0}}}
2025-05-25 10:35:39,585 - INFO - Optimizing 1 activities with budget 91407936.05
2025-05-25 10:35:39,607 - INFO - Optimization successful, total allocation: 100000.28
2025-05-25 10:35:39,608 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender analysis": {"optimal_allocation": 100000.2820387, "manual_allocation": 60000.0, "combined_total": 160000.2820387, "breakdown": {"mooe": 60000.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 500001.4101935}
2025-05-25 10:39:02,657 - INFO - Script started
2025-05-25 10:39:02,657 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 5555, "ps": 0, "co": 0}}}
2025-05-25 10:39:02,658 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 5555, "ps": 0, "co": 0}}}
2025-05-25 10:39:02,658 - INFO - Optimizing 1 activities with budget 91407936.05
2025-05-25 10:39:02,662 - INFO - Optimization successful, total allocation: 100000.28
2025-05-25 10:39:02,663 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 5555.0, "combined_total": 105555.2820387, "breakdown": {"mooe": 5555.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 500001.4101935}
2025-05-25 10:39:39,328 - INFO - Script started
2025-05-25 10:39:39,329 - INFO - Received input data: {"activities": ["Capdev/Training - gender responsive planning and budgeting"], "scores": [4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender responsive planning and budgeting": {"mooe": 4444, "ps": 0, "co": 0}}}
2025-05-25 10:39:39,329 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender responsive planning and budgeting"], "scores": [4], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender responsive planning and budgeting": {"mooe": 4444, "ps": 0, "co": 0}}}
2025-05-25 10:39:39,329 - INFO - Optimizing 1 activities with budget 91407936.05
2025-05-25 10:39:39,334 - INFO - Optimization successful, total allocation: 100000.28
2025-05-25 10:39:39,334 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender responsive planning and budgeting": {"optimal_allocation": 100000.2820387, "manual_allocation": 4444.0, "combined_total": 104444.2820387, "breakdown": {"mooe": 4444.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 400001.1281548}
2025-05-25 10:43:02,345 - INFO - Script started
2025-05-25 10:43:02,346 - INFO - Received input data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 5445, "ps": 0, "co": 0}}}
2025-05-25 10:43:02,346 - INFO - Starting optimization with data: {"activities": ["Capdev/Training - gender sensitivity trainings (GST)"], "scores": [5], "total_budget": 91407936.05, "manual_allocations": {"Capdev/Training - gender sensitivity trainings (GST)": {"mooe": 5445, "ps": 0, "co": 0}}}
2025-05-25 10:43:02,347 - INFO - Optimizing 1 activities with budget 91407936.05
2025-05-25 10:43:02,378 - INFO - Optimization successful, total allocation: 100000.28
2025-05-25 10:43:02,378 - INFO - Output result: {"success": true, "results": {"Capdev/Training - gender sensitivity trainings (GST)": {"optimal_allocation": 100000.2820387, "manual_allocation": 5445.0, "combined_total": 105445.2820387, "breakdown": {"mooe": 5445.0, "ps": 0.0, "co": 0.0}}}, "total_optimal_score": 500001.4101935}
