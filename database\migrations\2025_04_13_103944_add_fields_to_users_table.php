<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddFieldsToUsersTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('users')) {
            $columnsToAdd = [
                'name' => fn($table) => $table->string('name')->nullable(),
                'middle_name' => fn($table) => $table->string('middle_name')->nullable(),
                'last_name' => fn($table) => $table->string('last_name')->nullable(),
                'suffix' => fn($table) => $table->string('suffix')->nullable(),
                'birthdate' => fn($table) => $table->date('birthdate')->nullable(),
                'gender' => fn($table) => $table->string('gender')->nullable(),
                'mobile_number' => fn($table) => $table->string('mobile_number')->nullable(),
                'region' => fn($table) => $table->string('region')->nullable(),
                'province' => fn($table) => $table->string('province')->nullable(),
                'city' => fn($table) => $table->string('city')->nullable(),
                'barangay' => fn($table) => $table->string('barangay')->nullable(),
                'verification_code' => fn($table) => $table->string('verification_code')->nullable(),
            ];

            Schema::table('users', function (Blueprint $table) use ($columnsToAdd) {
                foreach ($columnsToAdd as $column => $callback) {
                    if (!Schema::hasColumn('users', $column)) {
                        $callback($table);
                    }
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('users')) {
            if (config('database.default') === 'sqlite') {
                // SQLite workaround for dropping columns
                Schema::create('users_temp', function (Blueprint $table) {
                    $table->id();
                    $table->string('email')->unique();
                    $table->timestamp('email_verified_at')->nullable();
                    $table->string('password');
                    $table->rememberToken();
                    $table->timestamps();
                    // Add other columns that should remain in the table
                });

                // Copy data from the old table to the new table
                DB::statement('INSERT INTO users_temp SELECT id, email, email_verified_at, password, remember_token, created_at, updated_at FROM users');

                // Drop the old table
                Schema::drop('users');

                // Rename the new table to the original name
                Schema::rename('users_temp', 'users');
            } else {
                // For databases that support dropping columns
                Schema::table('users', function (Blueprint $table) {
                    $table->dropColumn([
                        'name',
                        'middle_name',
                        'last_name',
                        'suffix',
                        'birthdate',
                        'gender',
                        'mobile_number',
                        'region',
                        'province',
                        'city',
                        'barangay',
                        'verification_code',
                    ]);
                });
            }
        }
    }
}