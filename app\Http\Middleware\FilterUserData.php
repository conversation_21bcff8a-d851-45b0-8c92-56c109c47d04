<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class FilterUserData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get the authenticated user
        $user = Auth::user();
        
        // Add user data to all Inertia responses
        if ($user) {
            Inertia::share([
                'auth' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'middle_name' => $user->middle_name,
                        'last_name' => $user->last_name,
                        'suffix' => $user->suffix,
                        'birthdate' => $user->birthdate,
                        'gender' => $user->gender,
                        'mobile_number' => $user->mobile_number,
                        'region' => $user->region,
                        'province' => $user->province,
                        'city' => $user->city,
                        'barangay' => $user->barangay,
                        'barangay_id' => $user->barangay_id,
                        'email' => $user->email,
                        'role' => $user->role,
                        'status' => $user->status,
                        'profile_photo_url' => $user->profile_photo_url ?? null,
                    ],
                ],
                'userFilters' => [
                    'role' => $user->role,
                    'barangay' => $user->barangay,
                    'barangay_id' => $user->barangay_id,
                ],
            ]);
        }
        
        return $next($request);
    }
}