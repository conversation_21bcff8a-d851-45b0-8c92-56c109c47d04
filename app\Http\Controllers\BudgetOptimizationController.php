<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class BudgetOptimizationController extends Controller
{
    public function optimize(Request $request)
    {
        // Get activities and scores from request
        $activities = $request->input('activities', []);
        $scores = $request->input('scores', []);
        $totalBudget = $request->input('total_budget', 0);
        $manualAllocations = $request->input('manual_allocations', []);
        
        // Prepare data for Python script
        $data = [
            'activities' => $activities,
            'scores' => $scores,
            'total_budget' => $totalBudget,
            'manual_allocations' => $manualAllocations
        ];
        
        // Run Python script as subprocess
        $process = new Process([
            'python', 
            base_path('scripts/optimize_budget.py'), 
            json_encode($data)
        ]);
        
        $process->run();
        
        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }
        
        // Get output from Python script
        $result = json_decode($process->getOutput(), true);
        
        return response()->json($result);
    }
}