// Fix for TypeScript: declare window.Echo and window.onNewNotification
declare global {
  interface Window {
    Echo: any;
    onNewNotification?: (notification: any) => void;
  }
}

import { ref, computed, onMounted, onUnmounted } from 'vue';
import axios from 'axios';

export interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  data?: any;
  unread: boolean;
  time: string;
  created_at: string;
  user_name?: string;
  barangay_name?: string;
}

export function useNotifications() {
  const notifications = ref<Notification[]>([]);
  const unreadCount = ref(0);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const pollingInterval = ref<number | null>(null);
  const isPolling = ref(false);

  // Computed properties
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => n.unread)
  );

  const readNotifications = computed(() => 
    notifications.value.filter(n => !n.unread)
  );

  // Fetch notifications
  const fetchNotifications = async (unreadOnly = false) => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await axios.get('/api/notifications', {
        params: { unread_only: unreadOnly }
      });

      if (response.data.success) {
        notifications.value = response.data.notifications;
        unreadCount.value = response.data.unread_count;
      } else {
        error.value = response.data.message || 'Failed to fetch notifications';
      }
    } catch (err: any) {
      console.error('Error fetching notifications:', err);
      error.value = err.response?.data?.message || 'Failed to fetch notifications';
    } finally {
      loading.value = false;
    }
  };

  // Fetch unread count only (for real-time updates)
  const fetchUnreadCount = async () => {
    try {
      const response = await axios.get('/api/notifications/unread-count');
      if (response.data.success) {
        const newUnreadCount = response.data.unread_count;
        
        // If unread count increased, fetch full notifications
        if (newUnreadCount > unreadCount.value) {
          await fetchNotifications();
        } else {
          unreadCount.value = newUnreadCount;
        }
      }
    } catch (err: any) {
      console.error('Error fetching unread count:', err);
    }
  };

  // Start real-time polling
  const startPolling = (intervalMs: number = 30000) => {
    if (isPolling.value) return;
    
    isPolling.value = true;
    pollingInterval.value = window.setInterval(async () => {
      await fetchUnreadCount();
    }, intervalMs);
    
    console.log('Started notification polling every', intervalMs, 'ms');
  };

  // Stop real-time polling
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    isPolling.value = false;
    console.log('Stopped notification polling');
  };

  // Mark notification as read
  const markAsRead = async (notificationId: number) => {
    try {
      const response = await axios.put(`/api/notifications/${notificationId}/read`);
      
      if (response.data.success) {
        // Update local state
        const notification = notifications.value.find(n => n.id === notificationId);
        if (notification) {
          notification.unread = false;
          unreadCount.value = Math.max(0, unreadCount.value - 1);
        }
      }
    } catch (err: any) {
      console.error('Error marking notification as read:', err);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await axios.put('/api/notifications/mark-all-read');
      
      if (response.data.success) {
        // Update local state
        notifications.value.forEach(n => n.unread = false);
        unreadCount.value = 0;
      }
    } catch (err: any) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string): string => {
    switch (type) {
      case 'budget': return '';
      case 'accomplishment': return '';
      case 'system': return '';
      case 'user': return '';
      case 'announcement': return '';
      case 'maintenance': return '';
      case 'update': return '';
      case 'reminder': return '';
      case 'urgent': return '';
      default: return '';
    }
  };

  // Get notification priority color
  const getNotificationPriorityColor = (data: any): string => {
    if (!data || !data.priority) return 'bg-gray-100';
    
    switch (data.priority) {
      case 'urgent': return 'bg-red-100 border-red-300';
      case 'high': return 'bg-orange-100 border-orange-300';
      case 'normal': return 'bg-blue-100 border-blue-300';
      case 'low': return 'bg-green-100 border-green-300';
      default: return 'bg-gray-100 border-gray-300';
    }
  };

  // Get notification type label
  const getNotificationTypeLabel = (type: string, data?: any): string => {
    if (type === 'system' && data?.notice_type) {
      switch (data.notice_type) {
        case 'announcement': return 'Announcement';
        case 'maintenance': return 'Maintenance';
        case 'update': return 'System Update';
        case 'reminder': return 'Reminder';
        case 'urgent': return 'Urgent Notice';
        default: return 'System Notice';
      }
    }
    
    switch (type) {
      case 'budget': return 'Budget Plan';
      case 'accomplishment': return 'Accomplishment Report';
      case 'system': return 'System Notice';
      case 'user': return 'User Activity';
      default: return 'Notification';
    }
  };

  // Filter notifications by type
  const filterByType = (type: string) => {
    return notifications.value.filter(n => n.type === type);
  };

  // Filter notifications by priority
  const filterByPriority = (priority: string) => {
    return notifications.value.filter(n => n.data?.priority === priority);
  };

  // Get urgent notifications
  const urgentNotifications = computed(() => 
    notifications.value.filter(n => n.data?.priority === 'urgent' && n.unread)
  );

  // Format time for display
  const formatTime = (time: string): string => {
    return time; // The backend already returns diffForHumans format
  };

  // Listen for real-time notifications
  const listenForRealtime = (userId: number) => {
    if (window.Echo) {
      console.log('Echo is available:', window.Echo);
      // Debug: Listen to all events on the notifications channel
      window.Echo.channel('notifications')
        .listen('NotificationPushed', (e: any) => {
          console.log('NotificationPushed event received:', e);
          let data = e;
          if (typeof e === 'string') {
            try {
              data = JSON.parse(e);
            } catch (err) {
              console.error('Failed to parse event data:', e);
              return;
            }
          }
          console.log('Public channel notification received:', data);
          notifications.value.unshift({
            id: data.id,
            type: data.type,
            title: data.title,
            message: data.message,
            data: data.data,
            unread: true,
            time: 'just now',
            created_at: data.created_at,
            user_name: data.user_name,
            barangay_name: data.barangay_name,
          });
          unreadCount.value++;
        })
        .listen('.NotificationPushed', (e: any) => {
          console.log('.NotificationPushed event received:', e);
          notifications.value.unshift({
            id: e.id,
            type: e.type,
            title: e.title,
            message: e.message,
            data: e.data,
            unread: true,
            time: 'just now',
            created_at: e.created_at,
            user_name: e.user_name,
            barangay_name: e.barangay_name,
          });
          unreadCount.value++;
        })
        .listen('pusher:subscription_succeeded', () => {
          console.log('Subscribed to notifications channel');
        })
        .listen('pusher:subscription_error', (err: any) => {
          console.error('Subscription error:', err);
        });
      // Private channel for production
      console.log('Subscribing to channel:', `App.Models.User.${userId}`); // Debug log for channel subscription
      window.Echo.private(`App.Models.User.${userId}`)
        .listen('NotificationPushed', (e: any) => {
          console.log('Real-time notification received (NotificationPushed):', e); // Debug log for event reception
          notifications.value.unshift({
            id: e.id,
            type: e.type,
            title: e.title,
            message: e.message,
            data: e.data,
            unread: true,
            time: 'just now',
            created_at: e.created_at,
            user_name: e.user_name,
            barangay_name: e.barangay_name,
          });
          unreadCount.value++;
        })
        .listen('.NotificationPushed', (e: any) => {
          console.log('Real-time notification received (.NotificationPushed):', e); // Debug log for dot-prefixed event
          notifications.value.unshift({
            id: e.id,
            type: e.type,
            title: e.title,
            message: e.message,
            data: e.data,
            unread: true,
            time: 'just now',
            created_at: e.created_at,
            user_name: e.user_name,
            barangay_name: e.barangay_name,
          });
          unreadCount.value++;
        });
    } else {
      console.error('Echo is not available on window!');
    }
  };

  // Initialize notifications with real-time updates
  const init = async (userId: number, enablePolling: boolean = false) => {
    // Ensure CSRF cookie is set before any Echo or API requests
    await axios.get('/sanctum/csrf-cookie', { withCredentials: true });
    await fetchNotifications();
    listenForRealtime(userId);
    if (enablePolling) {
      startPolling();
    }
  };

  // Cleanup on unmount
  const cleanup = () => {
    stopPolling();
  };

  return {
    // State
    notifications,
    unreadCount,
    loading,
    error,
    isPolling,
    
    // Computed
    unreadNotifications,
    readNotifications,
    urgentNotifications,
    
    // Methods
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    getNotificationIcon,
    getNotificationPriorityColor,
    getNotificationTypeLabel,
    filterByType,
    filterByPriority,
    formatTime,
    init,
    startPolling,
    stopPolling,
    cleanup,
    listenForRealtime
  };
} 