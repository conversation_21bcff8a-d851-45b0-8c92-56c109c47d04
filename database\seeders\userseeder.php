<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('adminpassword'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );
        
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Second Admin',
                'password' => Hash::make('anotherpassword'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );
        
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ErvinAdmin User',
                'password' => Hash::make('<EMAIL>'),
                'role' => 'barangay',
                'email_verified_at' => now(),
            ]
        );
        
    }
}


