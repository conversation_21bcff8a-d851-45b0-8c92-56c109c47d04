<script setup lang="ts">
import { Head, usePage } from '@inertiajs/vue3';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { settingsActiveTab } from '@/settingsTabState';
import { Settings as SettingsIcon, User, Lock, Palette } from 'lucide-vue-next';
import SettingsGeneral from '@/pages/settings/Settings.vue';
import SettingsProfile from '@/pages/settings/Profile.vue';
import SettingsPassword from '@/pages/settings/Password.vue';
import SettingsAppearance from '@/pages/settings/Appearance.vue';
import { type SharedData } from '@/types';

const page = usePage<SharedData>();
const user = page.props.auth.user;

// Set active tab to general by default
if (!settingsActiveTab.value) {
    settingsActiveTab.value = 'general';
}

const sidebarNavItems = [
    {
        title: 'General',
        icon: SettingsIcon,
        spaTab: 'general',
    },
    {
        title: 'Profile',
        icon: User,
        spaTab: 'profile',
    },
    {
        title: 'Password',
        icon: Lock,
        spaTab: 'password',
    },
    {
        title: 'Appearance',
        icon: Palette,
        spaTab: 'appearance',
    },
];

const handleTabClick = (spaTab: string) => {
    settingsActiveTab.value = spaTab as any;
};

const getActiveComponent = () => {
    switch (settingsActiveTab.value) {
        case 'general':
            return SettingsGeneral;
        case 'profile':
            return SettingsProfile;
        case 'password':
            return SettingsPassword;
        case 'appearance':
            return SettingsAppearance;
        default:
            return SettingsGeneral;
    }
};
</script>

<template>
    <Head title="Settings" />
    <div class="flex-1 overflow-y-auto">
        <!-- Settings Header -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="py-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
                            <p class="text-gray-600 dark:text-gray-300">Manage your account settings and preferences</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Settings Sidebar -->
                <aside class="lg:w-64 flex-shrink-0">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                        <nav class="space-y-1">
                            <Button
                                v-for="item in sidebarNavItems"
                                :key="item.spaTab"
                                variant="ghost"
                                :class="[
                                    'w-full justify-start h-12 px-4 text-left',
                                    { 
                                        'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800': 
                                        settingsActiveTab === item.spaTab
                                    }
                                ]"
                                @click="handleTabClick(item.spaTab)"
                            >
                                <component :is="item.icon" class="h-5 w-5 mr-3" />
                                <span class="font-medium">{{ item.title }}</span>
                            </Button>
                        </nav>
                    </div>
                </aside>

                <!-- Settings Content -->
                <div class="flex-1 min-w-0">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                        <component 
                            :is="getActiveComponent()" 
                            v-if="settingsActiveTab === 'profile'"
                            :must-verify-email="!user.email_verified_at"
                            :status="page.props.status"
                        />
                        <component 
                            :is="getActiveComponent()" 
                            v-else
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template> 