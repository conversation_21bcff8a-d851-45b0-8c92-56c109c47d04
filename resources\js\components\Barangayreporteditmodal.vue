<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-2">
    <div class="bg-gradient-to-br from-emerald-700 to-emerald-900 rounded-xl shadow-xl p-2 sm:p-3 md:p-4 w-full max-w-7xl mx-auto flex flex-col overflow-y-auto min-h-[300px] max-h-[90vh] invisible-scrollbar">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-white">Edit Accomplishment Report</h2>
        <button class="text-white/80 hover:text-white text-xl" @click="$emit('close')">✕</button>
      </div>

      <form @submit.prevent="updateReport" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-3 gap-y-3 w-full max-w-full">
          <!-- Left Column -->
          <div class="flex flex-col gap-3 min-w-0 flex-1">
            <select v-model="form.focused" 
                    class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                    required>
              <option disabled value="">Focused (required)</option>
              <option>CLIENT-FOCUSED</option>
              <option>ORGANIZATION-FOCUSED</option>
            </select>

            <select v-model="form.gadMandate" 
                    class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                    required>
              <option disabled value="">Gender Issue or GAD Mandate</option>
              <option>Gender Issue</option>
              <option>GAD Mandate</option>
            </select>

            <textarea v-model="form.title" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="Title/Description of Gender Issue or GAD Mandate (required)"></textarea>

            <textarea v-model="form.supportingData" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                      placeholder="Supporting Statistics Data (optional)"></textarea>

            <textarea v-model="form.dataSource" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                      placeholder="Source of Supporting Statistics Data (optional)"></textarea>

            <input v-model="form.ppa" 
                   type="text" 
                   class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                   placeholder="PPA/s"/>
          </div>

          <!-- Middle Column -->
          <div class="flex flex-col gap-3 min-w-0 flex-1">
            <textarea v-model="form.gadObjective" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="GAD Objective (required)"></textarea>

            <textarea v-model="form.lguPpa" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="Relevant LGU PPA"></textarea>

            <div>
              <label class="block text-xs text-emerald-100 mb-1">Activity</label>
              <div class="text-xs text-emerald-200/70 italic mb-1">Specify here if the activity category is not listed on the selection</div>
              <textarea v-model="form.otherActivity" 
                        class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                        placeholder="Other Activity Category Value"></textarea>
            </div>

            <textarea v-model="form.gadActivity" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="GAD Activity"></textarea>

            <textarea v-model="form.performanceIndicator" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="Performance Indicator and Target"></textarea>
          </div>

          <!-- Right Column -->
          <div class="flex flex-col gap-3 min-w-0 flex-1">
            <textarea v-model="form.actualResults" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                      required
                      placeholder="Actual Results"></textarea>

            <input v-model="form.approvedBudget" 
                   type="text" 
                   inputmode="numeric"
                   class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all" 
                   placeholder="Approved GAD Budget" />

            <input v-model="form.actualCost" 
                   type="text" 
                   inputmode="numeric"
                   class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                   placeholder="Actual Cost or Expenditure" />

            <textarea v-model="form.remarks" 
                      class="w-full rounded-lg border-0 bg-white p-3 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                      placeholder="Variance / Remarks"></textarea>
          </div>
        </div>

        <!-- Footer Buttons -->
        <div class="flex flex-col sm:flex-row justify-end gap-2 mt-6 shrink-0">
          <button type="button" class="px-3 py-1.5 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors text-sm" @click="$emit('close')" :disabled="loading">Cancel</button>
          <button 
            type="submit" 
            class="px-3 py-1.5 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium transition-colors text-sm"
            :disabled="loading"
          >
            <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
            <span v-else>Update Report</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  show: Boolean,
  report: Object
});

const emit = defineEmits(['close', 'update']);
const loading = ref(false);

const form = reactive({
  id: '',
  title: '',
  genderIssue: '',
  gadMandate: '',
  focused: '',
  gadObjective: '',
  lguPpa: '',
  gadActivity: '',
  performanceIndicator: '',
  actualResults: '',
  approvedBudget: 0,
  actualCost: 0,
  remarks: '',
  status: '',
  supportingData: '',
  dataSource: '',
  ppa: '',
  otherActivity: ''
});

// Watch for changes in the report prop
onMounted(() => {
  if (props.report) {
    // Copy report data to form
    Object.keys(form).forEach(key => {
      if (props.report[key] !== undefined) {
        form[key] = props.report[key];
      }
    });
  }
});

async function updateReport() {
  loading.value = true;
  try {
    // Convert budget values to numbers
    if (form.approvedBudget) form.approvedBudget = Number(form.approvedBudget);
    if (form.actualCost) form.actualCost = Number(form.actualCost);
    
    const response = await axios.put(`/api/accomplishment-reports/${form.id}`, form);
    
    if (response.data.success) {
      emit('update', response.data.report);
      emit('close');
    } else {
      console.error('Error updating report:', response.data.message);
      alert('Failed to update report: ' + response.data.message);
    }
  } catch (error) {
    console.error('Error updating report:', error);
    if (error.response && error.response.data) {
      alert('Error: ' + (error.response.data.message || 'Unknown server error'));
    } else {
      alert('An error occurred while updating the report');
    }
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.invisible-scrollbar::-webkit-scrollbar {
  display: none;
}
.invisible-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
