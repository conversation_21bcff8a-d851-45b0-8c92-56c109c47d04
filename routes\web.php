<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Models\BudgetPlan;
use App\Models\AccomplishmentReport;
use App\Http\Controllers\BudgetPlanController;
use App\Http\Controllers\AccomplishmentReportController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\SystemNoticeController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\CommentController;

Route::get('/', function () {
    return Inertia::render('auth/Login');
})->name('login');

Route::get('dashboard', function () {
    $user = auth()->user();
    if ($user && $user->role === 'barangay') {
        $budgetPlans = BudgetPlan::where('barangay_id', $user->barangay_id)->get();
        return Inertia::render('BudgetPlan', [
            'budgetPlans' => $budgetPlans,
        ]);
    } elseif ($user && $user->role === 'punong_barangay') {
        return Inertia::render('PunongBarangay');
    }
    // Default to admin dashboard
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified', 'check.user.status'])->name('dashboard');

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/barangay-dashboard', function () {
    $user = auth()->user();
    if ($user && $user->role === 'barangay') {
        $budgetPlans = BudgetPlan::where('barangay_id', $user->barangay_id)->get();
        return Inertia::render('BudgetPlan', [
            'budgetPlans' => $budgetPlans,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }
    abort(403);
})->name('barangay.dashboard');

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/api/barangay-users', [App\Http\Controllers\UserController::class, 'getBarangayUsers']);

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/barangay-budget-plan', function () {
    $user = auth()->user();
    if ($user->role === 'admin') {
        $budgetPlans = BudgetPlan::all();
    } else {
        $budgetPlans = BudgetPlan::where('barangay_id', $user->barangay_id)->get();
    }
    return Inertia::render('BudgetPlan', [
        'budgetPlans' => $budgetPlans,
        'auth' => [
            'user' => $user,
        ],
    ]);
})->name('barangay.budgetplan');

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/barangay-accomplishment-report', function () {
    $user = auth()->user();
    if ($user->role === 'admin') {
        $reports = AccomplishmentReport::all();
    } else {
        $reports = AccomplishmentReport::where('barangay_id', $user->barangay_id)->get();
    }
    return Inertia::render('AccomplishmentReport', [
        'reports' => $reports,
    ]);
})->name('barangay.accomplishment');

// Models already imported above; do not import again.

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/admin/budget-plan', function () {
    $budgetPlans = BudgetPlan::all();
    return Inertia::render('AdminBudgetPlan', [
        'budgetPlans' => $budgetPlans,
    ]);
})->name('admin.budgetplan');

Route::middleware(['auth', 'verified', 'check.user.status'])->get('/admin/accomplishment-report', function () {
    $reports = AccomplishmentReport::all();
    return Inertia::render('AdminAccomplishmentReport', [
        'reports' => $reports,
    ]);
})->name('admin.accomplishment');

// Budget Plan API Routes
Route::prefix('api')->middleware(['auth', 'verified', 'check.user.status'])->group(function () {
    Route::get('/budget-plans', [BudgetPlanController::class, 'index']);
    Route::post('/budget-plans', [BudgetPlanController::class, 'store']);
    Route::post('/budget-plans/draft', [BudgetPlanController::class, 'storeDraft']);
    Route::get('/budget-plans/{id}', [BudgetPlanController::class, 'show']);
    Route::put('/budget-plans/{id}', [BudgetPlanController::class, 'update']);
    Route::delete('/budget-plans/{id}', [BudgetPlanController::class, 'destroy']);

    // Budget Plan Deadline API
    Route::get('/budget-plan-deadline', [SettingController::class, 'getDeadline']);
    Route::post('/budget-plan-deadline', [SettingController::class, 'setDeadline']);
});

// Accomplishment Report API Routes
Route::prefix('api')->middleware(['auth', 'verified', 'check.user.status'])->group(function () {
    Route::post('/accomplishment-reports', [AccomplishmentReportController::class, 'store']);
    Route::post('/accomplishment-reports/draft', [AccomplishmentReportController::class, 'storeDraft']);
    Route::get('/accomplishment-reports', [AccomplishmentReportController::class, 'index']);
    Route::get('/accomplishment-reports/{id}', [AccomplishmentReportController::class, 'show']);
    Route::put('/accomplishment-reports/{id}', [AccomplishmentReportController::class, 'update']);
    Route::delete('/accomplishment-reports/{id}', [AccomplishmentReportController::class, 'destroy']);
});

// Notification API Routes
Route::prefix('api')->middleware(['auth', 'verified', 'check.user.status'])->group(function () {
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::get('/notifications/unread-count', [NotificationController::class, 'unreadCount']);
    Route::put('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::put('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::post('/notifications/generate-from-existing', [NotificationController::class, 'generateFromExistingData']);
    
    // System Notice Routes
    Route::get('/system-notices', [SystemNoticeController::class, 'getSystemNotices']);
    Route::post('/system-notices', [SystemNoticeController::class, 'createNotice']);
    Route::post('/system-notices/barangay', [SystemNoticeController::class, 'createBarangayNotice']);
    Route::delete('/system-notices/{id}', [SystemNoticeController::class, 'deleteNotice']);
});

// Comments API
Route::get('/api/comments/{plan_id}', [CommentController::class, 'index']);
Route::post('/api/comments', [CommentController::class, 'storeOrUpdate']);

// Add this route for Punong Barangay dashboard
Route::middleware(['auth', 'verified', 'check.user.status'])->get('/punong-barangay', function () {
    return Inertia::render('PunongBarangay');
})->name('punong.barangay');

Route::middleware(['auth', 'verified', 'check.user.status'])->post('/admin/users', [App\Http\Controllers\UserController::class, 'createUser']);
Route::middleware(['auth', 'verified', 'check.user.status'])->put('/admin/users/{id}', [App\Http\Controllers\UserController::class, 'updateUser']);
Route::middleware(['auth', 'verified', 'check.user.status'])->patch('/admin/users/{id}/deactivate', [App\Http\Controllers\UserController::class, 'deactivateUser']);
Route::middleware(['auth', 'verified', 'check.user.status'])->patch('/admin/users/{id}/reactivate', [App\Http\Controllers\UserController::class, 'reactivateUser']);

Route::get('/accomplishment-report-deadline', [\App\Http\Controllers\SettingController::class, 'getAccomplishmentDeadline']);
Route::post('/accomplishment-report-deadline', [\App\Http\Controllers\SettingController::class, 'setAccomplishmentDeadline']);

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/locations.php';
require __DIR__.'/test.php';







