
<template>
    <Head title="Register" />

    <!-- Main Content -->
    <div class="min-h-screen bg-emerald-100 relative overflow-hidden">
        <!-- Success Notification -->
        <div v-if="flash.success" class="fixed top-4 right-4 left-4 md:left-auto z-50 transition-all duration-500 transform translate-y-0 opacity-100">
            <div class="bg-emerald-100 border-l-4 border-emerald-500 text-emerald-700 p-4 rounded-xl shadow-lg" role="alert">
                <div class="flex items-center">
                    <svg class="h-6 w-6 text-emerald-500 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                        <p class="font-bold">Registration Successful!</p>
                        <p class="text-sm">{{ flash.success }}</p>
                    </div>
                    <button @click="router.reload()" class="ml-auto text-emerald-500 hover:text-emerald-700">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Error Notification -->
        <div v-if="flash.error" class="fixed top-4 right-4 left-4 md:left-auto z-50 transition-all duration-500 transform translate-y-0 opacity-100">
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-xl shadow-lg" role="alert">
                <div class="flex items-center">
                    <svg class="h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <div>
                        <p class="font-bold">Registration Error</p>
                        <p class="text-sm">{{ flash.error }}</p>
                    </div>
                    <button @click="router.reload()" class="ml-auto text-red-500 hover:text-red-700">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="flex items-center justify-center min-h-screen px-4 py-6 relative z-10">
            <div class="bg-white rounded-xl shadow-2xl overflow-hidden max-w-5xl w-full relative z-20">
                <!-- Header Section with Logo -->
                <div class="bg-gradient-to-br from-emerald-400 to-emerald-600 p-3 text-center text-white">
                    <h1 class="text-lg font-bold mb-1 whitespace-nowrap">Create Your Account</h1>
                    <p class="text-emerald-100 text-xs whitespace-nowrap">Join the GAD Planning and Budgeting Management System</p>
                </div>

                <!-- Form Section -->
                <div class="p-6 lg:p-8">

                <form @submit.prevent="submit" class="space-y-3 sm:space-y-4 overflow-y-auto flex-1 min-h-0">
                    <!-- Personal Information Section -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 w-full">
                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="name" class="block text-xs font-medium text-gray-700 mb-1">First Name (required)</label>
                                <input
                                    id="name"
                                    type="text"
                                    required
                                    v-model="form.name"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter first name"
                                />
                                <InputError :message="form.errors.name" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="middle_name" class="block text-xs font-medium text-gray-700 mb-1">Middle Name</label>
                                <input
                                    id="middle_name"
                                    type="text"
                                    v-model="form.middle_name"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter middle name"
                                />
                                <InputError :message="form.errors.middle_name" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="last_name" class="block text-xs font-medium text-gray-700 mb-1">Last Name (required)</label>
                                <input
                                    id="last_name"
                                    type="text"
                                    required
                                    v-model="form.last_name"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter last name"
                                />
                                <InputError :message="form.errors.last_name" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="suffix" class="block text-xs font-medium text-gray-700 mb-1">Suffix</label>
                                <input
                                    id="suffix"
                                    type="text"
                                    v-model="form.suffix"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Jr, Sr, III"
                                />
                                <InputError :message="form.errors.suffix" class="mt-1" />
                            </div>
                        </div>
                    </div>

                    <!-- Location Information Section -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 w-full">
                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="region" class="block text-xs font-medium text-gray-700 mb-1">Region (required)</label>
                                <select
                                    id="region"
                                    v-model="form.region"
                                    required
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                >
                                    <option value="">Select Region</option>
                                    <option v-for="region in regions" :key="region.value" :value="region.value">
                                        {{ region.label }}
                                    </option>
                                </select>
                                <InputError :message="form.errors.region" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="province" class="block text-xs font-medium text-gray-700 mb-1">Province (required)</label>
                                <select
                                    id="province"
                                    v-model="form.province"
                                    required
                                    :disabled="!form.region"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                                >
                                    <option value="">Select Province</option>
                                    <option v-for="province in provinces" :key="province.value" :value="province.value">
                                        {{ province.label }}
                                    </option>
                                </select>
                                <InputError :message="form.errors.province" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="city" class="block text-xs font-medium text-gray-700 mb-1">City (required)</label>
                                <select
                                    id="city"
                                    v-model="form.city"
                                    required
                                    :disabled="!form.province"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                                >
                                    <option value="">Select City</option>
                                    <option v-for="city in cities" :key="city.value" :value="city.value">
                                        {{ city.label }}
                                    </option>
                                </select>
                                <InputError :message="form.errors.city" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="barangay" class="block text-xs font-medium text-gray-700 mb-1">Barangay (required)</label>
                                <select
                                    id="barangay"
                                    v-model="form.barangay"
                                    required
                                    :disabled="!form.city"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                                >
                                    <option value="">Select Barangay</option>
                                    <option v-for="barangay in barangays" :key="barangay.value" :value="barangay.value">
                                        {{ barangay.label }}
                                    </option>
                                </select>
                                <InputError :message="form.errors.barangay" class="mt-1" />
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 w-full">
                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="birthdate" class="block text-xs font-medium text-gray-700 mb-1">Birthdate (required)</label>
                                <input
                                    id="birthdate"
                                    type="date"
                                    required
                                    v-model="form.birthdate"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                />
                                <InputError :message="form.errors.birthdate" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="gender" class="block text-xs font-medium text-gray-700 mb-1">Gender (required)</label>
                                <select
                                    id="gender"
                                    v-model="form.gender"
                                    required
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                >
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                                <InputError :message="form.errors.gender" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="mobile_number" class="block text-xs font-medium text-gray-700 mb-1">Mobile Number (required)</label>
                                <input
                                    id="mobile_number"
                                    type="tel"
                                    required
                                    v-model="form.mobile_number"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter your mobile number"
                                />
                                <InputError :message="form.errors.mobile_number" class="mt-1" />
                            </div>
                        </div>
                    </div>


                    <!-- Account Credentials Section -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 w-full">
                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="email" class="block text-xs font-medium text-gray-700 mb-1">Email Address (required)</label>
                                <input
                                    id="email"
                                    type="email"
                                    required
                                    v-model="form.email"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter your email address"
                                />
                                <InputError :message="form.errors.email" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="verification_code" class="block text-xs font-medium text-gray-700 mb-1">Verification Code</label>
                                <input
                                    id="verification_code"
                                    type="text"
                                    v-model="form.verification_code"
                                    class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                    placeholder="Enter verification code"
                                />
                                <InputError :message="form.errors.verification_code" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="password" class="block text-xs font-medium text-gray-700 mb-1">Password (required)</label>
                                <div class="relative">
                                    <input
                                        id="password"
                                        :type="showPassword ? 'text' : 'password'"
                                        required
                                        v-model="form.password"
                                        class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 pr-10 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                        placeholder="Enter your password"
                                    />
                                    <button
                                        type="button"
                                        @click="togglePasswordVisibility"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                                    >
                                        <svg v-if="showPassword" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg v-else class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <InputError :message="form.errors.password" class="mt-1" />
                            </div>
                        </div>

                        <div class="flex flex-col gap-2 sm:gap-3 min-w-0 flex-1">
                            <div>
                                <label for="password_confirmation" class="block text-xs font-medium text-gray-700 mb-1">Confirm Password (required)</label>
                                <div class="relative">
                                    <input
                                        id="password_confirmation"
                                        :type="showPasswordConfirmation ? 'text' : 'password'"
                                        required
                                        v-model="form.password_confirmation"
                                        class="w-full rounded-lg border-0 bg-white p-2 sm:p-2.5 pr-10 text-sm shadow-sm ring-1 ring-inset ring-emerald-100 focus:ring-2 focus:ring-emerald-500 transition-all"
                                        placeholder="Confirm your password"
                                    />
                                    <button
                                        type="button"
                                        @click="togglePasswordConfirmationVisibility"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                                    >
                                        <svg v-if="showPasswordConfirmation" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg v-else class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <InputError :message="form.errors.password_confirmation" class="mt-1" />
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex flex-col items-center gap-4">
                        <button
                            type="submit"
                            class="bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-2 px-6 rounded-lg transition duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 flex items-center justify-center text-sm"
                            :disabled="form.processing"
                        >
                            <svg
                                v-if="form.processing"
                                class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                            {{ form.processing ? 'Creating Account...' : 'Create Account' }}
                        </button>

                        <div class="text-center text-xs text-gray-600">
                            Already have an account?
                            <a
                                :href="route('login')"
                                class="text-emerald-600 hover:text-emerald-700 font-medium ml-1 transition duration-200"
                            >
                                Log in here
                            </a>
                        </div>
                    </div>
                </form>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="absolute bottom-0 w-full py-2 text-center text-xs text-emerald-700">
            <div class="max-w-7xl mx-auto px-4">
                © {{ new Date().getFullYear() }} Panabo City Government - GAD Planning and Budgeting Management System <br> All Rights Reserved
            </div>
        </footer>

        <!-- Custom Alert/Confirm Modal -->
        <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 mr-3">
                        <!-- Error Icon -->
                        <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <!-- Success Icon -->
                        <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <!-- Info Icon -->
                        <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
                </div>
                <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
                <div class="flex justify-end gap-3">
                    <button
                        v-if="modalType === 'confirm'"
                        @click="closeModal"
                        class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        @click="modalType === 'confirm' ? confirmAction() : closeModal()"
                        class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
                    >
                        {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import InputError from '@/components/InputError.vue';
import { computed, ref, watch, onMounted } from 'vue';
import { usePage } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

interface LocationOption {
    value: string;
    label: string;
}

interface FlashMessage {
    success?: string;
    error?: string;
}

interface PageProps {
    [key: string]: any;
    flash?: FlashMessage;
    errors?: Record<string, string>;
}

const page = usePage<PageProps>();
const flash = computed(() => {
    const flashValue = page.props.flash || {};
    console.log('Flash object:', flashValue);
    return flashValue;
});

// Password visibility states
const showPassword = ref(false);
const showPasswordConfirmation = ref(false);

// Modal state variables
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref('info'); // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref<(() => void) | null>(null);

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const togglePasswordConfirmationVisibility = () => {
    showPasswordConfirmation.value = !showPasswordConfirmation.value;
};

// Modal functions
function showModal(title: string, message: string, type: string = 'info') {
    modalTitle.value = title;
    modalMessage.value = message;
    modalType.value = type;
    modalConfirmCallback.value = null;
    modalVisible.value = true;
}

function showConfirmModal(title: string, message: string, onConfirm: () => void) {
    modalTitle.value = title;
    modalMessage.value = message;
    modalType.value = 'confirm';
    modalConfirmCallback.value = onConfirm;
    modalVisible.value = true;
}

function closeModal() {
    modalVisible.value = false;
    modalConfirmCallback.value = null;
}

function confirmAction() {
    if (modalConfirmCallback.value) {
        modalConfirmCallback.value();
    }
    closeModal();
}

const form = useForm({
    name: '',
    middle_name: '',
    last_name: '',
    suffix: '',
    birthdate: '',
    gender: '',
    mobile_number: '',
    region: '',
    province: '',
    city: '',
    barangay: '',
    email: '',
    password: '',
    password_confirmation: '',
    verification_code: '',
});

const submit = () => {
    form.post('/register', {
        onSuccess: () => {
            console.log('Registration successful');
            form.reset('password', 'password_confirmation');
            window.scrollTo({ top: 0, behavior: 'smooth' });
            // Show success modal instead of alert
            showModal(
                'Registration Successful!',
                'Your account has been created successfully. You can now log in to your account.',
                'success'
            );
        },
        onError: (errors) => {
            console.log('Registration errors:', errors);
            window.scrollTo({ top: 0, behavior: 'smooth' });
            // Show error modal for registration errors
            const errorMessage = Object.values(errors).flat().join(' ') || 'Registration failed. Please check your information and try again.';
            showModal(
                'Registration Error',
                errorMessage,
                'error'
            );
        }
    });
};

// You would typically fetch these from an API
// Location data states
const regions = ref<LocationOption[]>([]);
const provinces = ref<LocationOption[]>([]);
const cities = ref<LocationOption[]>([]);
const barangays = ref<LocationOption[]>([]);

// Fetch regions on component mount
onMounted(async () => {
    try {
        const response = await fetch('/locations/regions');
        regions.value = await response.json();
    } catch (error) {
        console.error('Error fetching regions:', error);
    }
});

// Watch for region changes to fetch provinces
watch(() => form.region, async (newRegion: string) => {
    if (newRegion) {
        try {
            const response = await fetch(`/locations/provinces/${newRegion}`);
            provinces.value = await response.json();
            // Reset dependent fields
            form.province = '';
            form.city = '';
            form.barangay = '';
            cities.value = [];
            barangays.value = [];
        } catch (error) {
            console.error('Error fetching provinces:', error);
        }
    }
});

// Watch for province changes to fetch cities
watch(() => form.province, async (newProvince: string) => {
    if (newProvince && form.region) {
        try {
            const response = await fetch(`/locations/cities/${form.region}/${newProvince}`);
            cities.value = await response.json();
            // Reset dependent fields
            form.city = '';
            form.barangay = '';
            barangays.value = [];
        } catch (error) {
            console.error('Error fetching cities:', error);
        }
    }
});

// Watch for city changes to fetch barangays
watch(() => form.city, async (newCity: string) => {
    if (newCity && form.region && form.province) {
        try {
            const response = await fetch(`/locations/barangays/${form.region}/${form.province}/${newCity}`);
            barangays.value = await response.json();
            // Reset dependent field
            form.barangay = '';
        } catch (error) {
            console.error('Error fetching barangays:', error);
        }
    }
});


</script>

<style scoped>
/* Custom styles for enhanced appearance */
.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Button disabled state */
button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Smooth transitions */
.transition {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* Focus ring for accessibility */
.focus\:ring-2:focus {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5);
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>



