{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/leaflet.heat": "^0.2.4", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "laravel-echo": "^2.1.6", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "tw-animate-css": "^1.2.5", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@inertiajs/vue3": "^2.0.0-beta.3", "@tailwindcss/vite": "^4.1.1", "@types/leaflet": "^1.9.20", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.8.2", "chart.js": "^3.9.1", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "laravel-vite-plugin": "^1.0", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "lucide": "^0.468.0", "lucide-vue-next": "^0.468.0", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.3", "reka-ui": "^2.2.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^4.1.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "vue-chart-3": "^3.1.8", "vue-datepicker-next": "^1.0.3", "vue3-datepicker": "^0.4.0", "xlsx": "^0.18.5", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}