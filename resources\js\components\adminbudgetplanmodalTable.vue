<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-1 sm:p-4">
    <div class="bg-white shadow-lg w-full max-w-7xl mx-auto overflow-hidden border border-gray-400 rounded-xl max-h-[98vh] sm:max-h-[95vh] flex flex-col">

       <!-- Title -->
         <div class="text-center py-2 sm:py-4 relative px-2 sm:px-6 flex-shrink-0">
  <h1 class="text-xs sm:text-lg lg:text-xl font-bold text-gray-700 pr-12 sm:pr-20 leading-tight">BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) PLAN FY   {{ plan.fiscal_year || new Date().getFullYear() }}</h1>
  <button @click="$emit('close')" class="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 px-2 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-md shadow-sm hover:shadow-md hover:from-red-600 hover:to-red-700 transition-all duration-200 text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-1.5">
    <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
    <span class="hidden sm:inline">Close</span>
  </button>
</div>

      <!-- Only show content if plan exists -->

        <!-- Status Badges -->
        <div class="flex-shrink-0 px-2 sm:px-4 pt-2 sm:pt-3 pb-2">
          <!-- Mobile Layout -->
          <div class="block sm:hidden space-y-2">
            <div class="flex flex-wrap gap-1 text-xs">
              <div class="flex items-center gap-1"><div class="w-3 h-3 bg-red-200"></div><span>Needs Revision</span></div>
              <div class="flex items-center gap-1"><div class="w-3 h-3 bg-yellow-200"></div><span>Revised by GAD Focal</span></div>
              <div class="flex items-center gap-1"><div class="w-3 h-3 bg-green-100"></div><span>Approved</span></div>
            </div>
            <div class="flex flex-wrap gap-1">
              <button @click="exportToExcel" class="bg-white border border-gray-400 px-2 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">📈 Excel</button>
              <button @click="toggleCommentMode" class="bg-white border border-gray-400 px-2 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">💬 Comments</button>
              <button @click="printpreview" class="bg-white border border-gray-400 px-2 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">🖨️ Print</button>
            </div>
            <div v-if="showOtherDetails" class="flex justify-center">
              <button @click="toggleDetailsView" class="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800 cursor-pointer">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span>Back</span>
              </button>
            </div>
          </div>

          <!-- Desktop Layout -->
          <div class="hidden sm:flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="flex items-center gap-2"><div class="w-4 h-4 bg-red-200"></div><span class="text-xs">Needs Revision</span></div>
              <div class="flex items-center gap-2"><div class="w-4 h-4 bg-yellow-200"></div><span class="text-xs">Revised by GAD Focal</span></div>
              <div class="flex items-center gap-2"><div class="w-4 h-4 bg-green-100"></div><span class="text-xs">The revision has been approved</span></div>
              <button @click="exportToExcel" class="bg-white border border-gray-400 px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">📈 Export to Excel</button>
              <button
                @click="toggleCommentMode"
                :class="['bg-white border border-gray-400 px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer', commentMode ? 'bg-emerald-100 border-emerald-500 text-emerald-700' : '', !commentsEditable ? 'opacity-50 cursor-not-allowed' : '']"
              >
                💬 Comments
                <span v-if="commentMode" class="ml-1 text-emerald-700 font-bold">Active</span>
                <span v-else class="ml-1 text-gray-400 font-bold">OFF</span>
              </button>
              <button @click="printpreview" class="bg-white border border-gray-400 px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer">🖨️ Print Preview</button>
            </div>
            <div class="flex items-center space-x-2">
              <button
                v-if="showOtherDetails"
                @click="toggleDetailsView"
                class="flex items-center space-x-1.5 text-sm text-gray-600 hover:text-gray-800 text-left cursor-pointer"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span>Back</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 overflow-auto">
          <!-- Mobile Card View - Main Plan -->
          <div v-if="!showOtherDetails" class="block md:hidden p-2 space-y-4">
            <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
              <div class="space-y-3">
                <div class="border-b pb-2">
                  <h3 class="font-semibold text-sm text-gray-800 mb-1">{{ focusedType }}</h3>
                  <p class="text-xs text-gray-600">
                    <span v-if="plan.gadMandate">GAD Mandate</span>
                    <span v-else-if="plan.genderIssue">Gender Issue</span>
                    <span v-else>{{ issueOrMandate.value }}</span>
                  </p>
                </div>

                <div class="space-y-2 text-xs">
                  <div>
                    <span class="font-semibold text-gray-700">Description:</span>
                    <p class="mt-1">{{ titleDescription }}</p>
                  </div>

                  <div>
                    <span class="font-semibold text-gray-700">GAD Objective:</span>
                    <p class="mt-1">{{ getPropertyValue('gadObjective') }}</p>
                  </div>

                  <div>
                    <span class="font-semibold text-gray-700">GAD Program/Project:</span>
                    <p class="mt-1">{{ getPropertyValue('lguProgram') }}</p>
                  </div>

                  <div>
                    <span class="font-semibold text-gray-700">GAD Activity:</span>
                    <p class="mt-1">{{ getPropertyValue('gadActivity') }}</p>
                  </div>

                  <div>
                    <span class="font-semibold text-gray-700">Performance Indicator:</span>
                    <p class="mt-1">{{ getPropertyValue('performanceIndicator') }}</p>
                  </div>

                  <div>
                    <span class="font-semibold text-gray-700">Responsible Office:</span>
                    <p class="mt-1">{{ getPropertyValue('responsibleOffice') }}</p>
                  </div>
                </div>

                <!-- Budget Section -->
                <div class="border-t pt-3">
                  <h4 class="font-semibold text-sm text-gray-800 mb-2">GAD Budget</h4>
                  <div class="grid grid-cols-3 gap-2 text-xs">
                    <div class="text-center">
                      <span class="block font-semibold text-gray-700">MOOE</span>
                      <span class="block mt-1">{{ formatNumber(getPropertyValue('mooe')) }}</span>
                    </div>
                    <div class="text-center">
                      <span class="block font-semibold text-gray-700">PS</span>
                      <span class="block mt-1">{{ formatNumber(getPropertyValue('ps')) }}</span>
                    </div>
                    <div class="text-center">
                      <span class="block font-semibold text-gray-700">CO</span>
                      <span class="block mt-1">{{ formatNumber(getPropertyValue('co')) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Sub-total Section -->
                <div class="bg-emerald-100 p-3 rounded">
                  <div class="flex justify-between items-center">
                    <span class="font-bold text-sm">Sub-total</span>
                  </div>
                  <div class="grid grid-cols-3 gap-2 mt-2 text-xs">
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.mooe && plan.mooe > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.mooe || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                    <div class="text-center" v-else>
                      <span class="font-bold">{{ plan.mooe !== undefined ? plan.mooe.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}</span>
                    </div>
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.ps && plan.ps > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.ps || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                    <div class="text-center" v-else>
                      <span class="font-bold">{{ plan.ps !== undefined ? plan.ps.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}</span>
                    </div>
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.co && plan.co > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.co || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                    <div class="text-center" v-else>
                      <span class="font-bold">{{ plan.co !== undefined ? plan.co.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}</span>
                    </div>
                  </div>
                </div>

                <!-- Total Section -->
                <div class="bg-emerald-200 p-3 rounded">
                  <div class="flex justify-between items-center">
                    <span class="font-bold text-sm">TOTAL (MOOE + PS + CO)</span>
                    <span class="font-bold text-sm">{{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                  </div>
                </div>

                <!-- Grand Total Section -->
                <div class="bg-emerald-300 p-3 rounded">
                  <div class="flex justify-between items-center">
                    <span class="font-bold text-sm">GRAND TOTAL (A+B+C)</span>
                    <span class="font-bold text-lg">{{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                  </div>
                </div>

                <!-- Action Button -->
                <div class="flex justify-center pt-2">
                  <button
                    @click="toggleDetailsView"
                    class="flex items-center space-x-1.5 text-sm text-gray-600 hover:text-gray-800 cursor-pointer"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{{ showOtherDetails ? 'Main table' : 'Other details' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Desktop Table View -->
          <div class="hidden md:block p-2 sm:p-4">
            <div class="overflow-x-auto">
              <table v-if="!showOtherDetails" class="w-full border border-gray-400 text-xs bg-white min-w-[800px]">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Gender Issue or GAD Mandate</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Objective</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Relevant GAD Program or Project</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Activity</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Performance Indicator and Target</th>
                  <th colspan="3" class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Budget (6)</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Lead or Responsible Office</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-white"></th>
                </tr>
                <tr>
                  <th colspan="5" class="border border-gray-400 font-bold text-left px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">{{ focusedType }}</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">MOOE</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">PS</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">CO</th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                </tr>
                <tr>
                  <td colspan="5" class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 font-semibold bg-gray-100 text-xs">
                    <span v-if="plan.gadMandate">GAD Mandate</span>
                    <span v-else-if="plan.genderIssue">Gender Issue</span>
                    <span v-else>{{ issueOrMandate.value }}</span>
                  </td>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                  <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2"></th>
                </tr>
              </thead>
            <tbody>
              <tr>
                <!-- Gender Issue or GAD Mandate -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('genderIssue') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('genderIssue'), position: 'relative' }"
                  @click="onCellClick('genderIssue')"
                  @mouseenter="hoveredCell = 'genderIssue'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ titleDescription }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('genderIssue')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('genderIssue')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('genderIssue') && (commentMode || visibleCommentCell === 'genderIssue') && activeCommentCell !== 'genderIssue'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('genderIssue') }}
                  </span>
                  <!-- Edit icon below comment output in comment mode -->
                  <div v-if="getCellComment('genderIssue') && commentMode && activeCommentCell !== 'genderIssue' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'genderIssue'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'genderIssue' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['genderIssue'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['genderIssue'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('genderIssue', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('genderIssue', cellComments['genderIssue'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- GAD Objective -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('gadObjective') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('gadObjective'), position: 'relative' }"
                  @click="onCellClick('gadObjective')"
                  @mouseenter="hoveredCell = 'gadObjective'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ getPropertyValue('gadObjective') }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('gadObjective')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('gadObjective')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('gadObjective') && (commentMode || visibleCommentCell === 'gadObjective') && activeCommentCell !== 'gadObjective'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('gadObjective') }}
                  </span>
                  <div v-if="getCellComment('gadObjective') && commentMode && activeCommentCell !== 'gadObjective' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'gadObjective'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'gadObjective' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['gadObjective'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['gadObjective'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('gadObjective', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('gadObjective', cellComments['gadObjective'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- Relevant GAD Program or Project -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('lguProgram') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('lguProgram'), position: 'relative' }"
                  @click="onCellClick('lguProgram')"
                  @mouseenter="hoveredCell = 'lguProgram'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ getPropertyValue('lguProgram') }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('lguProgram')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('lguProgram')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('lguProgram') && (commentMode || visibleCommentCell === 'lguProgram') && activeCommentCell !== 'lguProgram'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('lguProgram') }}
                  </span>
                  <div v-if="getCellComment('lguProgram') && commentMode && activeCommentCell !== 'lguProgram' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'lguProgram'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'lguProgram' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['lguProgram'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['lguProgram'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('lguProgram', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('lguProgram', cellComments['lguProgram'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- GAD Activity -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('gadActivity') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('gadActivity'), position: 'relative' }"
                  @click="onCellClick('gadActivity')"
                  @mouseenter="hoveredCell = 'gadActivity'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ getPropertyValue('gadActivity') }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('gadActivity')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('gadActivity')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('gadActivity') && (commentMode || visibleCommentCell === 'gadActivity') && activeCommentCell !== 'gadActivity'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('gadActivity') }}
                  </span>
                  <div v-if="getCellComment('gadActivity') && commentMode && activeCommentCell !== 'gadActivity' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'gadActivity'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'gadActivity' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['gadActivity'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['gadActivity'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('gadActivity', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('gadActivity', cellComments['gadActivity'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- Performance Indicator and Target -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('performanceIndicator') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('performanceIndicator'), position: 'relative' }"
                  @click="onCellClick('performanceIndicator')"
                  @mouseenter="hoveredCell = 'performanceIndicator'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ getPropertyValue('performanceIndicator') }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('performanceIndicator')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('performanceIndicator')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('performanceIndicator') && (commentMode || visibleCommentCell === 'performanceIndicator') && activeCommentCell !== 'performanceIndicator'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('performanceIndicator') }}
                  </span>
                  <div v-if="getCellComment('performanceIndicator') && commentMode && activeCommentCell !== 'performanceIndicator' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'performanceIndicator'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'performanceIndicator' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['performanceIndicator'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['performanceIndicator'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('performanceIndicator', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('performanceIndicator', cellComments['performanceIndicator'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- MOOE -->
                <td
                  class="border border-gray-400 px-2 py-2 text-right"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('mooe') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('mooe'), position: 'relative' }"
                  @click="onCellClick('mooe')"
                  @mouseenter="hoveredCell = 'mooe'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ formatNumber(getPropertyValue('mooe')) }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('mooe')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('mooe')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('mooe') && (commentMode || visibleCommentCell === 'mooe') && activeCommentCell !== 'mooe'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('mooe') }}
                  </span>
                  <div v-if="getCellComment('mooe') && commentMode && activeCommentCell !== 'mooe' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'mooe'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'mooe' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['mooe'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['mooe'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('mooe', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('mooe', cellComments['mooe'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- PS -->
                <td
                  class="border border-gray-400 px-2 py-2 text-right"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('ps') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('ps'), position: 'relative' }"
                  @click="onCellClick('ps')"
                  @mouseenter="hoveredCell = 'ps'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ formatNumber(getPropertyValue('ps')) }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('ps')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('ps')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('ps') && (commentMode || visibleCommentCell === 'ps') && activeCommentCell !== 'ps'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('ps') }}
                  </span>
                  <div v-if="getCellComment('ps') && commentMode && activeCommentCell !== 'ps' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'ps'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'ps' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['ps'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['ps'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('ps', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('ps', cellComments['ps'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- CO -->
                <td
                  class="border border-gray-400 px-2 py-2 text-right"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('co') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('co'), position: 'relative' }"
                  @click="onCellClick('co')"
                  @mouseenter="hoveredCell = 'co'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ formatNumber(getPropertyValue('co')) }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('co')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('co')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('co') && (commentMode || visibleCommentCell === 'co') && activeCommentCell !== 'co'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('co') }}
                  </span>
                  <div v-if="getCellComment('co') && commentMode && activeCommentCell !== 'co' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'co'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'co' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['co'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['co'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('co', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('co', cellComments['co'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- Lead or Responsible Office -->
                <td
                  class="border border-gray-400 px-2 py-2"
                  :class="[commentMode ? 'comment-plus-cursor' : '', getCellComment('responsibleOffice') ? 'cell-has-comment' : '']"
                  :style="{ background: getCellColor('responsibleOffice'), position: 'relative' }"
                  @click="onCellClick('responsibleOffice')"
                  @mouseenter="hoveredCell = 'responsibleOffice'"
                  @mouseleave="hoveredCell = null"
                >
                  {{ getPropertyValue('responsibleOffice') }}
                  <!-- Eye icon indicator if comment exists -->
                  <div v-if="getCellComment('responsibleOffice')" class="cell-comment-indicator-badge-wrapper">
                    <span class="cell-comment-indicator-badge view-comment-badge" v-if="!commentMode" @click.stop="toggleVisibleCommentCell('responsibleOffice')">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="icon-eye"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="black"
                        stroke-width="2"
                        style="width: 1.2em; height: 1.2em; margin-right: 0.3em;"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                  </div>
                  <!-- Popover for viewing comment -->
                  <span
                    v-if="getCellComment('responsibleOffice') && (commentMode || visibleCommentCell === 'responsibleOffice') && activeCommentCell !== 'responsibleOffice'"
                    :class="['popover-comment', 'popover-comment-large', !commentMode ? 'popover-comment-visible' : '']"
                  >
                    {{ getCellComment('responsibleOffice') }}
                  </span>
                  <div v-if="getCellComment('responsibleOffice') && commentMode && activeCommentCell !== 'responsibleOffice' && commentsEditable" class="flex justify-center mt-1">
                    <span
                      class="cell-comment-indicator-badge edit-comment-badge"
                      @click.stop="activeCommentCell = 'responsibleOffice'"
                      title="Edit comment"
                      tabindex="0"
                      role="button"
                      style="user-select: none; outline: none;"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-edit" fill="none" viewBox="0 0 24 24" stroke="#2563eb" stroke-width="2" style="width: 1.1em; height: 1.1em; margin-right: 0.3em;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2v6H3a1 1 0 01-1-1v-2z" /></svg>
                      <span class="badge-text">Edit Comment</span>
                    </span>
                  </div>
                  <!-- Popover for editing comment -->
                  <div
                    v-if="activeCommentCell === 'responsibleOffice' && commentsEditable"
                    class="popover-comment popover-edit"
                    @click.stop
                  >
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs font-semibold">Edit Comment</span>
                    </div>
                    <textarea
                      v-model="cellComments['responsibleOffice'].text"
                      class="border rounded px-2 py-1 text-lg w-full mb-2 resize-vertical min-h-[60px] font-bold"
                      placeholder="Add comment..."
                      rows="3"
                      autofocus
                    />
                    <div class="flex gap-1 items-center mb-2">
                      <span class="text-xs mr-1">Color:</span>
                      <span v-for="color in commentColors" :key="color.value" class="color-swatch"
                        :style="{ background: color.value, border: cellComments['responsibleOffice'].color === color.value ? '2px solid #2563eb' : '1px solid #ccc' }"
                        @click="setCellCommentColor('responsibleOffice', color.value)"
                      ></span>
                    </div>
                    <div class="flex justify-end gap-2">
                      <button class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs font-semibold" @click.stop="activeCommentCell = null">Close</button>
                      <button class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-xs font-semibold" @click.stop="saveCellComment('responsibleOffice', cellComments['responsibleOffice'].text)" :disabled="!commentsEditable">Save</button>
                    </div>
                  </div>
                </td>
                <!-- Action cell (no comment) -->
                <td class="border border-gray-400 px-2 py-2">
                  <div class="flex flex-col space-y-1.5">
                    <!-- Other details button -->
                    <button
                      @click="toggleDetailsView"
                      class="flex items-center space-x-1.5 text-sm text-gray-600 hover:text-gray-800 text-left  cursor-pointer"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{{ showOtherDetails ? 'Main table' : 'Other details' }}</span>
                    </button>
                  </div>
                </td>
              </tr>
              <!-- Sub-total row -->
              <tr class="bg-emerald-100">
                <td class="text-left font-bold border border-gray-400 px-2 py-2" colspan="5">Sub-total</td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.mooe && plan.mooe > 0">
                  {{ Math.floor(Number(plan.mooe || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-else>
                  {{ plan.mooe !== undefined ? plan.mooe.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}
                </td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.ps && plan.ps > 0">
                  {{ Math.floor(Number(plan.ps || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-else>
                  {{ plan.ps !== undefined ? plan.ps.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}
                </td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.co && plan.co > 0">
                  {{ Math.floor(Number(plan.co || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border border-gray-400 px-2 py-2 text-right font-bold" v-else>
                  {{ plan.co !== undefined ? plan.co.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00' }}
                </td>
                <td class="border border-gray-400 px-2 py-2"></td>
                <td class="border border-gray-400 px-2 py-2"></td>
              </tr>
              <!-- Total row with LP included -->
              <tr class="bg-emerald-200 font-bold">
                <td class="text-left font-bold border-l border-t border-b border-gray-400 px-2 py-2" colspan="5">
                  TOTAL (MOOE + PS + CO)
                </td>
                <td class="border-t border-b border-l border-gray-400 px-2 py-2"></td>
                <td class="border-t border-b border-gray-400 px-2 py-2 font-bold text-center">
                  {{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border-t border-b border-r border-gray-400 px-2 py-2"></td>
                <td class="border border-gray-400 px-2 py-2"></td>
                <td class="border border-gray-400 px-2 py-2"></td>
              </tr>
              <!-- Grand total row -->
              <tr class="bg-emerald-300 font-bold">
                <td colspan="5" class="border border-gray-400 px-2 py-2 text-left">
                  GRAND TOTAL (A+B+C)
                  <div class="text-xs text-emerald-700 mt-1 font-normal">
                  </div>
                </td>
                <td class="border-t border-b border-l border-gray-400 px-2 py-2"></td>
                <td class="border-t border-b border-gray-400 px-2 py-2 text-right font-bold text-sm sm:text-lg">
                  {{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border-t border-b border-r border-gray-400 px-2 py-2"></td>
                <td class="border border-gray-400 px-2 py-2"></td>
                <td class="border border-gray-400 px-2 py-2"></td>
              </tr>
      <!-- Prepared/Approved by row -->
      <tr>
<td colspan="2" class="border-b border-t border-l border-gray-400 p-3">
  <div class="font-semibold mb-1">Prepared by:</div>
  <div class="font-bold text-base mb-0">{{ props.plan.user_full_name || props.plan.user_name || 'UNKNOWN USER' }}</div>
  <div class="text-xs mt-1">Barangay Gad Focal</div>
</td>
        <td colspan="2" class="border border-gray-400 p-3">
          <div class="font-semibold mb-2 text-gray-700">Approved by:</div>
                           <div class="font-bold text-base mb-0">
            <br>
              <span class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">Punong Barangay</div>
        </td>
        <td colspan="6" class="border-b border-t border-r border-gray-400 p-3">
          <div class="font-semibold mb-1">Date:</div>
                           <div class="font-bold text-base mb-0">
            <br>
              <span class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">DD/MM/YEAR</div>
        </td>
      </tr>
      <tr>
  <td colspan="4" class="border-b border-t border-l border-gray-400 px-2 py-2">
  <div class="font-semibold mb-1">Verified and Endorsed by:</div>
                   <div class="font-bold text-base mb-0">
            <br>
              <span v-if="plan.status === 'Approved' && plan.admin_approver" class="text-black">{{ plan.admin_approver }}</span>
              <span v-else class="text-gray-400"></span>

          </div>
    <div class="text-xs mt-1">Chairperson, GFPS TWG/secretary</div>
  </td>
  <td colspan="5" class="border-b border-t border-gray-400 px-2 py-2">
    <div class="font-semibold mb-1">Date Verified:</div>
                 <div class="font-bold text-base mb-0">
            <br>
              <span v-if="plan.status === 'Approved' && plan.approval_date" class="text-black">{{ formatApprovalDate(plan.approval_date) }}</span>
              <span v-else class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">DD/MM/YEAR</div>
</td>
  <td class="border-gray-400 px-2 py-2 flex flex-col items-center justify-center gap-3 whitespace-nowrap">
  <button
    @click="approvebudget"
    :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
    class="bg-gradient-to-r from-emerald-500 to-emerald-600 border border-emerald-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-emerald-600 hover:to-emerald-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px]"
  >
    <svg class="w-4 h-4" fill="none" :stroke="plan.status === 'Approved' ? '#10b981' : 'currentColor'" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
    </svg>
    Approved
  </button>
  <button
    @click="disapproved"
    :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
    class="bg-gradient-to-r from-red-500 to-red-600 border border-red-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-red-600 hover:to-red-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px]"
  >
    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
    <span class="text-blue-200 text-white">Disapproved</span>
  </button>
  <button
    @click="revised"
    :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
    class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-1.5 text-xs font-semibold flex items-center justify-start gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer w-[140px]"
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
    Revised
  </button>
</td>
</tr>
    </tbody>
  </table>

  <!-- Other Details Table - shown when showOtherDetails is true -->
  <table v-else class="w-full border border-gray-400 text-xs bg-white">
    <thead>
      <tr class="bg-gray-100">
        <th class="border border-gray-400 px-2 py-2">Supporting Statistics Data</th>
        <th class="border border-gray-400 px-2 py-2">Source of Supporting Statistics Data</th>
        <th class="border border-gray-400 px-2 py-2">PPA/s</th>
        <th class="border border-gray-400 px-2 py-2">Activity</th>
        <th class="border border-gray-400 px-2 py-2">Other Activity Category</th>
        <th class="border border-gray-400 px-2 py-2">Implementation Start</th>
        <th class="border border-gray-400 px-2 py-2">Implementation End</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('supportingStats') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('sourceStats') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatArrayValue(getPropertyValue('ppaSi')) }}</td>
        <td class="border border-gray-400 px-2 py-2">
          <div v-if="Array.isArray(getPropertyValue('activity'))">
            <div v-for="(act, index) in getPropertyValue('activity')" :key="index" class="mb-1">
              - {{ formatActivityValue(act) }}
            </div>
          </div>
          <div v-else>{{ formatArrayValue(getPropertyValue('activity')) || '-' }}</div>
        </td>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('otherActivityCategory') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatDate(getPropertyValue('dateImplementationStart')) }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatDate(getPropertyValue('dateImplementationEnd')) }}</td>
      </tr>
    </tbody>
              </table>
            </div>
          </div>

          <!-- Mobile Other Details View -->
          <div v-if="showOtherDetails" class="block md:hidden p-2 space-y-4">
            <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
              <h3 class="font-semibold text-sm text-gray-800 mb-3">Other Details</h3>
              <div class="space-y-3 text-xs">
                <div>
                  <span class="font-semibold text-gray-700">Supporting Statistics Data:</span>
                  <p class="mt-1">{{ getPropertyValue('supportingStats') || '-' }}</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Source of Supporting Statistics Data:</span>
                  <p class="mt-1">{{ getPropertyValue('sourceStats') || '-' }}</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">PPA/s:</span>
                  <p class="mt-1">{{ formatArrayValue(getPropertyValue('ppaSi')) }}</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Activity:</span>
                  <div class="mt-1">
                    <div v-if="Array.isArray(getPropertyValue('activity'))">
                      <div v-for="(act, index) in getPropertyValue('activity')" :key="index" class="mb-1">
                        - {{ formatActivityValue(act) }}
                      </div>
                    </div>
                    <div v-else>{{ formatArrayValue(getPropertyValue('activity')) || '-' }}</div>
                  </div>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Other Activity Category:</span>
                  <p class="mt-1">{{ getPropertyValue('otherActivityCategory') || '-' }}</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Implementation Start:</span>
                  <p class="mt-1">{{ formatDate(getPropertyValue('dateImplementationStart')) }}</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Implementation End:</span>
                  <p class="mt-1">{{ formatDate(getPropertyValue('dateImplementationEnd')) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Signature Section -->
          <div class="block md:hidden p-2">
            <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm space-y-4">
              <div class="grid grid-cols-1 gap-4 text-xs">
                <div class="border-b pb-3">
                  <span class="font-semibold text-gray-700">Prepared by:</span>
                  <p class="mt-1 font-bold text-sm">{{ props.plan.user_full_name || props.plan.user_name || 'UNKNOWN USER' }}</p>
                  <p class="text-xs text-gray-600">Barangay GAD Focal</p>
                </div>
                <div class="border-b pb-3">
                  <span class="font-semibold text-gray-700">Approved by:</span>
                  <p class="mt-1 font-bold text-sm">
                    <br>
                    <span class="text-gray-400"></span>
                  </p>
                  <p class="text-xs text-gray-600">Punong Barangay</p>
                </div>
                <div class="border-b pb-3">
                  <span class="font-semibold text-gray-700">Date:</span>
                  <p class="mt-1 font-bold text-sm">
                    <br>
                    <span class="text-gray-400"></span>
                  </p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
                <div class="border-b pb-3">
                  <span class="font-semibold text-gray-700">Verified and Endorsed by:</span>
                  <p v-if="plan.status === 'Approved' && plan.admin_approver" class="mt-1 font-bold text-sm text-black">{{ plan.admin_approver }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Chairperson, GFPS TWG/secretary</p>
                </div>
                <div class="border-b pb-3">
                  <span class="font-semibold text-gray-700">Date Verified:</span>
                  <p v-if="plan.status === 'Approved' && plan.approval_date" class="mt-1 font-bold text-sm text-black">{{ formatApprovalDate(plan.approval_date) }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>

              <!-- Mobile Action Buttons -->
              <div class="flex flex-col gap-2 pt-3">
                <button @click="approvebudget" 
                :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
                class="bg-gradient-to-r from-emerald-500 to-emerald-600 border border-emerald-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-emerald-600 hover:to-emerald-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Approved
                </button>
                <button @click="disapproved" 
                :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
                class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Disapproved
                </button>
                 <button @click="revised" 
                 :disabled="plan.status === 'Approved' || plan.status === 'Disapproved' || plan.status === 'Revision'"
                 class="bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-600 px-4 py-2 text-sm font-semibold flex items-center justify-center gap-2 text-white hover:from-blue-600 hover:to-blue-700 rounded-md shadow-sm transition-all duration-200 cursor-pointer">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Revised
                </button>
              </div>
            </div>
          </div>
        </div>
<!-- Add more detailed fields below as needed -->
      <!-- Comments Modal -->
      <div v-if="showComments" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-4 sm:p-6 w-full max-w-md mx-4 sm:mx-auto max-h-[90vh] overflow-auto">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg sm:text-xl font-bold">Comments</h2>
            <button @click="showComments = false" class="text-gray-500 hover:text-gray-700 text-xl">&times;</button>
          </div>
          <div class="space-y-4 max-h-60 overflow-auto">
            <div v-for="comment in comments" :key="comment.id" class="border-b pb-2">
              <div class="flex justify-between text-sm">
                <span class="font-semibold">{{ comment.author }}</span>
                <span class="text-gray-500">{{ formatDate(comment.date) }}</span>
              </div>
              <p class="text-sm mt-1">{{ comment.text }}</p>
            </div>
          </div>
          <div class="mt-4">
            <textarea
              v-model="newComment"
              class="w-full border rounded p-2 text-sm"
              placeholder="Add a comment..."
              rows="3"
            ></textarea>
            <div class="mt-2 flex justify-end gap-2">
              <button @click="addComment" class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-sm">Add Comment</button>
              <button @click="showComments = false" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-sm">Close</button>
            </div>
          </div>
        </div>
      </div>
      <!-- Remove the duplicate Other Details Table section -->
    </div>

    <!-- Custom Alert/Confirm Modal -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button
            v-if="modalType === 'confirm'"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            @click="modalType === 'confirm' ? confirmAction() : closeModal()"
            class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
          >
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import * as XLSX from 'xlsx';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';
import { User } from 'lucide-vue-next';

const props = defineProps<{
  show: boolean;
  plan: any | null;
}>();

const emit = defineEmits(['close', 'delete-row', 'attach-file', 'save-other-details']);

const page = usePage<{ auth: { user: { name: string; first_name?: string; middle_name?: string; last_name?: string; suffix?: string; role?: string; } } }>();
const approverName = ref('');

// Modal state variables
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref('info'); // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref<(() => void) | null>(null);

// Add this function to get admin's full name
function getAdminFullName() {
  // Get the current authenticated user from page props
  const user = page.props.auth.user;

  if (!user) return 'Unknown Admin';

  // Build the full name properly
  let fullName = '';

  if (user.first_name) {
    fullName += user.first_name;
  } else if (user.name) {
    fullName += user.name;
  }

  if (user.middle_name) {
    fullName += ' ' + user.middle_name.charAt(0) + '.';
  }

  if (user.last_name) {
    fullName += ' ' + user.last_name;
  }

  if (user.suffix) {
    fullName += ', ' + user.suffix;
  }

  // If we couldn't build a name from the parts, fall back to the name field
  if (!fullName.trim() && user.name) {
    fullName = user.name;
  }

  // Return the full name in uppercase
  return fullName.trim().toUpperCase();
}

// Modal functions
function showModal(title: string, message: string, type: string = 'info') {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = type;
  modalConfirmCallback.value = null;
  modalVisible.value = true;
}

function showConfirmModal(title: string, message: string, onConfirm: () => void) {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = 'confirm';
  modalConfirmCallback.value = onConfirm;
  modalVisible.value = true;
}

function closeModal() {
  modalVisible.value = false;
  modalConfirmCallback.value = null;
}

function confirmAction() {
  if (modalConfirmCallback.value) {
    modalConfirmCallback.value();
  }
  closeModal();
}

const showComments = ref(false);
const showOtherDetails = ref(false);
const newComment = ref('');
const comments = ref<any[]>([]);
const submitterFullName = ref('');
const isLoadingSubmitter = ref(false);

// Function to toggle between main table and other details
function toggleDetailsView() {
  showOtherDetails.value = !showOtherDetails.value;
}

// Add a computed property to determine if we should show Gender Issue or GAD Mandate
const issueOrMandate = computed(() => {
  if (!props.plan) return { type: null, value: '-' };

  if (props.plan.genderIssue || props.plan.gender_issue) {
    return {
      type: 'Gender Issue',
      value: props.plan.genderIssue || props.plan.gender_issue
    };
  }

  if (props.plan.gadMandate || props.plan.gad_mandate) {
    return {
      type: 'GAD Mandate',
      value: props.plan.gadMandate || props.plan.gad_mandate
    };
  }

  return { type: null, value: '-' };
});

// Add a computed property for the title/description
const titleDescription = computed(() => {
  if (!props.plan) return '-';

  // Check for title/description in different possible property names
  return props.plan.titleDesc ||
         props.plan.title_desc ||
         props.plan.title ||
         props.plan.description ||
         '-';
});

// Add a computed property for the focused type
const focusedType = computed(() => {
  if (!props.plan) return '-';

  // Check for focused property in different formats
  const focused = props.plan.focused || props.plan.focus || '';

  // Return the value if it exists, otherwise default to '-'
  return focused || '-';
});

function formatNumber(value?: number): string {
  if (value === undefined || value === null || value === 0) return '';
  return value.toLocaleString(undefined, { minimumFractionDigits: 2 });
}

function deleteRow() {
  if (props.plan) {
    emit('delete-row', props.plan);
  }
}



function viewDetails() {
  showOtherDetails.value = true;
}

function openComments() {
  showComments.value = true;
}

function addComment() {
  if (newComment.value.trim()) {
    const comment = {
      id: Date.now(),
      author: page.props.auth.user.name || 'Anonymous',
      date: new Date().toISOString(),
      text: newComment.value.trim()
    };
    comments.value.push(comment);
    newComment.value = '';
  }
}

function exportToExcel() {
  if (!props.plan) return;

  // Add computed property for current year
  const currentYear = new Date().getFullYear();

  // Create structured data that matches the print preview format
  const structuredData = [
    // Header row
    ['BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) FY ' + currentYear],
    [''], // Empty row for spacing

    // Main table headers
    ['Gender Issue or GAD Mandate', 'GAD Objective', 'Relevant GAD Program or Project', 'GAD Activity', 'Performance Indicator and Target', 'GAD Budget (6)', '', '', 'Lead or Responsible Office'],
    ['', '', '', '', '', 'MOOE', 'PS', 'CO', ''],

    // Focused type row
    [focusedType.value, '', '', '', '', 'MOOE', 'PS', 'CO', ''],

    // Issue/Mandate type row
    [props.plan?.gadMandate ? 'GAD Mandate' : props.plan?.genderIssue ? 'Gender Issue' : issueOrMandate.value, '', '', '', '', '', '', '', ''],

    // Data row
    [
      titleDescription.value,
      getPropertyValue('gadObjective'),
      getPropertyValue('lguProgram'),
      getPropertyValue('gadActivity'),
      getPropertyValue('performanceIndicator'),
      formatNumber(getPropertyValue('mooe')),
      formatNumber(getPropertyValue('ps')),
      formatNumber(getPropertyValue('co')),
      getPropertyValue('responsibleOffice')
    ],

    // Sub-total row
    [
      'Sub-total', '', '', '', '',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.mooe && props.plan.mooe > 0
        ? Math.floor(Number(props.plan.mooe || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : props.plan?.mooe !== undefined ? props.plan.mooe.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.ps && props.plan.ps > 0
        ? Math.floor(Number(props.plan.ps || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : props.plan?.ps !== undefined ? props.plan.ps.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.co && props.plan.co > 0
        ? Math.floor(Number(props.plan.co || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : props.plan?.co !== undefined ? props.plan.co.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00',
      ''
    ],

    // Total row
    [
      'TOTAL (MOOE + PS + CO + LP):', '', '', '', '', '',
      Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}),
      '', ''
    ],

    // Grand Total row
    [
      'GRAND TOTAL (A+B+C+LP)', '', '', '', '', '',
      Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}),
      '', ''
    ],

    [''], // Empty row for spacing
    [''], // Empty row for spacing

    // Other Details section
    ['OTHER DETAILS'],
    ['Supporting Statistics Data', 'Source of Supporting Statistics Data', 'PPA/s', 'Activity', 'Other Activity Category', 'Implementation Start', 'Implementation End'],
    [
      getPropertyValue('supportingStats') || '-',
      getPropertyValue('sourceStats') || '-',
      formatArrayValue(getPropertyValue('ppaSi')),
      Array.isArray(getPropertyValue('activity'))
        ? getPropertyValue('activity').map((act: any) => `- ${formatActivityValue(act)}`).join('\n')
        : formatArrayValue(getPropertyValue('activity')) || '-',
      getPropertyValue('otherActivityCategory') || '-',
      formatDate(getPropertyValue('dateImplementationStart')),
      formatDate(getPropertyValue('dateImplementationEnd'))
    ],

    [''], // Empty row for spacing
    [''], // Empty row for spacing

    // Signature section
    ['SIGNATURES'],
    ['Prepared by:', '', 'Approved by:', '', 'Date:'],
    [props.plan?.user_full_name || props.plan?.user_name || 'UNKNOWN USER', '', '_________________', '', '_________________'],
    ['Barangay GAD Focal', '', 'Punong Barangay', '', 'DD/MM/YEAR'],
    [''], // Empty row
    ['Verified and Endorsed by:', '', '', 'Date Verified:'],
    [props.plan?.status === 'Approved' && props.plan?.admin_approver ? props.plan.admin_approver : '_________________', '', '', props.plan?.status === 'Approved' && props.plan?.approval_date ? formatApprovalDate(props.plan.approval_date) : '_________________'],
    ['Chairperson, GFPS TWG/secretary', '', '', 'DD/MM/YEAR']
  ];

  // Create worksheet from the structured data
  const ws = XLSX.utils.aoa_to_sheet(structuredData);

  // Set column widths for better formatting
  ws['!cols'] = [
    { wch: 25 }, // Gender Issue or GAD Mandate
    { wch: 20 }, // GAD Objective
    { wch: 25 }, // Relevant GAD Program or Project
    { wch: 20 }, // GAD Activity
    { wch: 25 }, // Performance Indicator and Target
    { wch: 15 }, // MOOE
    { wch: 15 }, // PS
    { wch: 15 }, // CO
    { wch: 20 }  // Lead or Responsible Office
  ];

  // Create workbook and add the worksheet
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'GAD Plan FY ' + currentYear);

  // Generate filename with current year
  const filename = `Admin_GAD_Plan_FY${currentYear}.xlsx`;
  XLSX.writeFile(wb, filename);
}

function printpreview() {
  // Create a new window for printing only the modal content
  const printWindow = window.open('', '_blank', 'width=800,height=600');

  if (!printWindow) {
    alert('Please allow popups to enable printing');
    return;
  }

  // Get the print content (both main table and other details)
  const printContent = generatePrintContent();

  // Write the complete HTML document to the print window
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Barangay GAD Plan FY ${new Date().getFullYear()}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          font-size: 12px;
          line-height: 1.4;
        }

        .header {
          text-align: center;
          margin-bottom: 20px;
          font-weight: bold;
          font-size: 16px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          page-break-inside: avoid;
        }

        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
          vertical-align: top;
          font-size: 10px;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .text-center {
          text-align: center;
        }

        .text-right {
          text-align: right;
        }

        .font-bold {
          font-weight: bold;
        }

        .bg-emerald-100 {
          background-color: #d1fae5;
        }

        .bg-emerald-200 {
          background-color: #a7f3d0;
        }

        .bg-emerald-300 {
          background-color: #6ee7b7;
        }

        .section-title {
          font-size: 14px;
          font-weight: bold;
          margin: 30px 0 15px 0;
          text-align: center;
        }

        .signature-section {
          margin-top: 20px;
        }

        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `);

  printWindow.document.close();

  // Wait for content to load, then print
  printWindow.onload = function() {
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
}

function generatePrintContent() {
  const currentYear = new Date().getFullYear();
  const userFullName = props.plan?.user_full_name || props.plan?.user_name || 'UNKNOWN USER';

  return `
    <div class="header">
      BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) FY ${currentYear}
    </div>

    <!-- Main Budget Table -->
    <table>
      <thead>
        <tr>
          <th>Gender Issue or GAD Mandate</th>
          <th>GAD Objective</th>
          <th>Relevant GAD Program or Project</th>
          <th>GAD Activity</th>
          <th>Performance Indicator and Target</th>
          <th>MOOE</th>
          <th>PS</th>
          <th>CO</th>
          <th>Lead or Responsible Office</th>
        </tr>
        <tr>
          <th colspan="5" class="font-bold">${focusedType.value}</th>
          <th class="text-center">MOOE</th>
          <th class="text-center">PS</th>
          <th class="text-center">CO</th>
          <th></th>
        </tr>
        <tr>
          <td colspan="5" class="font-bold bg-emerald-100">
            ${props.plan?.gadMandate ? 'GAD Mandate' : props.plan?.genderIssue ? 'Gender Issue' : issueOrMandate.value}
          </td>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${titleDescription.value}</td>
          <td>${getPropertyValue('gadObjective')}</td>
          <td>${getPropertyValue('lguProgram')}</td>
          <td>${getPropertyValue('gadActivity')}</td>
          <td>${getPropertyValue('performanceIndicator')}</td>
          <td class="text-right">${formatNumber(getPropertyValue('mooe'))}</td>
          <td class="text-right">${formatNumber(getPropertyValue('ps'))}</td>
          <td class="text-right">${formatNumber(getPropertyValue('co'))}</td>
          <td>${getPropertyValue('responsibleOffice')}</td>
        </tr>

        <!-- Sub-total row -->
        <tr class="bg-emerald-100">
          <td colspan="5" class="font-bold">Sub-total</td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.mooe && props.plan.mooe > 0
              ? Math.floor(Number(props.plan.mooe || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : props.plan?.mooe !== undefined ? props.plan.mooe.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00'}
          </td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.ps && props.plan.ps > 0
              ? Math.floor(Number(props.plan.ps || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : props.plan?.ps !== undefined ? props.plan.ps.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00'}
          </td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.co && props.plan.co > 0
              ? Math.floor(Number(props.plan.co || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : props.plan?.co !== undefined ? props.plan.co.toLocaleString(undefined, {minimumFractionDigits:2}) : '0.00'}
          </td>
          <td></td>
        </tr>

        <!-- Total row -->
        <tr class="bg-emerald-200 font-bold">
          <td colspan="5" class="font-bold">TOTAL (MOOE + PS + CO + LP):</td>
          <td></td>
          <td class="text-center font-bold">
            ${Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}
          </td>
          <td></td>
          <td></td>
        </tr>

        <!-- Grand Total row -->
        <tr class="bg-emerald-300 font-bold">
          <td colspan="5" class="font-bold">GRAND TOTAL (A+B+C+LP)</td>
          <td></td>
          <td class="text-right font-bold" style="font-size: 14px;">
            ${Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}
          </td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>

    <!-- Other Details Section - Centered below Grand Total -->

    <table>
      <thead>
        <tr>
          <th>Supporting Statistics Data</th>
          <th>Source of Supporting Statistics Data</th>
          <th>PPA/s</th>
          <th>Activity</th>
          <th>Other Activity Category</th>
          <th>Implementation Start</th>
          <th>Implementation End</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${getPropertyValue('supportingStats') || '-'}</td>
          <td>${getPropertyValue('sourceStats') || '-'}</td>
          <td>${formatArrayValue(getPropertyValue('ppaSi'))}</td>
          <td>
            ${Array.isArray(getPropertyValue('activity'))
              ? getPropertyValue('activity').map((act: any) => `- ${formatActivityValue(act)}`).join('<br>')
              : formatArrayValue(getPropertyValue('activity')) || '-'}
          </td>
          <td>${getPropertyValue('otherActivityCategory') || '-'}</td>
          <td>${formatDate(getPropertyValue('dateImplementationStart'))}</td>
          <td>${formatDate(getPropertyValue('dateImplementationEnd'))}</td>
        </tr>
      </tbody>
    </table>

    <!-- Signature Section -->
    <table class="signature-section">
      <tr>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Prepared by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${userFullName}</div>
          <div style="font-size: 10px;">Barangay GAD Focal</div>
        </td>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Approved by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">Punong Barangay</div>
        </td>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
      <tr>
        <td colspan="2" style="width: 66%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Verified and Endorsed by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.plan?.status === 'Approved' && props.plan?.admin_approver ? props.plan.admin_approver : '_________________'}</div>
          <div style="font-size: 10px;">Chairperson, GFPS TWG/secretary</div>
        </td>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date Verified:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.plan?.status === 'Approved' && props.plan?.approval_date ? formatApprovalDate(props.plan.approval_date) : '_________________'}</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
    </table>
  `;
}

function revised() {
  if (!props.plan || !props.plan.id) {
    showModal('Error', 'Cannot revise: Invalid plan data', 'error');
    return;
  }

  showConfirmModal(
    'Request Revision',
    'Are you sure you want to request revision for this budget plan? The submitter will be notified to make changes.',
    () => {
      performRevision();
    }
  );
}

function performRevision() {
    // Get the current user's full name
    const currentUser = page.props.auth.user;

    // Build the full name properly
    let fullName = '';

    if (currentUser.first_name) {
      fullName += currentUser.first_name;
    } else if (currentUser.name) {
      fullName += currentUser.name;
    }

    if (currentUser.middle_name) {
      fullName += ' ' + currentUser.middle_name.charAt(0) + '.';
    }

    if (currentUser.last_name) {
      fullName += ' ' + currentUser.last_name;
    }

    if (currentUser.suffix) {
      fullName += ', ' + currentUser.suffix;
    }

    // If we couldn't build a name from the parts, fall back to the name field
    if (!fullName.trim() && currentUser.name) {
      fullName = currentUser.name;
    }

    // Convert to uppercase
    fullName = fullName.trim().toUpperCase();

    // Create a FormData instance
    const formData = new FormData();

    // Add the plan ID and status
    formData.append('id', props.plan.id);
    formData.append('status', 'Revision');
    formData.append('revised_by', fullName);
    formData.append('revision_date', new Date().toISOString());

    // Set up axios config
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    // Update the plan status using PUT request
    axios.post(`/api/budget-plans/${props.plan.id}?_method=PUT`, formData, config)
      .then(response => {
        console.log('Revision response:', response.data);
        if (response.data.success) {
          // Update the local plan object to ensure UI reflects changes
          if (props.plan) {
            props.plan.status = 'Revision';
            props.plan.revised_by = fullName;
            props.plan.revision_date = new Date().toISOString();
          }
          showModal('Success', 'Budget plan marked for revision successfully!', 'success');
          setTimeout(() => {
            emit('close'); // Close the modal after showing success
          }, 1500);
        } else {
          throw new Error(response.data.message || 'Unknown error');
        }
      })
      .catch(error => {
        console.error('Error marking budget plan for revision:', error);
        showModal('Error', 'Failed to mark budget plan for revision. Please try again.', 'error');
      });
}
function disapproved() {
  if (!props.plan || !props.plan.id) {
    showModal('Error', 'Cannot disapprove: Invalid plan data', 'error');
    return;
  }

  showConfirmModal(
    'Disapprove Budget Plan',
    'Are you sure you want to disapprove this budget plan? This action cannot be undone.',
    () => {
      // Build full name of current user
      const user = page.props.auth.user;
      let fullName = '';
      if (user.first_name) fullName += user.first_name;
      else if (user.name) fullName += user.name;
      if (user.middle_name) fullName += ' ' + user.middle_name.charAt(0) + '.';
      if (user.last_name) fullName += ' ' + user.last_name;
      if (user.suffix) fullName += ', ' + user.suffix;
      if (!fullName.trim() && user.name) fullName = user.name;
      fullName = fullName.trim().toUpperCase();

      // Prepare form data
      const formData = new FormData();
      formData.append('id', props.plan.id);
      formData.append('status', 'Disapproved');
      formData.append('disapproved_by', fullName);
      formData.append('disapproval_date', new Date().toISOString());

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Requested-With': 'XMLHttpRequest'
        }
      };

      axios.post(`/api/budget-plans/${props.plan.id}?_method=PUT`, formData, config)
        .then(response => {
          if (response.data.success) {
            props.plan.status = 'Disapproved';
            props.plan.disapproved_by = fullName;
            props.plan.disapproval_date = new Date().toISOString();
            showModal('Success', 'Budget plan disapproved successfully!', 'success');
            setTimeout(() => {
              emit('close');
            }, 1500);
          } else {
            throw new Error(response.data.message || 'Unknown error');
          }
        })
        .catch(error => {
          showModal('Error', 'Failed to disapprove budget plan. Please try again.', 'error');
        });
    }
  );
}
  

function formatDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB');
}

function formatApprovalDate(dateString: string) {
  if (!dateString) return 'DD/MM/YEAR';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}



// Improved getPropertyValue function to handle both camelCase and snake_case
function getPropertyValue(propertyName: string) {
  if (!props.plan) return '-';

  // Special case for focused field
  if (propertyName === 'focused') {
    return focusedType.value;
  }

  // Special case for title/description
  if (propertyName === 'titleDesc') {
    return titleDescription.value;
  }

  // Try camelCase first
  if (props.plan[propertyName] !== undefined && props.plan[propertyName] !== null) {
    return props.plan[propertyName];
  }

  // Try snake_case version
  const snakeCase = propertyName.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`);
  if (props.plan[snakeCase] !== undefined && props.plan[snakeCase] !== null) {
    return props.plan[snakeCase];
  }

  // Special case for specific fields
  if (propertyName === 'performanceIndicator' && props.plan.performanceTarget) {
    return props.plan.performanceTarget;
  }
  if (propertyName === 'performanceIndicator' && props.plan.performance_target) {
    return props.plan.performance_target;
  }

  // Special case for responsible office
  if (propertyName === 'responsibleOffice' && props.plan.leadOffice) {
    return props.plan.leadOffice;
  }
  if (propertyName === 'responsibleOffice' && props.plan.lead_office) {
    return props.plan.lead_office;
  }

  // Special case for supporting statistics data
  if (propertyName === 'supportingStats' && props.plan.supportingData) {
    return props.plan.supportingData;
  }

  // Special case for source of supporting statistics data
  if (propertyName === 'sourceStats' && props.plan.dataSource) {
    return props.plan.dataSource;
  }

  return '-';
}


// Helper function to format array values
function formatArrayValue(value: any) {
  if (!value) return '-';

  if (Array.isArray(value)) {
    // Join array elements with commas and capitalize first letter of each item
    return value.map(item => {
      const str = String(item);
      return str.charAt(0).toUpperCase() + str.slice(1);
    }).join(', ');
  }

  // If it's a string that looks like JSON array, try to parse and format it
  if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.map(item => {
          const str = String(item);
          return str.charAt(0).toUpperCase() + str.slice(1);
        }).join(', ');
      }
    } catch (e) {
      // If parsing fails, just clean the string
      return value.replace(/[\[\]"\\]/g, '');
    }
  }

  // For regular strings
  return String(value);
}

// Helper function specifically for activity values to handle escaped slashes
function formatActivityValue(value: any) {
  if (!value) return '-';

  // Replace escaped slashes and other special characters
  return String(value).replace(/\\+\//g, '/');
}

function truncateFilename(filename: string, maxLength = 20) {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;

  const extension = filename.includes('.') ? filename.split('.').pop() || '' : '';
  const nameWithoutExt = filename.includes('.') ? filename.substring(0, filename.lastIndexOf('.')) : filename;

  // Keep the extension and truncate the name part
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 3) + '...';
  return extension ? `${truncatedName}.${extension}` : truncatedName;
}

function fetchSubmitterData() {
  if (!props.plan || !props.plan.user_id) return;

  isLoadingSubmitter.value = true;

  // Try to determine the correct API endpoint
  // First check if we can use the barangay-users endpoint
  axios.get('/api/barangay-users')
    .then(response => {
      console.log("Barangay users response:", response.data);

      // The response has admin and barangay user lists
      let user = null;

      // Check in barangay users list
      if (response.data.barangay && Array.isArray(response.data.barangay.list)) {
        user = response.data.barangay.list.find((u: any) => u.id === props.plan.user_id);
      }

      // If not found, check in admin users list
      if (!user && response.data.admin && Array.isArray(response.data.admin.list)) {
        user = response.data.admin.list.find((u: any) => u.id === props.plan.user_id);
      }

      if (user) {
        let fullName = user.name || '';

        if (user.middle_name) {
          fullName += ' ' + user.middle_name.charAt(0) + '.';
        }

        if (user.last_name) {
          fullName += ' ' + user.last_name;
        }

        if (user.suffix) {
          fullName += ', ' + user.suffix;
        }

        submitterFullName.value = fullName.toUpperCase();
      } else {
        // If user not found in either list, fall back to what we have
        submitterFullName.value = props.plan.user_name ? props.plan.user_name.toUpperCase() : 'Unknown User';
      }
    })
    .catch(error => {
      console.error('Error fetching barangay users:', error);
      // Fall back to user_name if available
      submitterFullName.value = props.plan.user_name ? props.plan.user_name.toUpperCase() : 'Unknown User';
    })
    .finally(() => {
      isLoadingSubmitter.value = false;
    });
}

onMounted(() => {
  console.log('Admin budget plan modal mounted');
  console.log('Current admin user:', page.props.auth.user);

  // Initialize approver name based on plan status
  if (props.plan && props.plan.status === 'Approved' && props.plan.approver_name) {
    approverName.value = props.plan.approver_name;
  } else if (props.plan && props.plan.status === 'Pending') {
    // For pending plans, don't show an approver name
    approverName.value = '';
  }
});

watch(() => props.plan, (newPlan) => {
  if (newPlan) {
    fetchSubmitterData();
  } else {
    submitterFullName.value = '';
  }
});

function approvebudget() {
  if (!props.plan || !props.plan.id) {
    showModal('Error', 'Cannot approve: Invalid plan data', 'error');
    return;
  }

  showConfirmModal(
    'Approve Budget Plan',
    'Are you sure you want to approve this budget plan? This action cannot be undone.',
    () => {
      performApproval();
    }
  );
}

function performApproval() {
    // Get the current user's full name
    const currentUser = page.props.auth.user;
    const isPunongBarangay = currentUser.role === 'punong_barangay';
    const isAdmin = currentUser.role === 'admin';

    // Build the full name properly
    let fullName = '';

    if (currentUser.first_name) {
      fullName += currentUser.first_name;
    } else if (currentUser.name) {
      fullName += currentUser.name;
    }

    if (currentUser.middle_name) {
      fullName += ' ' + currentUser.middle_name.charAt(0) + '.';
    }

    if (currentUser.last_name) {
      fullName += ' ' + currentUser.last_name;
    }

    if (currentUser.suffix) {
      fullName += ', ' + currentUser.suffix;
    }

    // If we couldn't build a name from the parts, fall back to the name field
    if (!fullName.trim() && currentUser.name) {
      fullName = currentUser.name;
    }

    // Convert to uppercase
    fullName = fullName.trim().toUpperCase();

    // Create a FormData instance
    const formData = new FormData();

    // Add the plan ID and status
    formData.append('id', props.plan.id);
    formData.append('status', 'Approved');

    // Only set approver_name if this is a Punong Barangay
    if (isPunongBarangay) {
      formData.append('approver_name', fullName);
    }

    // Set admin_approver if this is an admin
    if (isAdmin) {
      formData.append('admin_approver', fullName);
    }

    // Add approval date (current date) for both Punong Barangay and Admin
    const approvalDate = new Date().toISOString();
    formData.append('approval_date', approvalDate);

    if (isPunongBarangay) {
      formData.append('approved_by_punong_barangay', 'true');
    }

    // Log what we're sending
    console.log('Sending approval data:', {
      id: props.plan.id,
      status: 'Approved',
      approver_name: isPunongBarangay ? fullName : undefined,
      admin_approver: isAdmin ? fullName : undefined,
      approval_date: approvalDate,
      approved_by_punong_barangay: isPunongBarangay ? 'true' : undefined
    });

    // Set up axios config
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    // Update the plan status using PUT request
    axios.post(`/api/budget-plans/${props.plan.id}?_method=PUT`, formData, config)
      .then(response => {
        console.log('Approval response:', response.data);
        if (response.data.success) {
          // Update the local plan object to ensure UI reflects changes
          if (props.plan) {
            props.plan.status = 'Approved';
            props.plan.approval_date = approvalDate;
            if (isPunongBarangay) {
              props.plan.approver_name = fullName;
              props.plan.approved_by_punong_barangay = true;
            }
            if (isAdmin) {
              props.plan.admin_approver = fullName;
            }
          }
          showModal('Success', 'Budget plan approved successfully!', 'success');
          setTimeout(() => {
            emit('close'); // Close the modal after showing success
          }, 1500);
        } else {
          throw new Error(response.data.message || 'Unknown error');
        }
      })
      .catch(error => {
        console.error('Error approving budget plan:', error);
        showModal('Error', 'Failed to approve budget plan. Please try again.', 'error');
      });
}

// Helper function to get manual total (MOOE + PS + CO)
function getManualTotal(plan: any): number {
  return (Number(plan.mooe || 0) + Number(plan.ps || 0) + Number(plan.co || 0));
}

// Helper function to get combined budget (LP allocation + manual total)
function getCombinedTotal(plan: any): number {
  const lpAllocation = Number(plan.lp_allocation || 0);
  const manualTotal = getManualTotal(plan);

  // If total_budget is set (from backend calculation), use it
  // Otherwise, calculate as LP allocation + manual total
  return plan.total_budget || (lpAllocation + manualTotal);
}

// --- Inline Comment Feature State ---
const commentMode = ref(false); // true when in comment mode
const activeCommentCell = ref<string | null>(null); // field name being commented
const cellComments = ref<Record<string, { text: string, color: string, id?: number, author_id?: number }>>({});
const commentColors = [
  { name: 'White', value: '#fff' },
  { name: 'Blue', value: '#bfdbfe' },
  { name: 'Green', value: '#bbf7d0' },
  { name: 'Yellow', value: '#fef9c3' },
  { name: 'Red', value: '#fecaca' },
];
const defaultColor = '#fff';

// Fetch all comments for the plan
async function fetchComments() {
  if (!props.plan?.id) return;
  try {
    const res = await axios.get(`/api/comments/${props.plan.id}`);
    const map: Record<string, { text: string, color: string, id?: number, author_id?: number }> = {};
    for (const c of res.data) {
      map[c.column_key] = { text: c.text, color: c.color || defaultColor, id: c.id, author_id: c.author_id };
    }
    cellComments.value = map;
  } catch (e) {
    console.error('Failed to fetch comments', e);
  }
}

onMounted(fetchComments);
watch(() => props.plan?.id, fetchComments);

function toggleCommentMode() {
  if (!commentsEditable.value) return;
  commentMode.value = !commentMode.value;
  if (!commentMode.value) {
    activeCommentCell.value = null;
  }
}

function handleCellClick(field: string) {
  if (commentMode.value) {
    activeCommentCell.value = field;
    // If no comment object exists, initialize
    if (!cellComments.value[field]) {
      cellComments.value[field] = { text: '', color: defaultColor };
    }
  }
}

async function saveCellComment(field: string, value: string) {
  if (!props.plan?.id || !commentsEditable.value) return;
  const payload = {
    plan_id: props.plan.id,
    column_key: field,
    row_index: 0, // update if you support multiple rows
    text: value,
    color: cellComments.value[field]?.color || defaultColor,
  };
  try {
    const res = await axios.post('/api/comments', payload);
    cellComments.value[field] = { ...cellComments.value[field], text: value, color: payload.color, id: res.data.id, author_id: res.data.author_id };
    activeCommentCell.value = null;
  } catch (e) {
    alert('Failed to save comment');
  }
}

function setCellCommentColor(field: string, color: string) {
  if (!cellComments.value[field]) {
    cellComments.value[field] = { text: '', color: defaultColor };
  }
  // Reassign the object to trigger reactivity
  cellComments.value[field] = { ...cellComments.value[field], color };
}
function getCellComment(field: string) {
  const c = cellComments.value[field];
  if (!c) return '';
  if (typeof c === 'string') return c; // backward compatibility
  return c.text;
}
function getCellColor(field: string) {
  const c = cellComments.value[field];
  if (!c) return defaultColor;
  if (typeof c === 'string') return defaultColor;
  return c.color || defaultColor;
}

const hoveredCell = ref<string | null>(null);

const visibleCommentCell = ref<string | null>(null);

function toggleVisibleCommentCell(field: string) {
  if (visibleCommentCell.value === field) {
    visibleCommentCell.value = null;
  } else {
    visibleCommentCell.value = field;
  }
}

// Hide popover when clicking outside
function handleDocumentClick(e: MouseEvent) {
  // Only hide if a popover is open and not in comment mode
  if (!commentMode.value && visibleCommentCell.value) {
    // Check if the click is inside a popover, on an eye icon, or on the badge
    const popover = document.querySelector('.popover-comment-visible');
    const eyes = document.querySelectorAll('.icon-eye');
    const badges = document.querySelectorAll('.view-comment-badge');
    let clickedEye = false;
    let clickedBadge = false;
    eyes.forEach(eye => {
      if (eye.contains(e.target as Node)) clickedEye = true;
    });
    badges.forEach(badge => {
      if (badge.contains(e.target as Node)) clickedBadge = true;
    });
    if (popover && !popover.contains(e.target as Node) && !clickedEye && !clickedBadge) {
      visibleCommentCell.value = null;
    }
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleDocumentClick);
});
onUnmounted(() => {
  document.removeEventListener('mousedown', handleDocumentClick);
});

function onCellClick(field: string) {
  if (commentMode.value) {
    handleCellClick(field);
  }
}

// Add a computed property to check if comments are editable
const commentsEditable = computed(() => {
  return !['Approved', 'Disapproved', 'Revision'].includes(props.plan?.status);
});
</script>

<style scoped>
.bg-pink-200 { background-color: #e9b7e7 !important; }
.bg-yellow-200 { background-color: #fff9c4 !important; }
.bg-green-100 { background-color: #d1fae5 !important; }
.comment-plus-cursor {
  cursor: copy !important;
}
.popover-comment {
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%);
  background: #fff;
  color: #065f46;
  border: 1px solid #10b981;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.85em;
  white-space: pre-line;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(16,185,129,0.15);
  margin-top: 0.25rem;
  min-width: 120px;
  max-width: 250px;
  pointer-events: none;
}
.popover-edit {
  pointer-events: auto;
  background: #f0fdfa;
  border: 2px solid #10b981;
  color: #065f46;
  box-shadow: 0 4px 16px rgba(16,185,129,0.18);
}
.cell-has-comment {
  /* background: #fffde7 !important; */
  position: relative;
}
.cell-comment-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  z-index: 2;
}
.icon-eye {
  width: 1.2em;
  height: 1.2em;
  color: black;
  opacity: 0.95;
}
.color-swatch {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
  border: 1px solid #ccc;
  margin-right: 2px;
  transition: border 0.2s;
}
.color-swatch:hover {
  border: 2px solid #2563eb;
}
.popover-comment-large {
  font-size: 1.15em;
  font-weight: bold;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  padding: 1em 1.2em;
  margin-top: 0.5em;
  background: #f8fafc;
  color: #222;
  border: 2px solid #60a5fa;
  border-radius: 0.5em;
  text-align: left;
  word-break: break-word;
}
.cell-comment-indicator-badge-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5em;
}
.cell-comment-indicator-badge {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.75em;
  padding: 0.15em 0.7em 0.15em 0.4em;
  font-size: 0.85em;
  font-weight: 500;
  color: #222;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
  cursor: pointer;
  transition: background 0.2s, border 0.2s, box-shadow 0.2s;
  outline: none;
}
.cell-comment-indicator-badge:hover, .cell-comment-indicator-badge:focus {
  background: #e0e7ef;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px #60a5fa33;
}
.badge-text {
  font-size: 0.92em;
  font-weight: 500;
  color: #222;
  white-space: nowrap;
}
.edit-comment-btn {
  background: transparent;
  border: none;
  padding: 0.1em 0.3em;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: background 0.2s;
}
.edit-comment-btn:hover {
  background: #e0e7ef;
  border-radius: 0.5em;
}
.icon-edit {
  width: 1.1em;
  height: 1.1em;
  color: #2563eb;
  opacity: 0.95;
}
.edit-comment-badge {
  /* No overrides, just use .cell-comment-indicator-badge styles */
}
</style>