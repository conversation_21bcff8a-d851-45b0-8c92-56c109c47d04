<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('budget_plans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('barangay_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('focused')->nullable();
            $table->string('gender_issue')->nullable();
            $table->text('title_desc')->nullable();
            $table->text('supporting_stats')->nullable();
            $table->text('source_stats')->nullable();
            $table->json('ppa_si')->nullable();
            $table->text('gad_objective')->nullable();
            $table->text('lgu_program')->nullable();
            $table->json('activity')->nullable();
            $table->string('other_activity_category')->nullable();
            $table->text('gad_activity')->nullable();
            $table->date('date_implementation_start')->nullable();
            $table->date('date_implementation_end')->nullable();
            $table->text('performance_target')->nullable();
            $table->decimal('mooe', 15, 2)->default(0);
            $table->decimal('ps', 15, 2)->default(0);
            $table->decimal('co', 15, 2)->default(0);
            $table->decimal('total_budget', 15, 2)->default(0);
            $table->string('lead_office')->nullable();
            $table->json('items')->nullable();
            $table->text('remarks')->nullable();
            $table->string('status')->default('Pending');
            $table->boolean('is_draft')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('budget_plans');
    }
};

