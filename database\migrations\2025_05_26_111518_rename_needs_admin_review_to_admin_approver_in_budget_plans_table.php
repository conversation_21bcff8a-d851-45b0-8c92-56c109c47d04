<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // For SQLite, we need to rename the column differently
            if (Schema::hasColumn('budget_plans', 'needs_admin_review')) {
                // Add the new column
                $table->string('admin_approver')->nullable()->after('approver_name');
            }
        });

        // Copy data and drop old column
        if (Schema::hasColumn('budget_plans', 'needs_admin_review')) {
            // For SQLite, we'll handle this with raw SQL
            DB::statement('UPDATE budget_plans SET admin_approver = CASE WHEN needs_admin_review = 1 THEN "pending_admin_review" ELSE NULL END');

            Schema::table('budget_plans', function (Blueprint $table) {
                $table->dropColumn('needs_admin_review');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // Add back the old column
            $table->boolean('needs_admin_review')->default(false)->after('approved_by_punong_barangay');
        });

        // Copy data and drop new column
        if (Schema::hasColumn('budget_plans', 'admin_approver')) {
            // For SQLite, we'll handle this with raw SQL
            DB::statement('UPDATE budget_plans SET needs_admin_review = CASE WHEN admin_approver IS NOT NULL THEN 1 ELSE 0 END');

            Schema::table('budget_plans', function (Blueprint $table) {
                $table->dropColumn('admin_approver');
            });
        }
    }
};
