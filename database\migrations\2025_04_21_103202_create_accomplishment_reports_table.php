<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accomplishment_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('barangay_id')->nullable()->constrained('barangays')->onDelete('set null');
            $table->string('barangay')->nullable();
            $table->string('title');
            $table->text('genderIssue')->nullable();
            $table->text('gadMandate')->nullable();
            $table->string('focused')->nullable();
            $table->text('gadObjective')->nullable();
            $table->text('lguPpa')->nullable();
            $table->text('gadActivity')->nullable();
            $table->text('performanceIndicator')->nullable();
            $table->text('actualResults')->nullable();
            $table->decimal('approvedBudget', 15, 2)->nullable();
            $table->decimal('actualCost', 15, 2)->nullable();
            $table->text('remarks')->nullable();
            $table->string('status')->default('pending');
            $table->date('dateSubmitted')->nullable();
            $table->text('supportingData')->nullable();
            $table->text('dataSource')->nullable();
            $table->text('ppa')->nullable();
            $table->text('otherActivity')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accomplishment_reports');
    }
};
