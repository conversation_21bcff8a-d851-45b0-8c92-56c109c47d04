<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/Register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'suffix' => 'nullable|string|max:10',
            'birthdate' => 'required|date',
            'gender' => 'required|string',
            'mobile_number' => 'required|string|max:20',
            'region' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'barangay' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'verification_code' => 'nullable|string|max:6',
            'role' => 'required|string|in:barangay,punong_barangay', // Add role validation
        ]);

        // First, find or create a barangay record
        $barangayId = DB::table('barangays')
            ->where('name', $request->barangay)
            ->where('region', $request->region)
            ->where('province', $request->province)
            ->where('city', $request->city)
            ->value('id');

        if (!$barangayId) {
            $barangayId = DB::table('barangays')->insertGetId([
                'name' => $request->barangay,
                'region' => $request->region,
                'province' => $request->province,
                'city' => $request->city,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $user = User::create([
            'name' => $request->name,
            'middle_name' => $request->middle_name,
            'last_name' => $request->last_name,
            'suffix' => $request->suffix,
            'birthdate' => $request->birthdate,
            'gender' => $request->gender,
            'mobile_number' => $request->mobile_number,
            'region' => $request->region,
            'province' => $request->province,
            'city' => $request->city,
            'barangay' => $request->barangay,
            'barangay_id' => $barangayId, // Set the barangay_id
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'verification_code' => $request->verification_code,
            'role' => $request->role, // Add this line to save the role
        ]);

        event(new Registered($user));

        // Flash a success message
        session()->flash('success', 'Your account has been successfully created! Your information has been saved to the database.');

        Auth::login($user);

        return redirect()->route('dashboard');
    }
}




