<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BudgetPlanController;
use App\Http\Controllers\AccomplishmentReportController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SubmissionController;
use App\Http\Controllers\BudgetSyncController;
use App\Http\Controllers\SystemNoticeController;
use App\Http\Controllers\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Apply the FilterBarangayData middleware to all budget plan routes
Route::middleware(['auth:sanctum', 'filter.barangay'])->group(function () {
    Route::apiResource('budget-plans', BudgetPlanController::class);
    Route::apiResource('accomplishment-reports', AccomplishmentReportController::class);
});

Route::middleware(['auth:sanctum'])->get('/barangay-users', [UserController::class, 'getBarangayUsers']);

// Add this route for fetching pending submissions
Route::get('/pending-submissions', [SubmissionController::class, 'getPendingSubmissions'])
    ->middleware('auth:sanctum');

// Budget sync routes for LP optimization
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/budget-sync', [BudgetSyncController::class, 'syncBudget']);
    Route::get('/budget-sync', [BudgetSyncController::class, 'getBudget']);
    Route::post('/budget-sync/recalculate', [BudgetSyncController::class, 'recalculateLpAllocations']);
});

// Notification routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::get('/notifications/unread-count', [NotificationController::class, 'unreadCount']);
    Route::put('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::put('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
});

// System notice routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/system-notices', [SystemNoticeController::class, 'createNotice']);
    Route::post('/system-notices/barangay', [SystemNoticeController::class, 'createBarangayNotice']);
    Route::get('/system-notices', [SystemNoticeController::class, 'getSystemNotices']);
    Route::delete('/system-notices/{id}', [SystemNoticeController::class, 'deleteNotice']);
});

// User management routes for admin
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/admin/users', [UserController::class, 'createUser']);
    Route::put('/admin/users/{id}', [UserController::class, 'updateUser']);
});











