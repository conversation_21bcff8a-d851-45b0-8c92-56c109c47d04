<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { type NavItem } from '@/types';
import { usePage } from '@inertiajs/vue3';
import { settingsActiveTab } from '@/settingsTabState';
import { User, Lock, Palette, Settings as SettingsIcon } from 'lucide-vue-next';
import AppShell from '@/components/AppShell.vue';
import AppContent from '@/components/AppContent.vue';
import AppSidebar from '@/components/AppSidebar.vue';

const sidebarNavItems: (NavItem & { icon?: any; spaTab?: string })[] = [
    {
        title: 'General',
        href: '/settings',
        icon: SettingsIcon,
        spaTab: 'general',
    },
    {
        title: 'Profile',
        href: '/settings/profile',
        icon: User,
        spaTab: 'profile',
    },
    {
        title: 'Password',
        href: '/settings/password',
        icon: Lock,
        spaTab: 'password',
    },
    {
        title: 'Appearance',
        href: '/settings/appearance',
        icon: Palette,
        spaTab: 'appearance',
    },
];

const page = usePage();
const currentPath = window.location.pathname;

const handleTabClick = (spaTab: string) => {
    settingsActiveTab.value = spaTab as any;
};
</script>

<template>
    <AppShell variant="sidebar">
        <AppSidebar />
        <AppContent variant="sidebar">
            <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
                <!-- Settings Header -->
                <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                    <div class="px-4 sm:px-6 lg:px-8">
                        <div class="py-6">
                            <Heading title="Settings" description="Manage your account settings and preferences" />
                        </div>
                    </div>
                </div>

                <div class="px-4 sm:px-6 lg:px-8 py-8">
                    <div class="flex flex-col lg:flex-row gap-8">
                        <!-- Settings Sidebar -->
                        <aside class="lg:w-64 flex-shrink-0">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                                <nav class="space-y-1">
                                    <Button
                                        v-for="item in sidebarNavItems"
                                        :key="item.href"
                                        variant="ghost"
                                        :class="[
                                            'w-full justify-start h-12 px-4 text-left',
                                            { 
                                                'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800': 
                                                settingsActiveTab === item.spaTab
                                            }
                                        ]"
                                        @click="item.spaTab && handleTabClick(item.spaTab)"
                                    >
                                        <component v-if="item.icon" :is="item.icon" class="h-5 w-5 mr-3" />
                                        <span class="font-medium">{{ item.title }}</span>
                                    </Button>
                                </nav>
                            </div>
                        </aside>

                        <!-- Settings Content -->
                        <div class="flex-1 min-w-0">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                                <slot />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppContent>
    </AppShell>
</template>
