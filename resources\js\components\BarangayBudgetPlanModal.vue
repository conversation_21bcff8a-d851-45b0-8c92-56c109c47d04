<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm p-1 sm:p-4">
    <div class="bg-white shadow-lg w-full max-w-7xl mx-auto overflow-hidden border border-gray-400 rounded-xl max-h-[98vh] sm:max-h-[95vh] flex flex-col">
      <!-- Title -->
      <div class="text-center py-2 sm:py-4 relative px-2 sm:px-6 flex-shrink-0">
        <h1 class="text-xs sm:text-lg lg:text-xl font-bold text-gray-700 pr-12 sm:pr-20 leading-tight">BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) PLAN FY {{ currentYear }}</h1>
        <button @click="$emit('close')" class="absolute right-1 sm:right-4 top-1/2 -translate-y-1/2 px-1.5 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-md shadow-sm hover:shadow-md hover:from-red-600 hover:to-red-700 transition-all duration-200 text-xs sm:text-sm font-medium flex items-center gap-1 sm:gap-1.5">
          <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span class="hidden sm:inline">Close</span>
        </button>
      </div>

      <!-- Status Badges -->
      <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between px-3 sm:px-4 pt-3 pb-2 gap-3 sm:gap-0 flex-shrink-0">
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 w-full sm:w-auto">
          <div class="flex items-center gap-2 flex-wrap">
            <span v-if="plan.status === 'Draft'" class="bg-gray-200 border border-gray-400 text-xs px-2 py-1 font-medium rounded">Draft</span>
            <span v-if="plan.status === 'Pending'" class="bg-yellow-200 border border-yellow-300 text-xs px-2 py-1 font-medium rounded">Pending</span>
            <span v-if="plan.status === 'Approved'" class="bg-green-200 border border-green-300 text-xs px-2 py-1 font-medium rounded">Approved</span>
            <span v-if="plan.status === 'Rejected' || plan.status === 'Revision'" class="bg-pink-200 border border-pink-300 text-xs px-2 py-1 font-medium rounded">Needs Revision</span>
          </div>
          <span class="text-xs text-gray-500 hidden sm:block">{{ getStatusMessage(plan.status) }}</span>
          <div class="flex flex-wrap gap-1 sm:gap-2">
            <button @click="exportToExcel" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer rounded hover:bg-gray-50">
              <span class="text-xs">📈</span>
              <span class="hidden sm:inline">Export to Excel</span>
              <span class="sm:hidden">Export</span>
            </button>
            <button @click="openComments" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer rounded hover:bg-gray-50">
              <span class="text-xs">💬</span>
              <span class="hidden sm:inline">Comments</span>
              <span class="sm:hidden">Comments</span>
            </button>
            <button @click="printpreview" class="bg-white border border-gray-400 px-2 sm:px-3 py-1 text-xs font-semibold flex items-center gap-1 cursor-pointer rounded hover:bg-gray-50">
              <span class="text-xs">🖨️</span>
              <span class="hidden sm:inline">Print Preview</span>
              <span class="sm:hidden">Print</span>
            </button>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button
            v-if="showOtherDetails"
            @click="toggleDetailsView"
            class="flex items-center space-x-1.5 text-xs sm:text-sm text-gray-600 hover:text-gray-800 text-left cursor-pointer"
          >
            <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            <span>Back</span>
          </button>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="flex-1 overflow-auto">
        <!-- Mobile Card View - Main Plan -->
        <div v-if="!showOtherDetails" class="block md:hidden p-2 space-y-4">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
            <div class="space-y-3">
              <div class="border-b pb-2">
                <h3 class="font-semibold text-sm text-gray-800 mb-1">{{ focusedType }}</h3>
                <p class="text-xs text-gray-600">
                  <span v-if="plan.gadMandate">GAD Mandate</span>
                  <span v-else-if="plan.genderIssue">Gender Issue</span>
                  <span v-else>{{ issueOrMandate.value }}</span>
                </p>
              </div>

              <div class="grid grid-cols-1 gap-3 text-xs">
                <div>
                  <span class="font-semibold text-gray-700">Gender Issue/GAD Mandate:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ titleDescription }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Objective:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('gadObjective') }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">LGU Program/Project:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('lguProgram') }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">GAD Activity:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('gadActivity') }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Performance Indicator:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('performanceIndicator') }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Responsible Office:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('responsibleOffice') }}</p>
                </div>

                <!-- Budget Section -->
                <div class="border-t pt-3">
                  <span class="font-semibold text-gray-700 block mb-2">GAD Budget:</span>
                  <div class="grid grid-cols-3 gap-2 text-xs">
                    <div class="text-center">
                      <span class="block font-medium text-gray-600">MOOE</span>
                      <span class="block font-bold">{{ formatNumber(getPropertyValue('mooe')) || '-' }}</span>
                    </div>
                    <div class="text-center">
                      <span class="block font-medium text-gray-600">PS</span>
                      <span class="block font-bold">{{ formatNumber(getPropertyValue('ps')) || '-' }}</span>
                    </div>
                    <div class="text-center">
                      <span class="block font-medium text-gray-600">CO</span>
                      <span class="block font-bold">{{ formatNumber(getPropertyValue('co')) || '-' }}</span>
                    </div>
                  </div>
                </div>

                <!-- Sub-total Section -->
                <div class="bg-emerald-100 p-3 rounded">
                  <div class="flex justify-between items-center">
                    <span class="font-bold text-sm">Sub-total</span>
                  </div>
                  <div class="grid grid-cols-3 gap-2 mt-2 text-xs">
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.mooe && plan.mooe > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.mooe || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.ps && plan.ps > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.ps || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                    <div class="text-center" v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.co && plan.co > 0">
                      <span class="font-bold">{{ Math.floor(Number(plan.co || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Grand Total Section -->
                <div class="bg-emerald-300 p-3 rounded">
                  <div class="flex justify-between items-center">
                    <span class="font-bold text-sm">GRAND TOTAL (A+B+C)</span>
                    <span class="font-bold text-lg">{{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}</span>
                  </div>
                </div>

                <!-- Action Button -->
                <div class="border-t pt-3">
                  <button
                    @click="toggleDetailsView"
                    class="w-full flex items-center justify-center space-x-2 text-xs text-gray-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 p-2 rounded"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>View Other Details</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Card View - Other Details -->
        <div v-else class="block md:hidden p-2 space-y-4">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
            <div class="space-y-3">
              <div class="border-b pb-2">
                <h3 class="font-semibold text-sm text-gray-800 mb-1">Other Details</h3>
              </div>

              <div class="grid grid-cols-1 gap-3 text-xs">
                <div>
                  <span class="font-semibold text-gray-700">Supporting Statistics Data:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('supportingStats') || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Source of Supporting Statistics Data:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('sourceStats') || '-' }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">PPA/s:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ formatArrayValue(getPropertyValue('ppaSi')) }}</p>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Activity:</span>
                  <div class="mt-1 text-gray-600 break-words">
                    <div v-if="Array.isArray(getPropertyValue('activity'))">
                      <div v-for="(act, index) in getPropertyValue('activity')" :key="index" class="mb-1">
                        - {{ formatActivityValue(act) }}
                      </div>
                    </div>
                    <div v-else>{{ formatArrayValue(getPropertyValue('activity')) || '-' }}</div>
                  </div>
                </div>

                <div>
                  <span class="font-semibold text-gray-700">Other Activity Category:</span>
                  <p class="mt-1 text-gray-600 break-words">{{ getPropertyValue('otherActivityCategory') || '-' }}</p>
                </div>

                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <span class="font-semibold text-gray-700">Implementation Start:</span>
                    <p class="mt-1 text-gray-600">{{ formatDate(getPropertyValue('dateImplementationStart')) }}</p>
                  </div>
                  <div>
                    <span class="font-semibold text-gray-700">Implementation End:</span>
                    <p class="mt-1 text-gray-600">{{ formatDate(getPropertyValue('dateImplementationEnd')) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>



        <!-- Desktop Table View -->
        <div class="hidden md:block p-2 sm:p-4">
          <div class="overflow-x-auto">
            <table v-if="!showOtherDetails" class="w-full border border-gray-400 text-xs bg-white min-w-[800px]">
            <thead>
              <tr class="bg-gray-100">
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Gender Issue or GAD Mandate</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Objective</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Relevant GAD Program or Project</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Activity</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Performance Indicator and Target</th>
                <th colspan="3" class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">GAD Budget (6)</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">Lead or Responsible Office</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-white text-xs"></th>
              </tr>
              <tr>
                <th colspan="5" class="border border-gray-400 font-bold text-left px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">{{ focusedType }}</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">MOOE</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">PS</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 bg-gray-100 text-xs">CO</th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>

              </tr>
              <tr>
                <td colspan="5" class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 font-semibold bg-gray-100 text-xs">
                  <span v-if="plan.gadMandate">GAD Mandate</span>
                  <span v-else-if="plan.genderIssue">Gender Issue</span>
                  <!-- <span v-else> <td>{{ issueOrMandate.value }} </td></span> -->
                </td>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>
                <th class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ titleDescription }}</div>
                  <div v-if="getCellComment('titleDesc')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'titleDesc' ? null : 'titleDesc'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'titleDesc'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('titleDesc') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ getPropertyValue('gadObjective') }}</div>
                  <div v-if="getCellComment('gadObjective')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'gadObjective' ? null : 'gadObjective'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'gadObjective'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('gadObjective') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ getPropertyValue('lguProgram') }}</div>
                  <div v-if="getCellComment('lguProgram')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'lguProgram' ? null : 'lguProgram'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'lguProgram'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('lguProgram') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ getPropertyValue('gadActivity') }}</div>
                  <div v-if="getCellComment('gadActivity')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'gadActivity' ? null : 'gadActivity'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'gadActivity'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('gadActivity') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ getPropertyValue('performanceIndicator') }}</div>
                  <div v-if="getCellComment('performanceIndicator')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'performanceIndicator' ? null : 'performanceIndicator'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'performanceIndicator'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('performanceIndicator') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right text-xs">
                  <div>{{ formatNumber(getPropertyValue('mooe')) }}</div>
                  <div v-if="getCellComment('mooe')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'mooe' ? null : 'mooe'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'mooe'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('mooe') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right text-xs">
                  <div>{{ formatNumber(getPropertyValue('ps')) }}</div>
                  <div v-if="getCellComment('ps')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'ps' ? null : 'ps'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'ps'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('ps') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right text-xs">
                  <div>{{ formatNumber(getPropertyValue('co')) }}</div>
                  <div v-if="getCellComment('co')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'co' ? null : 'co'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'co'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('co') }}
                    </span>
                  </div>
                </td>
                <td class="relative border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div>{{ getPropertyValue('responsibleOffice') }}</div>
                  <div v-if="getCellComment('responsibleOffice')" class="flex flex-col items-center mt-1 relative">
                    <span class="cell-comment-indicator-badge view-comment-badge" @click.stop="visibleCommentCell = visibleCommentCell === 'responsibleOffice' ? null : 'responsibleOffice'" tabindex="0">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon-eye" fill="none" viewBox="0 0 24 24" stroke="black" stroke-width="2" style="width: 1.2em; height: 1.2em; margin-right: 0.3em;">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="badge-text">View Comment</span>
                    </span>
                    <span v-if="visibleCommentCell === 'responsibleOffice'" class="popover-comment popover-comment-large popover-comment-visible" style="top: 2.5em; left: 50%; transform: translateX(-50%);">
                      {{ getCellComment('responsibleOffice') }}
                    </span>
                  </div>
                </td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs">
                  <div class="flex flex-col space-y-1">
                    <!-- Other details button -->
                    <button
                      @click="toggleDetailsView"
                      class="flex items-center space-x-1 text-xs sm:text-sm text-gray-600 hover:text-gray-800 text-left cursor-pointer whitespace-nowrap"
                    >
                      <svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span class="hidden sm:inline">{{ showOtherDetails ? 'Main table' : 'Other details' }}</span>
                      <span class="sm:hidden">Details</span>
                    </button>
                  </div>
                </td>
              </tr>
              <!-- Sub-total row -->
              <tr class="bg-emerald-100">
                <td class="text-left font-bold border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs" colspan="5">Sub-total</td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right font-bold text-xs">
                  <div v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.mooe && plan.mooe > 0" class="text-xs">
                  {{ Math.floor(Number(plan.mooe || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                  </div>
                </td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right font-bold text-xs">
                  <div v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.ps && plan.ps > 0" class="text-xs">
                  {{ Math.floor(Number(plan.ps || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                  </div>
                </td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right font-bold text-xs">
                  <div v-if="plan.lp_allocation && plan.lp_allocation > 0 && plan.co && plan.co > 0" class="text-xs">
                 {{ Math.floor(Number(plan.co || 0) + Number(plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                  </div>
                </td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
              </tr>

              <!-- Total row with LP included -->
              <tr class="bg-emerald-200 font-bold">
                <td class="text-left font-bold border-l border-t border-b border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs" colspan="5">
                  TOTAL (MOOE + PS + CO):
                </td>
                <td class="border-t border-b border-l border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border-t border-b border-gray-400 px-1 sm:px-2 py-1 sm:py-2 font-bold text-center text-xs">
                  {{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border-t border-b border-r border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
              </tr>

              <!-- Grand Total row -->
              <tr class="bg-emerald-300 font-bold">
                <td colspan="5" class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-left text-xs">
                  GRAND TOTAL (A+B+C)
                  <div class="text-xs text-emerald-700 mt-1 font-normal">
                  </div>
                </td>
                <td class="border-t border-b border-l border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border-t border-b border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-right font-bold text-sm sm:text-lg">
                  {{ Math.floor(getCombinedTotal(plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}) }}
                </td>
                <td class="border-t border-b border-r border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
                <td class="border border-gray-400 px-1 sm:px-2 py-1 sm:py-2 text-xs"></td>
              </tr>
      <!-- Prepared/Approved by row -->
      <tr>
        <td colspan="3" class="border border-gray-400 p-3">
          <div class="font-semibold mb-1">Prepared by:</div>
          <div class="font-bold text-base mb-0">{{ getUserFullName() }}</div>
          <div class="text-xs mt-1">Barangay GAD Focal</div>
        </td>
        <td colspan="2" class="border border-gray-400 p-3">
          <div class="font-semibold mb-1">Approved by:</div>
          <div class="font-bold text-base mb-0">
            <br>
              <span class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">Punong Barangay</div>
        </td>
        <td colspan="5" class="border border-gray-400 p-3">
          <div class="font-semibold mb-1">Date:</div>
                    <div class="font-bold text-base mb-0">
            <br>
              <span class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">DD/MM/YEAR</div>

        </td>
      </tr>
      <tr>
        <td colspan="3" class="border-b border-t border-l border-gray-400 px-2 py-2">
  <div class="font-semibold mb-1">Verified and Endorsed by:</div>
    <div class="font-bold text-base mb-0">
            <br>
              <span v-if="plan.status === 'Approved' && plan.admin_approver" class="text-black">{{ plan.admin_approver }}</span>
              <span v-else class="text-gray-400"></span>

          </div>
    <div class="text-xs mt-1">Chairperson, GFPS TWG/secretary</div>
  </td>
  <td colspan="7" class="border-b border-t border-r border-gray-400 px-2 py-2">
    <div class="font-semibold mb-1">Date Verified:</div>
         <div class="font-bold text-base mb-0">
            <br>
              <span v-if="plan.status === 'Approved' && plan.approval_date" class="text-black">{{ formatApprovalDate(plan.approval_date) }}</span>
              <span v-else class="text-gray-400"></span>

          </div>
          <div class="text-xs mt-1">DD/MM/YEAR</div>
</td>
      </tr>
    </tbody>
  </table>

  <!-- Other Details Table - shown when showOtherDetails is true -->
  <table v-else class="w-full border border-gray-400 text-xs bg-white">
    <thead>
      <tr class="bg-gray-100">
        <th class="border border-gray-400 px-2 py-2">Supporting Statistics Data</th>
        <th class="border border-gray-400 px-2 py-2">Source of Supporting Statistics Data</th>
        <th class="border border-gray-400 px-2 py-2">PPA/s</th>
        <th class="border border-gray-400 px-2 py-2">Activity</th>
        <th class="border border-gray-400 px-2 py-2">Other Activity Category</th>
        <th class="border border-gray-400 px-2 py-2">Implementation Start</th>
        <th class="border border-gray-400 px-2 py-2">Implementation End</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('supportingStats') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('sourceStats') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatArrayValue(getPropertyValue('ppaSi')) }}</td>
        <td class="border border-gray-400 px-2 py-2">
          <div v-if="Array.isArray(getPropertyValue('activity'))">
            <div v-for="(act, index) in getPropertyValue('activity')" :key="index" class="mb-1">
              - {{ formatActivityValue(act) }}
            </div>
          </div>
          <div v-else>{{ formatArrayValue(getPropertyValue('activity')) || '-' }}</div>
        </td>
        <td class="border border-gray-400 px-2 py-2">{{ getPropertyValue('otherActivityCategory') || '-' }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatDate(getPropertyValue('dateImplementationStart')) }}</td>
        <td class="border border-gray-400 px-2 py-2">{{ formatDate(getPropertyValue('dateImplementationEnd')) }}</td>
      </tr>
    </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Signature Section -->
        <div class="block md:hidden p-2">
          <div class="bg-white border border-gray-300 rounded-lg p-4 shadow-sm space-y-4">
            <div class="grid grid-cols-1 gap-4 text-xs">
              <div class="border-b pb-3">
                <span class="font-semibold text-gray-700">Prepared by:</span>
                <p class="mt-1 font-bold text-sm">{{ getUserFullName() }}</p>
                <p class="text-xs text-gray-600">Barangay GAD Focal</p>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="font-semibold text-gray-700">Approved by:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Punong Barangay</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date:</span>
                  <p class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 border-t pt-3">
                <div>
                  <span class="font-semibold text-gray-700">Verified and Endorsed by:</span>
                  <p v-if="plan.status === 'Approved' && plan.admin_approver" class="mt-1 font-bold text-sm text-black">{{ plan.admin_approver }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">Chairperson, GFPS TWG/secretary</p>
                </div>
                <div>
                  <span class="font-semibold text-gray-700">Date Verified:</span>
                  <p v-if="plan.status === 'Approved' && plan.approval_date" class="mt-1 font-bold text-sm text-black">{{ formatApprovalDate(plan.approval_date) }}</p>
                  <p v-else class="mt-1 font-bold text-sm">_________________</p>
                  <p class="text-xs text-gray-600">DD/MM/YEAR</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Modal -->
      <div v-if="showComments" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-4 sm:p-6 w-full max-w-md mx-4 sm:mx-auto max-h-[90vh] overflow-auto">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg sm:text-xl font-bold">Comments</h2>
            <button @click="showComments = false" class="text-gray-500 hover:text-gray-700 text-xl">&times;</button>
          </div>
          <div class="space-y-4 max-h-60 overflow-auto">
            <div v-for="comment in comments" :key="comment.id" class="border-b pb-2">
              <div class="flex justify-between text-sm">
                <span class="font-semibold">{{ comment.author }}</span>
                <span class="text-gray-500">{{ formatDate(comment.date) }}</span>
              </div>
              <p class="text-sm mt-1">{{ comment.text }}</p>
            </div>
          </div>
          <div class="mt-4">
            <textarea
              v-model="newComment"
              class="w-full border rounded p-2 text-sm"
              placeholder="Add a comment..."
              rows="3"
            ></textarea>
            <div class="mt-2 flex justify-end gap-2">
              <button @click="addComment" class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-sm">Add Comment</button>
              <button @click="showComments = false" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-sm">Close</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import * as XLSX from 'xlsx';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';

// Add computed property for current year
const currentYear = computed(() => {
  return new Date().getFullYear();
});

const props = defineProps<{
  show: boolean;
  plan: any | null;
}>();

const emit = defineEmits(['close', 'delete-row', 'attach-file', 'save-other-details']);

const page = usePage<{ auth: { user: { name: string, middle_name?: string, last_name?: string, suffix?: string } } }>();

// Function to get the user's full name from the auth context
function getUserFullName() {
  const user = page.props.auth.user;

  if (!user) return 'Unknown User';

  // If plan has a specific preparer name, use that instead
  if (props.plan && props.plan.preparer_name) {
    return props.plan.preparer_name;
  }

  // Otherwise construct from user object
  let fullName = user.name || '';

  if (user.middle_name) {
    // Add middle initial
    fullName += ' ' + user.middle_name.charAt(0) + '.';
  }

  if (user.last_name) {
    fullName += ' ' + user.last_name;
  }

  if (user.suffix) {
    fullName += ', ' + user.suffix;
  }

  return fullName.toUpperCase();
}


const showComments = ref(false);
const showOtherDetails = ref(false);
const newComment = ref('');
const comments = ref<any[]>([]);
const visibleCommentCell = ref<string | null>(null);

// --- Comment integration for barangay users ---
const cellComments = ref<Record<string, { text: string, color: string, id?: number, author_id?: number }>>({});
const defaultColor = '#fff';

async function fetchComments() {
  if (!props.plan?.id) return;
  if (props.plan.status !== 'Revision' && props.plan.status !== 'Disapproved') return;
  try {
    const res = await axios.get(`/api/comments/${props.plan.id}`);
    const map: Record<string, { text: string, color: string, id?: number, author_id?: number }> = {};
    for (const c of res.data) {
      map[c.column_key] = { text: c.text, color: c.color || defaultColor, id: c.id, author_id: c.author_id };
    }
    cellComments.value = map;
  } catch (e) {
    console.error('Failed to fetch comments', e);
  }
}

onMounted(fetchComments);
watch(() => props.plan?.id, fetchComments);

function getCellComment(field: string) {
  const c = cellComments.value[field];
  if (!c) return '';
  return c.text;
}
function getCellColor(field: string) {
  const c = cellComments.value[field];
  if (!c) return defaultColor;
  return c.color || defaultColor;
}

// Function to toggle between main table and other details
function toggleDetailsView() {
  showOtherDetails.value = !showOtherDetails.value;
}

// Add a computed property to determine if we should show Gender Issue or GAD Mandate
const issueOrMandate = computed(() => {
  if (!props.plan) return { type: null, value: '-' };

  if (props.plan.genderIssue || props.plan.gender_issue) {
    return {
      type: 'Gender Issue',
      value: props.plan.genderIssue || props.plan.gender_issue
    };
  }

  if (props.plan.gadMandate || props.plan.gad_mandate) {
    return {
      type: 'GAD Mandate',
      value: props.plan.gadMandate || props.plan.gad_mandate
    };
  }

  return { type: null, value: '-' };
});

// Add a computed property for the title/description
const titleDescription = computed(() => {
  if (!props.plan) return '-';

  // Check for title/description in different possible property names
  return props.plan.titleDesc ||
         props.plan.title_desc ||
         props.plan.title ||
         props.plan.description ||
         '-';
});

// Add a computed property for the focused type
const focusedType = computed(() => {
  if (!props.plan) return '-';

  // Check for focused property in different formats
  const focused = props.plan.focused || props.plan.focus || '';

  // Return the value if it exists, otherwise default to '-'
  return focused || '-';
});

function formatNumber(value?: number): string {
  if (value === undefined || value === null || value === 0) return '';
  // Floor to whole number (always round down) and format with .00
  return Math.floor(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

function deleteRow() {
  if (props.plan) {
    emit('delete-row', props.plan);
  }
}


function confirmDelete() {
  if (confirm('Are you sure you want to delete this item?')) {
    emit('delete', props.plan);
  }
}

function viewDetails() {
  showOtherDetails.value = true;
}

function openComments() {
  showComments.value = true;
}

function exportToExcel() {
  if (!props.plan) return;

  // Create structured data that matches the print preview format
  const structuredData = [
    // Header row
    ['BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) PLAN FY ' + currentYear.value],
    [''], // Empty row for spacing

    // Main table headers
    ['Gender Issue or GAD Mandate', 'GAD Objective', 'Relevant GAD Program or Project', 'GAD Activity', 'Performance Indicator and Target', 'GAD Budget (6)', '', '', 'Lead or Responsible Office'],
    ['', '', '', '', '', 'MOOE', 'PS', 'CO', ''],

    // Focused type row
    [focusedType.value, '', '', '', '', 'MOOE', 'PS', 'CO', ''],

    // Issue/Mandate type row
    [props.plan?.gadMandate ? 'GAD Mandate' : props.plan?.genderIssue ? 'Gender Issue' : issueOrMandate.value, '', '', '', '', '', '', '', ''],

    // Data row
    [
      titleDescription.value,
      getPropertyValue('gadObjective'),
      getPropertyValue('lguProgram'),
      getPropertyValue('gadActivity'),
      getPropertyValue('performanceIndicator'),
      formatNumber(getPropertyValue('mooe')),
      formatNumber(getPropertyValue('ps')),
      formatNumber(getPropertyValue('co')),
      getPropertyValue('responsibleOffice')
    ],

    // Sub-total row
    [
      'Sub-total', '', '', '', '',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.mooe && props.plan.mooe > 0
        ? Math.floor(Number(props.plan.mooe || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : '',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.ps && props.plan.ps > 0
        ? Math.floor(Number(props.plan.ps || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : '',
      props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.co && props.plan.co > 0
        ? Math.floor(Number(props.plan.co || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
        : '',
      ''
    ],

    // Total row
    [
      'TOTAL (MOOE + PS + CO + LP):', '', '', '', '', '',
      Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}),
      '', ''
    ],

    // Grand Total row
    [
      'GRAND TOTAL (A+B+C+LP)', '', '', '', '', '',
      Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2}),
      '', ''
    ],

    [''], // Empty row for spacing
    [''], // Empty row for spacing

    // Other Details section
    ['OTHER DETAILS'],
    ['Supporting Statistics Data', 'Source of Supporting Statistics Data', 'PPA/s', 'Activity', 'Other Activity Category', 'Implementation Start', 'Implementation End'],
    [
      getPropertyValue('supportingStats') || '-',
      getPropertyValue('sourceStats') || '-',
      formatArrayValue(getPropertyValue('ppaSi')),
      Array.isArray(getPropertyValue('activity'))
        ? getPropertyValue('activity').map(act => `- ${formatActivityValue(act)}`).join('\n')
        : formatArrayValue(getPropertyValue('activity')) || '-',
      getPropertyValue('otherActivityCategory') || '-',
      formatDate(getPropertyValue('dateImplementationStart')),
      formatDate(getPropertyValue('dateImplementationEnd'))
    ],

    [''], // Empty row for spacing
    [''], // Empty row for spacing

    // Signature section
    ['SIGNATURES'],
    ['Prepared by:', '', 'Approved by:', '', 'Date:'],
    [getUserFullName(), '', '_________________', '', '_________________'],
    ['Barangay GAD Focal', '', 'Punong Barangay', '', 'DD/MM/YEAR'],
    [''], // Empty row
    ['Verified and Endorsed by:', '', '', 'Date Verified:'],
    [props.plan?.status === 'Approved' && props.plan?.admin_approver ? props.plan.admin_approver : '_________________', '', '', props.plan?.status === 'Approved' && props.plan?.approval_date ? formatApprovalDate(props.plan.approval_date) : '_________________'],
    ['Chairperson, GFPS TWG/secretary', '', '', 'DD/MM/YEAR']
  ];

  // Create worksheet from the structured data
  const ws = XLSX.utils.aoa_to_sheet(structuredData);

  // Set column widths for better formatting
  ws['!cols'] = [
    { wch: 25 }, // Gender Issue or GAD Mandate
    { wch: 20 }, // GAD Objective
    { wch: 25 }, // Relevant GAD Program or Project
    { wch: 20 }, // GAD Activity
    { wch: 25 }, // Performance Indicator and Target
    { wch: 15 }, // MOOE
    { wch: 15 }, // PS
    { wch: 15 }, // CO
    { wch: 20 }  // Lead or Responsible Office
  ];

  // Create workbook and add the worksheet
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'GAD Plan FY ' + currentYear.value);

  // Generate filename with current year
  const filename = `Barangay_GAD_Plan_FY${currentYear.value}.xlsx`;
  XLSX.writeFile(wb, filename);
}

function printpreview() {
  // Create a new window for printing only the modal content
  const printWindow = window.open('', '_blank', 'width=800,height=600');

  if (!printWindow) {
    alert('Please allow popups to enable printing');
    return;
  }

  // Get the print content (both main table and other details)
  const printContent = generatePrintContent();

  // Write the complete HTML document to the print window
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Barangay GAD Plan FY ${currentYear.value}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          font-size: 12px;
          line-height: 1.4;
        }

        .header {
          text-align: center;
          margin-bottom: 20px;
          font-weight: bold;
          font-size: 16px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          page-break-inside: avoid;
        }

        th, td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
          vertical-align: top;
          font-size: 10px;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .text-center {
          text-align: center;
        }

        .text-right {
          text-align: right;
        }

        .font-bold {
          font-weight: bold;
        }

        .bg-emerald-100 {
          background-color: #d1fae5;
        }

        .bg-emerald-200 {
          background-color: #a7f3d0;
        }

        .bg-emerald-300 {
          background-color: #6ee7b7;
        }

        .section-title {
          font-size: 14px;
          font-weight: bold;
          margin: 30px 0 15px 0;
          text-align: center;
        }

        .signature-section {
          margin-top: 20px;
        }

        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `);

  printWindow.document.close();

  // Wait for content to load, then print
  printWindow.onload = function() {
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
}

function generatePrintContent() {
  const currentYearValue = currentYear.value;
  const userFullName = getUserFullName();

  return `
    <div class="header">
      BARANGAY ANNUAL GENDER AND DEVELOPMENT (GAD) PLAN FY ${currentYearValue}
    </div>

    <!-- Main Budget Table -->
    <table>
      <thead>
        <tr>
          <th>Gender Issue or GAD Mandate</th>
          <th>GAD Objective</th>
          <th>Relevant GAD Program or Project</th>
          <th>GAD Activity</th>
          <th>Performance Indicator and Target</th>
          <th>MOOE</th>
          <th>PS</th>
          <th>CO</th>
          <th>Lead or Responsible Office</th>
        </tr>
        <tr>
          <th colspan="5" class="font-bold">${focusedType.value}</th>
          <th class="text-center">MOOE</th>
          <th class="text-center">PS</th>
          <th class="text-center">CO</th>
          <th></th>
        </tr>
        <tr>
          <td colspan="5" class="font-bold bg-emerald-100">
            ${props.plan?.gadMandate ? 'GAD Mandate' : props.plan?.genderIssue ? 'Gender Issue' : issueOrMandate.value}
          </td>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${titleDescription.value}</td>
          <td>${getPropertyValue('gadObjective')}</td>
          <td>${getPropertyValue('lguProgram')}</td>
          <td>${getPropertyValue('gadActivity')}</td>
          <td>${getPropertyValue('performanceIndicator')}</td>
          <td class="text-right">${formatNumber(getPropertyValue('mooe'))}</td>
          <td class="text-right">${formatNumber(getPropertyValue('ps'))}</td>
          <td class="text-right">${formatNumber(getPropertyValue('co'))}</td>
          <td>${getPropertyValue('responsibleOffice')}</td>
        </tr>

        <!-- Sub-total row -->
        <tr class="bg-emerald-100">
          <td colspan="5" class="font-bold">Sub-total</td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.mooe && props.plan.mooe > 0
              ? Math.floor(Number(props.plan.mooe || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : ''}
          </td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.ps && props.plan.ps > 0
              ? Math.floor(Number(props.plan.ps || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : ''}
          </td>
          <td class="text-right font-bold">
            ${props.plan?.lp_allocation && props.plan.lp_allocation > 0 && props.plan?.co && props.plan.co > 0
              ? Math.floor(Number(props.plan.co || 0) + Number(props.plan.lp_allocation || 0)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})
              : ''}
          </td>
          <td></td>
        </tr>

        <!-- Total row -->
        <tr class="bg-emerald-200 font-bold">
          <td colspan="5" class="font-bold">TOTAL (MOOE + PS + CO + LP):</td>
          <td></td>
          <td class="text-center font-bold">
            ${Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}
          </td>
          <td></td>
          <td></td>
        </tr>

        <!-- Grand Total row -->
        <tr class="bg-emerald-300 font-bold">
          <td colspan="5" class="font-bold">GRAND TOTAL (A+B+C+LP)</td>
          <td></td>
          <td class="text-right font-bold" style="font-size: 14px;">
            ${Math.floor(getCombinedTotal(props.plan)).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}
          </td>
          <td></td>
          <td></td>
        </tr>
      </tbody>
    </table>

    <!-- Other Details Section - Centered below Grand Total -->

    <table>
      <thead>
        <tr>
          <th>Supporting Statistics Data</th>
          <th>Source of Supporting Statistics Data</th>
          <th>PPA/s</th>
          <th>Activity</th>
          <th>Other Activity Category</th>
          <th>Implementation Start</th>
          <th>Implementation End</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${getPropertyValue('supportingStats') || '-'}</td>
          <td>${getPropertyValue('sourceStats') || '-'}</td>
          <td>${formatArrayValue(getPropertyValue('ppaSi'))}</td>
          <td>
            ${Array.isArray(getPropertyValue('activity'))
              ? getPropertyValue('activity').map(act => `- ${formatActivityValue(act)}`).join('<br>')
              : formatArrayValue(getPropertyValue('activity')) || '-'}
          </td>
          <td>${getPropertyValue('otherActivityCategory') || '-'}</td>
          <td>${formatDate(getPropertyValue('dateImplementationStart'))}</td>
          <td>${formatDate(getPropertyValue('dateImplementationEnd'))}</td>
        </tr>
      </tbody>
    </table>

    <!-- Signature Section -->
    <table class="signature-section">
      <tr>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Prepared by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${userFullName}</div>
          <div style="font-size: 10px;">Barangay GAD Focal</div>
        </td>
        <td style="width: 33%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Approved by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">Punong Barangay</div>
        </td>
        <td style="width: 34%;">
          <div style="font-weight: bold; margin-bottom: 5px;">Date:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">_________________</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
      <tr>
        <td>
          <div style="font-weight: bold; margin-bottom: 5px;">Verified and Endorsed by:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.plan?.status === 'Approved' && props.plan?.admin_approver ? props.plan.admin_approver : '_________________'}</div>
          <div style="font-size: 10px;">Chairperson, GFPS TWG/secretary</div>
        </td>
        <td colspan="2">
          <div style="font-weight: bold; margin-bottom: 5px;">Date Verified:</div>
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 20px;">${props.plan?.status === 'Approved' && props.plan?.approval_date ? formatApprovalDate(props.plan.approval_date) : '_________________'}</div>
          <div style="font-size: 10px;">DD/MM/YEAR</div>
        </td>
      </tr>
    </table>
  `;
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB');
}

function formatApprovalDate(dateString: string) {
  if (!dateString) return 'DD/MM/YEAR';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

function addComment() {
  if (!newComment.value.trim()) return;
  comments.value.push({
    id: Date.now(),
    author: 'Current User', // Replace with actual user info
    date: new Date(),
    text: newComment.value
  });
  newComment.value = '';
}

// Improved getPropertyValue function to handle both camelCase and snake_case
function getPropertyValue(propertyName: string) {
  if (!props.plan) return '-';

  // Special case for focused field
  if (propertyName === 'focused') {
    return focusedType.value;
  }

  // Special case for title/description
  if (propertyName === 'titleDesc') {
    return titleDescription.value;
  }

  // Try camelCase first
  if (props.plan[propertyName] !== undefined && props.plan[propertyName] !== null) {
    return props.plan[propertyName];
  }

  // Try snake_case version
  const snakeCase = propertyName.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`);
  if (props.plan[snakeCase] !== undefined && props.plan[snakeCase] !== null) {
    return props.plan[snakeCase];
  }

  // Special case for specific fields
  if (propertyName === 'performanceIndicator' && props.plan.performanceTarget) {
    return props.plan.performanceTarget;
  }
  if (propertyName === 'performanceIndicator' && props.plan.performance_target) {
    return props.plan.performance_target;
  }

  // Special case for responsible office
  if (propertyName === 'responsibleOffice' && props.plan.leadOffice) {
    return props.plan.leadOffice;
  }
  if (propertyName === 'responsibleOffice' && props.plan.lead_office) {
    return props.plan.lead_office;
  }

  // Special case for supporting statistics data
  if (propertyName === 'supportingStats' && props.plan.supportingData) {
    return props.plan.supportingData;
  }

  // Special case for source of supporting statistics data
  if (propertyName === 'sourceStats' && props.plan.dataSource) {
    return props.plan.dataSource;
  }

  return '-';
}



// Add this to debug the plan object
onMounted(() => {
  console.log('BarangayBudgetPlanModal mounted');
  console.log('Plan data:', props.plan);
  if (props.plan && props.plan.status === 'Approved') {
    console.log('Approver name:', props.plan.approver_name);
  }
});

// Add a watcher to log when the plan changes
watch(() => props.plan, (newPlan) => {
  console.log('Plan changed in BarangayBudgetPlanModal:', newPlan);
  if (newPlan && newPlan.status === 'Approved') {
    console.log('Approver name after change:', newPlan.approver_name);
  }
}, { deep: true });

// Add a watcher to debug when the plan changes
watch(() => props.plan, (newPlan) => {
  console.log("Plan received in modal:", newPlan);
  if (newPlan) {
    console.log("Title/Description:", titleDescription.value);
    console.log("Focused value:", focusedType.value);
  }
}, { immediate: true, deep: true });

// Helper function to format array values
function formatArrayValue(value: any) {
  if (!value) return '-';

  if (Array.isArray(value)) {
    // Join array elements with commas and capitalize first letter of each item
    return value.map((item: any) => {
      const str = String(item);
      return str.charAt(0).toUpperCase() + str.slice(1);
    }).join(', ');
  }

  // If it's a string that looks like JSON array, try to parse and format it
  if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.map(item => {
          const str = String(item);
          return str.charAt(0).toUpperCase() + str.slice(1);
        }).join(', ');
      }
    } catch (e) {
      // If parsing fails, just clean the string
      return value.replace(/[\[\]"\\]/g, '');
    }
  }

  // For regular strings
  return String(value);
}

// Helper function specifically for activity values to handle escaped slashes
function formatActivityValue(value: any) {
  if (!value) return '-';

  // Replace escaped slashes and other special characters
  return String(value).replace(/\\+\//g, '/');
}

function getStatusMessage(status: string) {
  if (!status) return '';

  const messages = {
    'Draft': 'The plan is a draft and is not yet submitted for review',
    'Pending': 'The plan is awaiting review',
    'Approved': 'The plan has been approved',
    'Rejected': 'You dont follow the GAD compliance',
    'Revision': 'The revision has been requested by the reviewer'
  };

  return messages[status] || '';
}

// Helper function to get manual total (MOOE + PS + CO)
function getManualTotal(plan: any): number {
  return (Number(plan.mooe || 0) + Number(plan.ps || 0) + Number(plan.co || 0));
}

// Helper function to get combined budget (LP allocation + manual total)
function getCombinedTotal(plan: any): number {
  const lpAllocation = Number(plan.lp_allocation || 0);
  const manualTotal = getManualTotal(plan);

  // If total_budget is set (from backend calculation), use it
  // Otherwise, calculate as LP allocation + manual total
  return plan.total_budget || (lpAllocation + manualTotal);
}

function handleDocumentClick(e: MouseEvent) {
  if (visibleCommentCell.value) {
    // Only close if click is outside any popover or badge
    const popover = document.querySelector('.popover-comment-visible');
    const badges = document.querySelectorAll('.view-comment-badge');
    let clickedBadge = false;
    badges.forEach(badge => {
      if (badge.contains(e.target as Node)) clickedBadge = true;
    });
    if (popover && !popover.contains(e.target as Node) && !clickedBadge) {
      visibleCommentCell.value = null;
    }
  }
}
onMounted(() => {
  document.addEventListener('mousedown', handleDocumentClick);
});
onUnmounted(() => {
  document.removeEventListener('mousedown', handleDocumentClick);
});
</script>

<style scoped>
.bg-pink-200 { background-color: #e9b7e7 !important; }
.bg-yellow-200 { background-color: #fff9c4 !important; }
.bg-green-100 { background-color: #d1fae5 !important; }

.cell-comment-indicator-badge {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.75em;
  padding: 0.15em 0.7em 0.15em 0.4em;
  font-size: 0.85em;
  font-weight: 500;
  color: #222;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
  cursor: pointer;
  transition: background 0.2s, border 0.2s, box-shadow 0.2s;
  outline: none;
}
.cell-comment-indicator-badge:hover, .cell-comment-indicator-badge:focus {
  background: #e0e7ef;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px #60a5fa33;
}
.badge-text {
  font-size: 0.92em;
  font-weight: 500;
  color: #222;
  white-space: nowrap;
}
.icon-eye {
  width: 1.2em;
  height: 1.2em;
  color: black;
  opacity: 0.95;
}
.popover-comment {
  position: absolute;
  left: 50%;
  background: #fff;
  color: #065f46;
  border: 1px solid #10b981;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.85em;
  white-space: pre-line;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(16,185,129,0.15);
  min-width: 120px;
  max-width: 250px;
  pointer-events: none;
}
.popover-comment-large {
  font-size: 1.15em;
  font-weight: bold;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  padding: 1em 1.2em;
  margin-top: 0.5em;
  background: #f8fafc;
  color: #222;
  border: 2px solid #60a5fa;
  border-radius: 0.5em;
  text-align: left;
  word-break: break-word;
}
.popover-comment-visible {
  pointer-events: auto;
  margin-top: 0 !important;
}
</style>










































