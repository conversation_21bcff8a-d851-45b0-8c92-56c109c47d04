<template>
  <BarChart :chart-data="chartData" :options="chartOptions" />
</template>

<script setup>
import { BarChart } from 'vue-chart-3';
import { Chart, BarElement, Tooltip, Legend, CategoryScale, LinearScale, BarController } from 'chart.js';

Chart.register(BarController, BarElement, CategoryScale, LinearScale, Tooltip, Legend);

const props = defineProps({
  chartData: { type: Object, required: true },
  chartOptions: { type: Object, default: () => ({ responsive: true, maintainAspectRatio: false }) }
});
</script>

<style scoped>
:deep(canvas) {
  width: 100% !important;
  height: 250px !important;
}
</style>
