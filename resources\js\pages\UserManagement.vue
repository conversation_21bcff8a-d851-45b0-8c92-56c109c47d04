<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 px-6 py-4 rounded-lg mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <div>
            <h1 class="text-xl font-bold text-white">User Management System</h1>
            <p class="text-emerald-100 text-sm">Manage all system users and permissions</p>
          </div>
        </div>
        <button
          v-if="userRole === 'admin'"
          @click="openAddUserModal"
          class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-150 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New User
        </button>
      </div>
    </div>

    <!-- User Charts -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3 rounded-t-lg -mx-4 -mt-4 mb-4">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 class="text-sm font-bold text-white">Active vs. Inactive Users Overview</h3>
              <p class="text-emerald-100 text-xs">Current System Status</p>
            </div>
          </div>
        </div>
        <div class="flex flex-col lg:flex-row gap-4 items-center w-full">
          <div class="flex-1 flex justify-center w-full">
            <PieChart :chart-data="userStatusPieData" class="w-full max-w-xs" />
          </div>
          <div class="flex flex-col gap-2 flex-1 w-full lg:w-auto mt-4 lg:mt-0">
            <div class="flex flex-col gap-2">
              <div
                v-for="(status, idx) in userStatusLegendData"
                :key="status.label"
                class="flex items-center gap-2 text-sm"
              >
                <span class="w-3 h-3 rounded-full border border-emerald-200" :style="{ background: status.color }"></span>
                <span class="font-medium text-emerald-700 flex-1">{{ status.label }}</span>
                <span class="font-semibold text-emerald-600">{{ status.value }}</span>
              </div>
            </div>
            <div class="mt-3 text-xs text-emerald-800 bg-emerald-50 rounded p-2 sm:p-3 font-medium">
              Shows the current distribution of active and inactive users in the system.
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-2xl shadow-lg border border-emerald-200 p-4">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3 rounded-t-lg -mx-4 -mt-4 mb-4">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 class="text-sm font-bold text-white">User Registration Trend</h3>
              <p class="text-emerald-100 text-xs">Last 12 Months</p>
            </div>
          </div>
        </div>
        <LineChart :chart-data="userRegistrationLineData" class="w-full max-w-xs" />
      </div>
    </div>

    <!-- Stats Bar -->
    <div class="bg-white px-6 py-4 rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-6">
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
            <span class="text-sm font-medium text-gray-700">Total Users: {{ barangayUsers.active + adminUsers.active }}</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span class="text-sm font-medium text-gray-700">Admin: {{ adminUsers.active }}</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-amber-500 rounded-full"></div>
            <span class="text-sm font-medium text-gray-700">Barangay: {{ barangayUsers.active }}</span>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          Last updated: {{ new Date().toLocaleString() }}
        </div>
      </div>
    </div>

    <!-- User List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div v-if="usersLoading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-3 text-emerald-600">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-600"></div>
          <span class="text-sm font-medium">Loading users...</span>
        </div>
      </div>
      <div v-else class="p-6">
        <!-- Admin: Show all users -->
        <template v-if="userRole === 'admin'">
          <!-- Admin Users Section -->
          <template v-if="adminUsers.list.length">
            <div class="mb-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Admin Users
                </h3>
                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {{ adminUsers.active }} of {{ adminUsers.total }}
                </span>
              </div>
              <div class="grid gap-3">
                <div v-for="user in adminUsers.list" :key="'admin-' + user.id"
                     :class="[
                       'border rounded-lg p-4 hover:shadow-md transition-shadow duration-200',
                       user.status === 'active' 
                         ? 'bg-white border-gray-200' 
                         : 'bg-gray-50 border-gray-300'
                     ]">
                  <div class="flex items-center gap-4">
                    <div class="relative">
                      <img :src="user.avatar || 'https://randomuser.me/api/portraits/lego/2.jpg'"
                           alt="avatar" class="w-12 h-12 rounded-full object-cover border-2 border-blue-200" />
                      <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div v-if="user.status === 'inactive'" class="absolute -top-1 -left-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-2">
                        <h4 class="text-sm font-semibold text-gray-900 truncate">{{ user.name }}</h4>
                        <span class="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-medium">Admin</span>
                        <span v-if="user.status === 'active'" class="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full font-medium">Active</span>
                        <span v-else class="bg-red-100 text-red-700 text-xs px-2 py-0.5 rounded-full font-medium">Inactive</span>
                      </div>
                      <p class="text-sm text-gray-600 truncate">{{ user.email }}</p>
                      <div v-if="user.barangay" class="mt-1">
                        <span class="inline-flex items-center gap-1 text-xs text-gray-500">
                          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                          </svg>
                          {{ user.barangay }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Barangay Users Section -->
          <template v-if="barangayUsers.list.length">
            <div class="mb-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <div class="w-2 h-2 bg-amber-500 rounded-full"></div>
                  Barangay Users
                </h3>
                <span class="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {{ barangayUsers.active }} of {{ barangayUsers.total }}
                </span>
              </div>
              <div class="grid gap-3">
                <div v-for="user in barangayUsers.list" :key="'barangay-' + user.id"
                     :class="[
                       'border rounded-lg p-4 hover:shadow-md transition-shadow duration-200',
                       user.status === 'active' 
                         ? 'bg-white border-gray-200' 
                         : 'bg-gray-50 border-gray-300'
                     ]">
                  <div class="flex items-center gap-4">
                    <div class="relative">
                      <img :src="user.avatar || 'https://randomuser.me/api/portraits/lego/1.jpg'"
                           alt="avatar" class="w-12 h-12 rounded-full object-cover border-2 border-amber-200" />
                      <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-amber-500 rounded-full border-2 border-white flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                        </svg>
                      </div>
                      <div v-if="user.status === 'inactive'" class="absolute -top-1 -left-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-2">
                        <h4 class="text-sm font-semibold text-gray-900 truncate">{{ user.name }}</h4>
                        <span class="bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded-full font-medium">Barangay</span>
                        <span v-if="user.status === 'active'" class="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full font-medium">Active</span>
                        <span v-else class="bg-red-100 text-red-700 text-xs px-2 py-0.5 rounded-full font-medium">Inactive</span>
                      </div>
                      <p class="text-sm text-gray-600 truncate">{{ user.email }}</p>
                      <div v-if="user.barangay" class="mt-1">
                        <span class="inline-flex items-center gap-1 text-xs text-gray-500">
                          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                          </svg>
                          {{ user.barangay }}
                        </span>
                      </div>
                    </div>
                    <div class="flex items-center gap-2">
                      <button @click="openEditUserModal(user)"
                              class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-100 border border-emerald-200 rounded-md hover:bg-emerald-200 hover:border-emerald-300 transition-colors duration-150"
                              title="Edit User">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit
                      </button>
                      <button v-if="user.status === 'active'" 
                              @click="deactivateUser(user)"
                              class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-amber-700 bg-amber-100 border border-amber-200 rounded-md hover:bg-amber-200 hover:border-amber-300 transition-colors duration-150"
                              title="Deactivate User">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                        </svg>
                        Deactivate
                      </button>
                      <button v-else
                              @click="reactivateUser(user)"
                              class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-200 rounded-md hover:bg-green-200 hover:border-green-300 transition-colors duration-150"
                              title="Reactivate User">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Reactivate
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>

        <!-- Barangay user: show only own account -->
        <template v-else>
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                Your Account
              </h3>
            </div>
            <div class="grid gap-3">
              <div v-for="user in barangayUsers.list" :key="'barangay-' + user.id"
                   :class="[
                     'border rounded-lg p-4 hover:shadow-md transition-shadow duration-200',
                     user.status === 'active' 
                       ? 'bg-white border-gray-200' 
                       : 'bg-gray-50 border-gray-300'
                   ]">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <img :src="user.avatar || 'https://randomuser.me/api/portraits/lego/1.jpg'"
                         alt="avatar" class="w-12 h-12 rounded-full object-cover border-2 border-emerald-200" />
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white flex items-center justify-center">
                      <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                      </svg>
                    </div>
                    <div v-if="user.status === 'inactive'" class="absolute -top-1 -left-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white flex items-center justify-center">
                      <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2">
                      <h4 class="text-sm font-semibold text-gray-900 truncate">{{ user.name }}</h4>
                      <span class="bg-emerald-100 text-emerald-700 text-xs px-2 py-0.5 rounded-full font-medium">You</span>
                    </div>
                    <p class="text-sm text-gray-600 truncate">{{ user.email }}</p>
                    <div v-if="user.barangay" class="mt-1">
                      <span class="inline-flex items-center gap-1 text-xs text-gray-500">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                        </svg>
                        {{ user.barangay }}
                      </span>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <button @click="openEditUserModal(user)"
                            class="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-100 border border-emerald-200 rounded-md hover:bg-emerald-200 hover:border-emerald-300 transition-colors duration-150"
                            title="Edit Profile">
                      <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Edit Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- Add User Modal -->
    <div v-if="showAddUserModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-[90vh] mx-4 flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 px-6 py-4 flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-white">Add New User</h2>
              <p class="text-emerald-100 text-sm">Create a new user account</p>
            </div>
          </div>
          <button @click="closeAddUserModal" class="text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form Content -->
        <div class="flex-1 overflow-y-auto p-6">
          <form @submit.prevent="handleCreateUser" class="space-y-6">
            <!-- Personal Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">First Name *</label>
                <input
                  v-model="addUserForm.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter first name"
                />
                <span v-if="addUserErrors.name" class="text-red-500 text-xs">{{ addUserErrors.name[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Middle Name</label>
                <input
                  v-model="addUserForm.middle_name"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter middle name"
                />
                <span v-if="addUserErrors.middle_name" class="text-red-500 text-xs">{{ addUserErrors.middle_name[0] }}</span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Last Name *</label>
                <input
                  v-model="addUserForm.last_name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter last name"
                />
                <span v-if="addUserErrors.last_name" class="text-red-500 text-xs">{{ addUserErrors.last_name[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Suffix</label>
                <input
                  v-model="addUserForm.suffix"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Jr., Sr., III, etc."
                />
                <span v-if="addUserErrors.suffix" class="text-red-500 text-xs">{{ addUserErrors.suffix[0] }}</span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Birthdate *</label>
                <input
                  v-model="addUserForm.birthdate"
                  type="date"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                />
                <span v-if="addUserErrors.birthdate" class="text-red-500 text-xs">{{ addUserErrors.birthdate[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Gender *</label>
                <select
                  v-model="addUserForm.gender"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                <span v-if="addUserErrors.gender" class="text-red-500 text-xs">{{ addUserErrors.gender[0] }}</span>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Mobile Number *</label>
              <input
                v-model="addUserForm.mobile_number"
                type="tel"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                placeholder="Enter mobile number"
              />
              <span v-if="addUserErrors.mobile_number" class="text-red-500 text-xs">{{ addUserErrors.mobile_number[0] }}</span>
            </div>

            <!-- Location Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Region *</label>
                <select
                  v-model="addUserForm.region"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select Region</option>
                  <option v-for="region in locationRegions" :key="region.value" :value="region.value">
                    {{ region.label }}
                  </option>
                </select>
                <span v-if="addUserErrors.region" class="text-red-500 text-xs">{{ addUserErrors.region[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Province *</label>
                <select
                  v-model="addUserForm.province"
                  required
                  :disabled="!addUserForm.region"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100"
                >
                  <option value="">Select Province</option>
                  <option v-for="province in locationProvinces" :key="province.value" :value="province.value">
                    {{ province.label }}
                  </option>
                </select>
                <span v-if="addUserErrors.province" class="text-red-500 text-xs">{{ addUserErrors.province[0] }}</span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">City *</label>
                <select
                  v-model="addUserForm.city"
                  required
                  :disabled="!addUserForm.province"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100"
                >
                  <option value="">Select City</option>
                  <option v-for="city in locationCities" :key="city.value" :value="city.value">
                    {{ city.label }}
                  </option>
                </select>
                <span v-if="addUserErrors.city" class="text-red-500 text-xs">{{ addUserErrors.city[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Barangay *</label>
                <select
                  v-model="addUserForm.barangay"
                  required
                  :disabled="!addUserForm.city"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100"
                >
                  <option value="">Select Barangay</option>
                  <option v-for="barangay in locationBarangays" :key="barangay.value" :value="barangay.value">
                    {{ barangay.label }}
                  </option>
                </select>
                <span v-if="addUserErrors.barangay" class="text-red-500 text-xs">{{ addUserErrors.barangay[0] }}</span>
              </div>
            </div>

            <!-- Account Information -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Email Address *</label>
              <input
                v-model="addUserForm.email"
                type="email"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                placeholder="Enter email address"
              />
              <span v-if="addUserErrors.email" class="text-red-500 text-xs">{{ addUserErrors.email[0] }}</span>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Role *</label>
              <select
                v-model="addUserForm.role"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
              >
                <option value="barangay">GAD FOCAL</option>
                <option value="admin">GAD ADMIN</option>
              </select>
              <span v-if="addUserErrors.role" class="text-red-500 text-xs">{{ addUserErrors.role[0] }}</span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Password *</label>
                <input
                  v-model="addUserForm.password"
                  type="password"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter password"
                />
                <span v-if="addUserErrors.password" class="text-red-500 text-xs">{{ addUserErrors.password[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Confirm Password *</label>
                <input
                  v-model="addUserForm.password_confirmation"
                  type="password"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Confirm password"
                />
              </div>
            </div>
          </form>
        </div>

        <!-- Footer -->
        <div class="flex-shrink-0 bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-end gap-3">
            <button
              type="button"
              @click="closeAddUserModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150"
            >
              Cancel
            </button>
            <button
              type="submit"
              @click="handleCreateUser"
              :disabled="addUserLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
            >
              <span v-if="addUserLoading" class="flex items-center gap-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Creating...
              </span>
              <span v-else>Create User</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Modal -->
    <div v-if="showEditUserModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-[90vh] mx-4 flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 px-6 py-4 flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-white">Edit User Profile</h2>
              <p class="text-emerald-100 text-sm">Update user information and settings</p>
            </div>
          </div>
          <button @click="closeEditUserModal" class="text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form Content -->
        <div class="flex-1 overflow-y-auto p-6">
          <form @submit.prevent="handleEditUser" class="space-y-6">
            <!-- Avatar Section -->
            <div class="flex items-center gap-6 p-4 bg-gray-50 rounded-lg">
              <img :src="editUserForm.avatar || 'https://cdn-icons-png.flaticon.com/512/747/747376.png'"
                   alt="User Avatar"
                   class="w-20 h-20 rounded-full object-cover border-4 border-emerald-200 shadow-lg" />
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Profile Picture</h3>
                <p class="text-sm text-gray-600 mb-2">Update the user's profile image</p>
                <label class="inline-flex items-center gap-2 px-3 py-2 bg-emerald-100 text-emerald-700 rounded-md cursor-pointer hover:bg-emerald-200 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Change Picture
                  <input type="file" accept="image/*" class="hidden" @change="handleAvatarChange" />
                </label>
              </div>
            </div>

            <!-- Personal Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">First Name *</label>
                <input
                  v-model="editUserForm.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter first name"
                />
                <span v-if="editUserErrors.name" class="text-red-500 text-xs">{{ editUserErrors.name[0] }}</span>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Email Address *</label>
                <input
                  v-model="editUserForm.email"
                  type="email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Enter email address"
                />
                <span v-if="editUserErrors.email" class="text-red-500 text-xs">{{ editUserErrors.email[0] }}</span>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Password <span class="text-xs text-gray-400">(leave blank to keep unchanged)</span></label>
              <input
                v-model="editUserForm.password"
                type="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-emerald-500 focus:border-emerald-500"
                autocomplete="new-password"
                placeholder="Enter new password"
              />
              <span v-if="editUserErrors.password" class="text-red-500 text-xs">{{ editUserErrors.password[0] }}</span>
            </div>
          </form>
        </div>

        <!-- Footer -->
        <div class="flex-shrink-0 bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-end gap-3">
            <button
              type="button"
              @click="closeEditUserModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150"
            >
              Cancel
            </button>
            <button
              type="submit"
              @click="handleEditUser"
              :disabled="editUserLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
            >
              <span v-if="editUserLoading" class="flex items-center gap-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Updating...
              </span>
              <span v-else>Update User</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Modal -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Confirm Icon -->
            <svg v-else-if="modalType === 'confirm'" class="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button v-if="modalType === 'confirm'" @click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Cancel</button>
          <button @click="modalType === 'confirm' ? confirmAction() : closeModal()" class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors">
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * TIP: If you get 401 errors, make sure to call `/sanctum/csrf-cookie` before any API requests, and ensure axios sends credentials:
 * axios.defaults.withCredentials = true;
 * await axios.get('/sanctum/csrf-cookie');
 */
import { ref, onMounted, watch, computed } from 'vue'
import axios from 'axios'
import { usePage, useForm } from '@inertiajs/vue3'
import PieChart from '../components/ui/charts/PieChart.vue';
import LineChart from '../components/ui/charts/LineChart.vue';

interface DashboardUser {
  id: number
  name: string
  email: string
  barangay?: string
  avatar?: string
  status?: string
  created_at?: string
}

const page = usePage<{ auth: { user: { role: string, id: number, name: string, email: string, barangay?: string, avatar?: string, status?: string } } }>()
const userRole = page.props.auth.user?.role

const barangayUsers = ref<{ total: number; active: number; list: DashboardUser[] }>({ total: 0, active: 0, list: [] })
const adminUsers = ref<{ total: number; active: number; list: DashboardUser[] }>({ total: 0, active: 0, list: [] })
const usersLoading = ref(false)

// Modal states
const showEditUserModal = ref(false)
const showAddUserModal = ref(false)

// User form data
const editUserForm = useForm({
  id: null,
  name: '',
  middle_name: '',
  last_name: '',
  suffix: '',
  birthdate: '',
  gender: '',
  mobile_number: '',
  region: '',
  province: '',
  city: '',
  barangay: '',
  email: '',
  password: '',
  role: '',
  avatar: ''
})

const addUserForm = useForm({
  name: '',
  middle_name: '',
  last_name: '',
  suffix: '',
  birthdate: '',
  gender: '',
  mobile_number: '',
  region: '',
  province: '',
  city: '',
  barangay: '',
  email: '',
  password: '',
  password_confirmation: '',
  role: 'barangay'
})

const editUserErrors = ref<Record<string, string[]>>({})
const addUserErrors = ref<Record<string, string[]>>({})
const editUserLoading = ref(false)
const addUserLoading = ref(false)

// Location dropdown state
interface LocationOption {
  value: string;
  label: string;
}

const locationRegions = ref<LocationOption[]>([])
const locationProvinces = ref<LocationOption[]>([])
const locationCities = ref<LocationOption[]>([])
const locationBarangays = ref<LocationOption[]>([])

// Edit location dropdown state
const editLocationRegions = ref<LocationOption[]>([])
const editLocationProvinces = ref<LocationOption[]>([])
const editLocationCities = ref<LocationOption[]>([])
const editLocationBarangays = ref<LocationOption[]>([])

// Modal state for notifications (reused from BudgetPlan.vue)
const modalVisible = ref(false)
const modalTitle = ref('')
const modalMessage = ref('')
const modalType = ref('info') // 'info', 'error', 'success', 'confirm'
const modalConfirmCallback = ref<(() => void) | null>(null)

function showModal(title: string, message: string, type: string = 'info') {
  modalTitle.value = title
  modalMessage.value = message
  modalType.value = type
  modalConfirmCallback.value = null
  modalVisible.value = true
}
function showConfirmModal(title: string, message: string, onConfirm: () => void) {
  modalTitle.value = title
  modalMessage.value = message
  modalType.value = 'confirm'
  modalConfirmCallback.value = onConfirm
  modalVisible.value = true
}
function closeModal() {
  modalVisible.value = false
  modalConfirmCallback.value = null
}
function confirmAction() {
  if (modalConfirmCallback.value) {
    modalConfirmCallback.value()
  }
  closeModal()
}

function openEditUserModal(user: any) {
  editUserForm.id = user.id
  editUserForm.name = user.name || ''
  editUserForm.middle_name = user.middle_name || ''
  editUserForm.last_name = user.last_name || ''
  editUserForm.suffix = user.suffix || ''
  editUserForm.birthdate = user.birthdate || ''
  editUserForm.gender = user.gender || ''
  editUserForm.mobile_number = user.mobile_number || ''
  editUserForm.region = user.region || ''
  editUserForm.province = user.province || ''
  editUserForm.city = user.city || ''
  editUserForm.barangay = user.barangay || ''
  editUserForm.email = user.email || ''
  editUserForm.password = ''
  editUserForm.role = user.role || 'barangay'
  editUserForm.avatar = user.avatar || ''
  showEditUserModal.value = true
  fetchEditRegions()
}

function openAddUserModal() {
  showAddUserModal.value = true
  resetAddUserForm()
  fetchRegions() // Load regions when modal opens
}

async function deactivateUser(user: any) {
  showConfirmModal(
    'Deactivate User',
    `Are you sure you want to deactivate ${user.name}? This will prevent them from accessing the system.`,
    async () => {
      try {
        const response = await axios.patch(`/admin/users/${user.id}/deactivate`)
        showModal('User Deactivated', 'User has been deactivated successfully.', 'success')
        await fetchUsers()
      } catch (error: any) {
        const message = error.response?.data?.error || 'Failed to deactivate user'
        showModal('Deactivation Failed', message, 'error')
      }
    }
  )
}

async function reactivateUser(user: any) {
  showConfirmModal(
    'Reactivate User',
    `Are you sure you want to reactivate ${user.name}? This will restore their access to the system.`,
    async () => {
      try {
        const response = await axios.patch(`/admin/users/${user.id}/reactivate`)
        showModal('User Reactivated', 'User has been reactivated successfully.', 'success')
        await fetchUsers()
      } catch (error: any) {
        const message = error.response?.data?.error || 'Failed to reactivate user'
        showModal('Reactivation Failed', message, 'error')
      }
    }
  )
}

function closeEditUserModal() {
  showEditUserModal.value = false
  resetEditUserForm()
}

function closeAddUserModal() {
  showAddUserModal.value = false
  resetAddUserForm()
}

function resetEditUserForm() {
  editUserForm.id = null
  editUserForm.name = ''
  editUserForm.middle_name = ''
  editUserForm.last_name = ''
  editUserForm.suffix = ''
  editUserForm.birthdate = ''
  editUserForm.gender = ''
  editUserForm.mobile_number = ''
  editUserForm.region = ''
  editUserForm.province = ''
  editUserForm.city = ''
  editUserForm.barangay = ''
  editUserForm.email = ''
  editUserForm.password = ''
  editUserForm.role = ''
  editUserForm.avatar = ''
  editUserErrors.value = {}
}

function resetAddUserForm() {
  addUserForm.name = ''
  addUserForm.middle_name = ''
  addUserForm.last_name = ''
  addUserForm.suffix = ''
  addUserForm.birthdate = ''
  addUserForm.gender = ''
  addUserForm.mobile_number = ''
  addUserForm.region = ''
  addUserForm.province = ''
  addUserForm.city = ''
  addUserForm.barangay = ''
  addUserForm.email = ''
  addUserForm.password = ''
  addUserForm.password_confirmation = ''
  addUserForm.role = 'barangay'
}

function handleAvatarChange(e: any) {
  const file = e.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (ev: any) => {
      if (ev.target?.result) {
        editUserForm.avatar = ev.target.result as string
      }
    }
    reader.readAsDataURL(file)
  }
}

function handleEditUser() {
  showConfirmModal(
    'Update User',
    'Are you sure you want to update this user? This action cannot be undone.',
    () => {
      submitEditUser()
    }
  )
}

function handleCreateUser() {
  showConfirmModal(
    'Create User',
    'Are you sure you want to create this user? This action cannot be undone.',
    () => {
      submitAddUser()
    }
  )
}

function submitAddUser() {
  addUserForm.post('/admin/users', {
    onSuccess: () => {
      showModal('User Created', 'User created successfully!', 'success')
      closeAddUserModal()
      fetchUsers()
    },
    onError: () => {
      showModal('User Creation Failed', 'Failed to create user. Please check the form and try again.', 'error')
    }
  })
}

function submitEditUser() {
  editUserForm.put(`/admin/users/${editUserForm.id}`, {
    onSuccess: () => {
      showModal('User Updated', 'User updated successfully!', 'success')
      closeEditUserModal()
      fetchUsers()
    },
    onError: () => {
      showModal('User Update Failed', 'Failed to update user. Please check the form and try again.', 'error')
    }
  })
}

onMounted(async () => {
  await fetchUsers()
})

async function fetchUsers() {
  usersLoading.value = true
  try {
    const response = await axios.get('/api/barangay-users?t=' + new Date().getTime())

    if (userRole === 'admin') {
      if (response.data.barangay && response.data.admin) {
        barangayUsers.value = response.data.barangay
        adminUsers.value = response.data.admin
      }
    } else if (userRole === 'barangay') {
                  barangayUsers.value = {
              total: 1,
              active: 1,
              list: [
                {
                  id: page.props.auth.user.id,
                  name: page.props.auth.user.name,
                  email: page.props.auth.user.email,
                  barangay: page.props.auth.user.barangay,
                  avatar: page.props.auth.user.avatar,
                  status: page.props.auth.user.status || 'active'
                }
              ]
            }
      adminUsers.value = { total: 0, active: 0, list: [] }
    }
  } catch (error) {
    console.error('Error fetching users:', error)
  } finally {
    usersLoading.value = false
  }
}

// Location functions
async function fetchRegions() {
  try {
    const response = await fetch('/locations/regions')
    locationRegions.value = await response.json()
  } catch (error) {
    console.error('Error fetching regions:', error)
  }
}

async function fetchProvinces(regionCode: string) {
  if (!regionCode) return
  try {
    const response = await fetch(`/locations/provinces/${regionCode}`)
    locationProvinces.value = await response.json()
    // Reset dependent fields
    addUserForm.province = ''
    addUserForm.city = ''
    addUserForm.barangay = ''
    locationCities.value = []
    locationBarangays.value = []
  } catch (error) {
    console.error('Error fetching provinces:', error)
  }
}

async function fetchCities(regionCode: string, provinceCode: string) {
  if (!regionCode || !provinceCode) return
  try {
    const response = await fetch(`/locations/cities/${regionCode}/${provinceCode}`)
    locationCities.value = await response.json()
    // Reset dependent fields
    addUserForm.city = ''
    addUserForm.barangay = ''
    locationBarangays.value = []
  } catch (error) {
    console.error('Error fetching cities:', error)
  }
}

async function fetchBarangays(regionCode: string, provinceCode: string, cityCode: string) {
  if (!regionCode || !provinceCode || !cityCode) return
  try {
    const response = await fetch(`/locations/barangays/${regionCode}/${provinceCode}/${cityCode}`)
    locationBarangays.value = await response.json()
    // Reset dependent field
    addUserForm.barangay = ''
  } catch (error) {
    console.error('Error fetching barangays:', error)
  }
}

// Edit location functions
async function fetchEditRegions() {
  try {
    const response = await fetch('/locations/regions')
    editLocationRegions.value = await response.json()
  } catch (error) {
    console.error('Error fetching regions:', error)
  }
}

async function fetchEditProvinces(regionCode: string) {
  if (!regionCode) return
  try {
    const response = await fetch(`/locations/provinces/${regionCode}`)
    editLocationProvinces.value = await response.json()
    // Reset dependent fields
    editUserForm.province = ''
    editUserForm.city = ''
    editUserForm.barangay = ''
    editLocationCities.value = []
    editLocationBarangays.value = []
  } catch (error) {
    console.error('Error fetching provinces:', error)
  }
}

async function fetchEditCities(regionCode: string, provinceCode: string) {
  if (!regionCode || !provinceCode) return
  try {
    const response = await fetch(`/locations/cities/${regionCode}/${provinceCode}`)
    editLocationCities.value = await response.json()
    // Reset dependent fields
    editUserForm.city = ''
    editUserForm.barangay = ''
    editLocationBarangays.value = []
  } catch (error) {
    console.error('Error fetching cities:', error)
  }
}

async function fetchEditBarangays(regionCode: string, provinceCode: string, cityCode: string) {
  if (!regionCode || !provinceCode || !cityCode) return
  try {
    const response = await fetch(`/locations/barangays/${regionCode}/${provinceCode}/${cityCode}`)
    editLocationBarangays.value = await response.json()
    // Reset dependent field
    editUserForm.barangay = ''
  } catch (error) {
    console.error('Error fetching barangays:', error)
  }
}

// Location watchers
watch(() => addUserForm.region, (newRegion: string) => {
  if (newRegion) {
    fetchProvinces(newRegion)
  } else {
    locationProvinces.value = []
    locationCities.value = []
    locationBarangays.value = []
  }
})

watch(() => addUserForm.province, (newProvince: string) => {
  if (newProvince && addUserForm.region) {
    fetchCities(addUserForm.region, newProvince)
  } else {
    locationCities.value = []
    locationBarangays.value = []
  }
})

watch(() => addUserForm.city, (newCity: string) => {
  if (newCity && addUserForm.region && addUserForm.province) {
    fetchBarangays(addUserForm.region, addUserForm.province, newCity)
  } else {
    locationBarangays.value = []
  }
})

// Edit location watchers
watch(() => editUserForm.region, (newRegion: string) => {
  if (newRegion) {
    fetchEditProvinces(newRegion)
  } else {
    editLocationProvinces.value = []
    editLocationCities.value = []
    editLocationBarangays.value = []
  }
})

watch(() => editUserForm.province, (newProvince: string) => {
  if (newProvince && editUserForm.region) {
    fetchEditCities(editUserForm.region, newProvince)
  } else {
    editLocationCities.value = []
    editLocationBarangays.value = []
  }
})

watch(() => editUserForm.city, (newCity: string) => {
  if (newCity && editUserForm.region && editUserForm.province) {
    fetchEditBarangays(editUserForm.region, editUserForm.province, newCity)
  } else {
    editLocationBarangays.value = []
  }
})

// Chart Data Preparation

// Combine all users
const allUsers = computed(() => [
  ...adminUsers.value.list,
  ...barangayUsers.value.list
]);

// 1. Active vs. Inactive Users (Pie)
const userStatusPieData = computed(() => {
  const statusCounts: Record<string, number> = { 'Active': 0, 'Inactive': 0 };
  allUsers.value.forEach(user => {
    if ((user.status || '').toLowerCase() === 'active') statusCounts['Active']++;
    else statusCounts['Inactive']++;
  });
  return {
    labels: ['Active', 'Inactive'],
    datasets: [
      {
        data: [statusCounts['Active'], statusCounts['Inactive']],
        backgroundColor: ['#10b981', '#f59e0b'],
      },
    ],
  };
});

// Legend data for user status pie chart
const userStatusLegendData = computed(() => {
  const chart = userStatusPieData.value;
  return chart.labels.map((label, idx) => ({
    label,
    value: chart.datasets[0].data[idx],
    color: chart.datasets[0].backgroundColor[idx]
  }));
});

// 2. User Registration Trend (Line)
const userRegistrationLineData = computed(() => {
  // Get last 12 months
  const now = new Date();
  const months: string[] = [];
  const monthCounts: Record<string, number> = {};
  for (let i = 11; i >= 0; i--) {
    const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const label = d.toLocaleString('default', { month: 'short', year: '2-digit' });
    months.push(label);
    monthCounts[label] = 0;
  }
  allUsers.value.forEach(user => {
    if (user.created_at) {
      const d = new Date(user.created_at);
      const label = d.toLocaleString('default', { month: 'short', year: '2-digit' });
      if (label in monthCounts) monthCounts[label]++;
    }
  });
  return {
    labels: months,
    datasets: [
      {
        label: 'Registrations',
        data: months.map(m => monthCounts[m]),
        borderColor: '#3b82f6',
        backgroundColor: '#3b82f6',
        fill: false,
        tension: 0.3,
      },
    ],
  };
});
</script>
