<script setup lang="ts">
// Explicitly define modelValue and update:modelValue for v-model compatibility
const props = defineProps<{ modelValue: boolean }>()
const emits = defineEmits(['update:modelValue'])

function onUpdate(val: boolean) {
  emits('update:modelValue', val)
}
</script>

<template>
  <DialogRoot
    :open="props.modelValue"
    @update:open="onUpdate"
    data-slot="dialog"
  >
    <slot />
  </DialogRoot>
</template>
