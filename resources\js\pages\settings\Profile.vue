<script setup lang="ts">
import { Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { type SharedData, type User } from '@/types';
import { settingsActiveTab } from '@/settingsTabState';
import { Camera, User as UserIcon, Mail, Shield, CheckCircle, AlertCircle } from 'lucide-vue-next';

interface Props {
    mustVerifyEmail: boolean;
    status?: string;
}

defineProps<Props>();

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

const form = useForm({
    name: user.name,
    middle_name: user.middle_name || '',
    last_name: user.last_name || '',
    suffix: user.suffix || '',
    birthdate: user.birthdate || '',
    gender: user.gender || '',
    mobile_number: user.mobile_number || '',
    region: user.region || '',
    province: user.province || '',
    city: user.city || '',
    barangay: user.barangay || '',
    email: user.email,
});

// Avatar upload state
const photoPreview = ref<string | null>(null);
const photoFile = ref<File | null>(null);
const photoUploading = ref(false);

const handlePhotoChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        photoFile.value = target.files[0];
        photoPreview.value = URL.createObjectURL(target.files[0]);
    }
};

const uploadPhoto = () => {
    if (!photoFile.value) return;
    photoUploading.value = true;
    const uploadForm = useForm({
        photo: photoFile.value,
    });
    uploadForm.post(route('profile.avatar.upload'), {
        forceFormData: true,
        onSuccess: () => {
            photoUploading.value = false;
            photoPreview.value = null;
            photoFile.value = null;
            // Optionally reload user data here
            window.location.reload();
        },
        onError: () => {
            photoUploading.value = false;
        },
    });
};

const submit = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
    });
};

// Set active tab
settingsActiveTab.value = 'profile';
</script>

<template>
  <div class="p-8 max-w-4xl mx-auto space-y-8">
    <!-- Profile Header Card -->
    <Card class="border-0 shadow-lg bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/50 dark:to-teal-950/50 overflow-hidden relative">
      <!-- Decorative Background Elements -->
      <div class="absolute inset-0 bg-gradient-to-r from-emerald-600/5 to-teal-600/5"></div>
      <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400/10 to-teal-400/10 rounded-full -translate-y-10 translate-x-10"></div>
      <div class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-teal-400/10 to-emerald-400/10 rounded-full translate-y-8 -translate-x-8"></div>
      
      <CardContent class="p-6 relative z-10">
        <div class="flex flex-col lg:flex-row items-center lg:items-start gap-6">
          <!-- Avatar Section -->
          <div class="relative group">
            <div class="relative">
              <!-- Avatar with Glow Effect -->
              <div class="absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity"></div>
              
              <img
                v-if="photoPreview"
                :src="photoPreview"
                alt="Profile preview"
                class="relative w-20 h-20 rounded-full object-cover border-4 border-white shadow-xl ring-4 ring-emerald-100 dark:ring-emerald-900"
              />
              <img
                v-else-if="user.profile_photo_url"
                :src="user.profile_photo_url"
                :alt="user.name + ' avatar'"
                class="relative w-20 h-20 rounded-full object-cover border-4 border-white shadow-xl ring-4 ring-emerald-100 dark:ring-emerald-900"
              />
              <div v-else class="relative w-20 h-20 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 text-white flex items-center justify-center text-2xl font-bold border-4 border-white shadow-xl ring-4 ring-emerald-100 dark:ring-emerald-900">
                {{ user.name.charAt(0).toUpperCase() }}
              </div>
              
              <!-- Upload Button with Hover Effect -->
              <label class="absolute -bottom-1 -right-1 bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer border-2 border-emerald-200 hover:border-emerald-400 hover:scale-110 group-hover:bg-emerald-50" title="Upload new photo">
                <input type="file" accept="image/*" class="hidden" @change="handlePhotoChange" />
                <Camera class="h-4 w-4 text-emerald-600" />
              </label>
            </div>
            
            <!-- Upload Actions -->
            <div v-if="photoFile" class="flex gap-2 mt-3 justify-center">
              <Button 
                @click="uploadPhoto" 
                :disabled="photoUploading"
                size="sm"
                class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <span v-if="photoUploading">Uploading...</span>
                <span v-else>Upload</span>
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                @click="() => { photoPreview = null; photoFile = null; }"
                class="border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-all duration-300"
              >
                Cancel
              </Button>
            </div>
          </div>

          <!-- User Info -->
          <div class="flex-1 text-center lg:text-left">
            <div class="space-y-3">
              <!-- Name and Email -->
              <div>
                <h1 class="text-xl font-bold text-gray-900 dark:text-white mb-1">
                  {{ user.name }}
                </h1>
                <p class="text-gray-600 dark:text-gray-300 font-medium flex items-center justify-center lg:justify-start gap-2">
                  <Mail class="h-4 w-4 text-emerald-500" />
                  {{ user.email }}
                </p>
              </div>
              
              <!-- Role Badge and Email Verification Side by Side -->
              <div class="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-3">
                <!-- Role Badge -->
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold shadow-md transition-all duration-300 border"
                      :class="(user as any).role === 'admin' 
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border-blue-200 dark:border-blue-700' 
                        : 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 border-amber-200 dark:border-amber-700'">
                  <div class="w-2 h-2 rounded-full mr-2 animate-pulse"
                       :class="(user as any).role === 'admin' ? 'bg-blue-500' : 'bg-amber-500'"></div>
                  {{ (user as any).role === 'admin' ? 'Administrator' : 'Barangay User' }}
                </span>
                
                <!-- Email Verification Status -->
                <div class="flex items-center gap-2">
                  <div class="w-2 h-2 rounded-full animate-pulse"
                       :class="user.email_verified_at ? 'bg-emerald-500' : 'bg-red-500'"></div>
                  <span class="text-sm font-medium"
                        :class="user.email_verified_at 
                          ? 'text-emerald-700 dark:text-emerald-300' 
                          : 'text-red-700 dark:text-red-300'">
                    Email {{ user.email_verified_at ? 'Verified' : 'Unverified' }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Email Verification Alert (only for unverified) -->
            <div v-if="mustVerifyEmail" class="mt-4 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg shadow-sm">
              <div class="flex items-start gap-2">
                <AlertCircle class="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <div class="flex-1 text-center">
                  <p class="text-xs text-red-700 dark:text-red-300 leading-relaxed mb-3">
                    Please verify your email address to access all features.
                  </p>
                  
                  <!-- Centered Resend Button -->
                  <div class="flex justify-center">
                    <Link
                      :href="route('verification.send')"
                      method="post"
                      as="button"
                      class="inline-flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-lg text-sm font-medium hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 shadow-sm hover:shadow-md"
                    >
                      <Mail class="h-4 w-4" />
                      Resend Verification Email
                    </Link>
                  </div>
                  
                  <div v-if="status === 'verification-link-sent'" class="mt-3 flex items-center justify-center gap-1 text-xs text-emerald-600 dark:text-emerald-400">
                    <CheckCircle class="h-3 w-3" />
                    Verification email sent successfully!
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Profile Information Form -->
    <Card class="border-0 shadow-lg">
      <CardHeader>
        <CardTitle class="flex items-center gap-2 text-xl">
          <UserIcon class="h-5 w-5 text-emerald-600" />
          Profile Information
        </CardTitle>
        <CardDescription>
          Update your personal information and contact details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="submit" class="space-y-6">
          <!-- Personal Information Section -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <UserIcon class="h-5 w-5 text-emerald-600" />
              Personal Information
            </h3>
            
            <div class="grid gap-4 md:grid-cols-2">
            <div class="space-y-2">
                <Label for="name" class="text-sm font-medium">First Name *</Label>
              <Input 
                id="name" 
                v-model="form.name" 
                required 
                  autocomplete="given-name" 
                  placeholder="Enter your first name"
                class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
              <InputError :message="form.errors.name" />
            </div>
            
              <div class="space-y-2">
                <Label for="middle_name" class="text-sm font-medium">Middle Name</Label>
                <Input 
                  id="middle_name" 
                  v-model="form.middle_name" 
                  autocomplete="additional-name" 
                  placeholder="Enter your middle name"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.middle_name" />
              </div>
              
              <div class="space-y-2">
                <Label for="last_name" class="text-sm font-medium">Last Name *</Label>
                <Input 
                  id="last_name" 
                  v-model="form.last_name" 
                  required 
                  autocomplete="family-name" 
                  placeholder="Enter your last name"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.last_name" />
              </div>
              
              <div class="space-y-2">
                <Label for="suffix" class="text-sm font-medium">Suffix</Label>
                <Input 
                  id="suffix" 
                  v-model="form.suffix" 
                  placeholder="e.g., Jr., Sr., III"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.suffix" />
              </div>
              
              <div class="space-y-2">
                <Label for="birthdate" class="text-sm font-medium">Birth Date *</Label>
                <Input 
                  id="birthdate" 
                  type="date"
                  v-model="form.birthdate" 
                  required 
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.birthdate" />
              </div>
              
              <div class="space-y-2">
                <Label for="gender" class="text-sm font-medium">Gender *</Label>
                <select 
                  id="gender" 
                  v-model="form.gender" 
                  required 
                  class="w-full rounded-lg border border-gray-300 bg-white p-3 text-sm shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all"
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                <InputError :message="form.errors.gender" />
              </div>
            </div>
          </div>

          <Separator />

          <!-- Contact Information Section -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Mail class="h-5 w-5 text-emerald-600" />
              Contact Information
            </h3>
            
            <div class="grid gap-4 md:grid-cols-2">
            <div class="space-y-2">
              <Label for="email" class="text-sm font-medium flex items-center gap-2">
                <Mail class="h-4 w-4" />
                  Email Address *
              </Label>
              <Input
                id="email"
                type="email"
                v-model="form.email"
                required
                autocomplete="username"
                placeholder="Enter your email address"
                class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
              <InputError :message="form.errors.email" />
              </div>
              
              <div class="space-y-2">
                <Label for="mobile_number" class="text-sm font-medium">Mobile Number *</Label>
                <Input 
                  id="mobile_number" 
                  v-model="form.mobile_number" 
                  required 
                  autocomplete="tel" 
                  placeholder="Enter your mobile number"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.mobile_number" />
              </div>
            </div>
          </div>

          <Separator />

          <!-- Location Information Section -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <svg class="h-5 w-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Location Information
            </h3>
            
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="region" class="text-sm font-medium">Region *</Label>
                <Input 
                  id="region" 
                  v-model="form.region" 
                  required 
                  placeholder="Enter your region"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.region" />
              </div>
              
              <div class="space-y-2">
                <Label for="province" class="text-sm font-medium">Province *</Label>
                <Input 
                  id="province" 
                  v-model="form.province" 
                  required 
                  placeholder="Enter your province"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.province" />
              </div>
              
              <div class="space-y-2">
                <Label for="city" class="text-sm font-medium">City *</Label>
                <Input 
                  id="city" 
                  v-model="form.city" 
                  required 
                  placeholder="Enter your city"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.city" />
              </div>
              
              <div class="space-y-2">
                <Label for="barangay" class="text-sm font-medium">Barangay *</Label>
                <Input 
                  id="barangay" 
                  v-model="form.barangay" 
                  required 
                  placeholder="Enter your barangay"
                  class="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
                <InputError :message="form.errors.barangay" />
              </div>
            </div>
          </div>

          <Separator />

          <div class="flex items-center gap-4">
            <Button 
              type="submit" 
              :disabled="form.processing" 
              class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2"
            >
              <span v-if="form.processing">Saving...</span>
              <span v-else>Save Changes</span>
            </Button>
            
            <Transition
              enter-active-class="transition ease-in-out duration-300"
              enter-from-class="opacity-0 transform translate-y-1"
              enter-to-class="opacity-100 transform translate-y-0"
              leave-active-class="transition ease-in-out duration-300"
              leave-from-class="opacity-100 transform translate-y-0"
              leave-to-class="opacity-0 transform translate-y-1"
            >
              <div v-show="form.recentlySuccessful" class="flex items-center gap-2 text-sm text-emerald-600 dark:text-emerald-400">
                <CheckCircle class="h-4 w-4" />
                Changes saved successfully!
              </div>
            </Transition>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- Account Information Section -->
    <Card class="border-0 shadow-lg">
      <CardHeader>
        <CardTitle class="flex items-center gap-2 text-xl">
          <Shield class="h-5 w-5 text-emerald-600" />
          Account Information
        </CardTitle>
        <CardDescription>
          Your account details and system information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div class="space-y-2">
            <h4 class="font-medium text-gray-900 dark:text-white">User ID</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ user.id }}</p>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Account Role</h4>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">
              {{ user.role === 'admin' ? 'Administrator' : 'Barangay User' }}
            </span>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Account Status</h4>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium" 
                  :class="user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'">
              {{ user.status === 'active' ? 'Active' : 'Inactive' }}
            </span>
          </div>
          
          
          <div class="space-y-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Member Since</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ new Date(user.created_at).toLocaleDateString() }}</p>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium text-gray-900 dark:text-white">Last Updated</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ new Date(user.updated_at).toLocaleDateString() }}</p>
          </div>
        </div>
      </CardContent>
    </Card>


  </div>
</template>


