<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

class LocationHelper
{
    protected static $locations = null;

    public static function getLocations()
    {
        if (self::$locations === null) {
            $path = database_path('data/locations.json');
            self::$locations = json_decode(File::get($path), true);
        }
        return self::$locations;
    }

    public static function getRegions()
    {
        return collect(self::getLocations())->map(function ($region, $code) {
            return [
                'value' => $code,
                'label' => $region['name']
            ];
        })->values();
    }

    public static function getProvinces($regionCode)
    {
        $region = self::getLocations()[$regionCode] ?? null;
        if (!$region) return collect();

        return collect($region['provinces'])->map(function ($province, $code) {
            return [
                'value' => $code,
                'label' => $province['name']
            ];
        })->values();
    }

    public static function getCities($regionCode, $provinceCode)
    {
        $province = self::getLocations()[$regionCode]['provinces'][$provinceCode] ?? null;
        if (!$province) return collect();

        return collect($province['cities'])->map(function ($city, $code) {
            return [
                'value' => $code,
                'label' => $city['name']
            ];
        })->values();
    }

    public static function getBarangays($regionCode, $provinceCode, $cityCode)
    {
        $city = self::getLocations()[$regionCode]['provinces'][$provinceCode]['cities'][$cityCode] ?? null;
        if (!$city) return collect();

        return collect($city['barangays'])->map(function ($barangay) {
            return [
                'value' => strtolower(str_replace(' ', '-', $barangay)),
                'label' => $barangay
            ];
        })->values();
    }
}
