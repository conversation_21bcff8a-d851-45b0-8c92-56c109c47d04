<?php

namespace App\Http\Controllers;

use App\Models\BudgetPlan;
use App\Models\BudgetAllocation;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class BudgetPlanController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $query = BudgetPlan::query()
            ->select(
                'budget_plans.*',
                'users.name as user_name',
                'users.middle_name',
                'users.last_name',
                'users.suffix',
                'users.email as user_email',
                'users.role as user_role',
                'barangays.name as barangay_name'
            )
            ->leftJoin('users', 'budget_plans.user_id', '=', 'users.id')
            ->leftJoin('barangays', 'budget_plans.barangay_id', '=', 'barangays.id');

        // Filter by status based on user role
        if ($user->role === 'barangay') {
            // For barangay users, show only their own plans from their barangay
            $query->where('budget_plans.barangay_id', $user->barangay_id)
                  ->where('budget_plans.user_id', $user->id)
                  ->whereIn('budget_plans.status', ['Pending', 'Draft', 'Approved', 'Revision', 'Disapproved', 'Pending Revision']);
        } else {
            // For admin users, show all budget plans except drafts (Pending, Approved, Revision, etc.)
            $query->where('budget_plans.status', '!=', 'Draft');
        }

        // Custom sorting: Pending first, then by submission time (oldest first)
        $budgetPlans = $query->orderByRaw("
            CASE
                WHEN budget_plans.status = 'Pending' THEN 1
                WHEN budget_plans.status = 'Revision' THEN 2
                WHEN budget_plans.status = 'Approved' THEN 3
                WHEN budget_plans.status = 'Disapproved' THEN 4
                WHEN budget_plans.status = 'Draft' THEN 5
                ELSE 6
            END
        ")->orderBy('budget_plans.created_at', 'asc')->get();

        // Transform the plans to match the expected format in the frontend
        $transformedPlans = $budgetPlans->map(function($plan) {
            // Construct a formatted full name for the preparer
            $fullName = $plan->user_name ?? '';
            if ($plan->last_name) {
                $fullName = $plan->user_name;
                if ($plan->middle_name) {
                    $fullName .= ' ' . strtoupper(substr($plan->middle_name, 0, 1)) . '.';
                }
                $fullName .= ' ' . strtoupper($plan->last_name);
                if ($plan->suffix) {
                    $fullName .= ', ' . $plan->suffix;
                }
            }
            $fullName = strtoupper(trim($fullName));
            return [
                'id' => $plan->id,
                'focused' => $plan->focused,
                'gender_issue' => $plan->gender_issue,
                'title_desc' => $plan->title_desc,
                'gad_objective' => $plan->gad_objective,
                'gad_activity' => $plan->gad_activity,
                'date_implementation_start' => $plan->date_implementation_start,
                'date_implementation_end' => $plan->date_implementation_end,
                'lead_office' => $plan->lead_office,
                'status' => $plan->status,
                'is_draft' => $plan->is_draft,
                'created_at' => $plan->created_at,
                'updated_at' => $plan->updated_at,
                'user_name' => $plan->user_name,
                'user_full_name' => $fullName,
                'user_email' => $plan->user_email,
                'barangay_name' => $plan->barangay_name,
                'supporting_stats' => $plan->supporting_stats,
                'source_stats' => $plan->source_stats,
                'ppa_si' => $plan->ppa_si,
                'activity' => is_array($plan->activity) ? json_encode($plan->activity) : (is_string($plan->activity) ? $plan->activity : ''),
                'other_activity_category' => $plan->other_activity_category,
                'performance_target' => $plan->performance_target,
                'performance_indicator' => $plan->performance_indicator,
                'mooe' => $plan->mooe,
                'ps' => $plan->ps,
                'co' => $plan->co,
                'total_budget' => $plan->total_budget,
                'total_gad_budget' => $plan->total_gad_budget,
                'lp_allocation' => $plan->lp_allocation,
                'activity_scores' => $plan->activity_scores,
                'lgu_program' => $plan->lgu_program,
                'responsible_office' => $plan->responsible_office,
                'attachment_path' => $plan->attachment_path,
                'remarks' => $plan->remarks,
                'admin_remarks' => $plan->admin_remarks,
                'approver_name' => $plan->approver_name,
                'approval_date' => $plan->approval_date,
                'approved_by_punong_barangay' => $plan->approved_by_punong_barangay,
                'admin_approver' => $plan->admin_approver,
                'fiscal_year' => $plan->fiscal_year ?? date('Y'),
                'user_role' => $plan->user_role,
            ];
        });

        \Log::info('Fetched budget plans:', ['user_id' => $user->id, 'barangay_id' => $user->barangay_id, 'count' => $budgetPlans->count(), 'plans' => $budgetPlans->toArray()]);
        return response()->json(['budgetPlans' => $transformedPlans]);
    }

    public function store(Request $request)
    {
        try {
            $user = Auth::user();

            $validated = $request->validate([
                'focused' => 'required|string',
                'titleDesc' => 'required|string',
                'gadObjective' => 'required|string',
                'gadActivity' => 'required|string',
                'dateImplementationStart' => 'required|date',
                'dateImplementationEnd' => 'required|date',
                'leadOffice' => 'required|string',
            ]);

            $budgetPlan = new BudgetPlan();
            $budgetPlan->user_id = $user->id;
            $budgetPlan->barangay_id = $user->barangay_id;
            $budgetPlan->fiscal_year = $request->fiscalYear ?? date('Y');
            $budgetPlan->focused = $request->focused;
            $budgetPlan->gender_issue = $request->genderIssue;
            $budgetPlan->title_desc = $request->titleDesc;
            $budgetPlan->supporting_stats = $request->supportingStats;
            $budgetPlan->source_stats = $request->sourceStats;
            $budgetPlan->ppa_si = is_array($request->ppaSi) ? json_encode($request->ppaSi) : $request->ppaSi;
            $budgetPlan->activity = is_array($request->activity) ? json_encode($request->activity) : $request->activity;
            $budgetPlan->gad_objective = $request->gadObjective;
            $budgetPlan->lgu_program = $request->lguProgram;
            // Remove this line to avoid overwriting the encoded value
            // $budgetPlan->activity = $request->activity;
            $budgetPlan->other_activity_category = $request->otherActivityCategory;
            $budgetPlan->gad_activity = $request->gadActivity;
            $budgetPlan->date_implementation_start = $request->dateImplementationStart;
            $budgetPlan->date_implementation_end = $request->dateImplementationEnd;
            $budgetPlan->performance_target = $request->performanceTarget;
            $budgetPlan->mooe = $request->mooe;
            $budgetPlan->ps = $request->ps;
            $budgetPlan->co = $request->co;
            $budgetPlan->lead_office = $request->leadOffice;
            $budgetPlan->remarks = $request->remarks;
            $budgetPlan->status = 'Pending';
            $budgetPlan->is_draft = false;

            // Linear programming removed: set LP-related fields to defaults
            $budgetPlan->lp_allocation = 0;
            $budgetPlan->activity_scores = null;

            // Persist Total GAD Budget if provided
            if ($request->has('totalGadBudget')) {
                $budgetPlan->total_gad_budget = (float) $request->input('totalGadBudget', 0);
            }

            // Calculate total GAD budget (MOOE + PS + CO only)
            $manualTotal = ($request->mooe ?? 0) + ($request->ps ?? 0) + ($request->co ?? 0);
            $budgetPlan->total_budget = $manualTotal;

            $budgetPlan->save();

            // Create notification for budget plan status change
            $this->createBudgetNotification($budgetPlan, 'Pending');

            return response()->json([
                'success' => true,
                'message' => 'Budget plan created successfully',
                'budgetPlan' => $budgetPlan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create budget plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeDraft(Request $request)
    {
        try {
            $user = Auth::user();

            $budgetPlan = new BudgetPlan();
            $budgetPlan->user_id = $user->id;
            $budgetPlan->barangay_id = $user->barangay_id;
            $budgetPlan->fiscal_year = $request->fiscalYear ?? date('Y');
            $budgetPlan->focused = $request->focused;
            $budgetPlan->gender_issue = $request->genderIssue;
            $budgetPlan->title_desc = $request->titleDesc;
            $budgetPlan->supporting_stats = $request->supportingStats;
            $budgetPlan->source_stats = $request->sourceStats;
            $budgetPlan->ppa_si = is_array($request->ppaSi) ? json_encode($request->ppaSi) : $request->ppaSi;
            $budgetPlan->activity = is_array($request->activity) ? json_encode($request->activity) : $request->activity;
            $budgetPlan->gad_objective = $request->gadObjective;
            $budgetPlan->lgu_program = $request->lguProgram;
            $budgetPlan->other_activity_category = $request->otherActivityCategory;
            $budgetPlan->gad_activity = $request->gadActivity;
            $budgetPlan->date_implementation_start = $request->dateImplementationStart;
            $budgetPlan->date_implementation_end = $request->dateImplementationEnd;
            $budgetPlan->performance_target = $request->performanceTarget;
            $budgetPlan->mooe = $request->mooe;
            $budgetPlan->ps = $request->ps;
            $budgetPlan->co = $request->co;
            $budgetPlan->lead_office = $request->leadOffice;
            $budgetPlan->remarks = $request->remarks;
            $budgetPlan->status = 'Draft';
            $budgetPlan->is_draft = true;

            // Linear programming removed: set LP-related fields to defaults
            $budgetPlan->lp_allocation = 0;
            $budgetPlan->activity_scores = null;

            // Persist Total GAD Budget if provided
            if ($request->has('totalGadBudget')) {
                $budgetPlan->total_gad_budget = (float) $request->input('totalGadBudget', 0);
            }

            // Calculate total GAD budget (MOOE + PS + CO only)
            $manualTotal = ($request->mooe ?? 0) + ($request->ps ?? 0) + ($request->co ?? 0);
            $budgetPlan->total_budget = $manualTotal;

            $budgetPlan->save();

            // Create notification for budget plan status change
            $this->createBudgetNotification($budgetPlan, 'Draft');

            return response()->json([
                'success' => true,
                'message' => 'Draft saved successfully',
                'budgetPlan' => $budgetPlan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save draft',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(BudgetPlan $budgetPlan)
    {
        $plan = BudgetPlan::query()
            ->select(
                'budget_plans.*',
                'users.name as user_name',
                'users.middle_name',
                'users.last_name',
                'users.suffix',
                'users.email as user_email',
                'users.role as user_role',
                'barangays.name as barangay_name'
            )
            ->leftJoin('users', 'budget_plans.user_id', '=', 'users.id')
            ->leftJoin('barangays', 'budget_plans.barangay_id', '=', 'barangays.id')
            ->where('budget_plans.id', $budgetPlan->id)
            ->first();
        // Construct a formatted full name for the preparer
        $fullName = $plan->user_name ?? '';
        if ($plan->last_name) {
            $fullName = $plan->user_name;
            if ($plan->middle_name) {
                $fullName .= ' ' . strtoupper(substr($plan->middle_name, 0, 1)) . '.';
            }
            $fullName .= ' ' . strtoupper($plan->last_name);
            if ($plan->suffix) {
                $fullName .= ', ' . $plan->suffix;
            }
        }
        $fullName = strtoupper(trim($fullName));
        $transformedPlan = [
            'id' => $plan->id,
            'focused' => $plan->focused,
            'gender_issue' => $plan->gender_issue,
            'title_desc' => $plan->title_desc,
            'gad_objective' => $plan->gad_objective,
            'gad_activity' => $plan->gad_activity,
            'date_implementation_start' => $plan->date_implementation_start,
            'date_implementation_end' => $plan->date_implementation_end,
            'lead_office' => $plan->lead_office,
            'status' => $plan->status,
            'created_at' => $plan->created_at,
            'updated_at' => $plan->updated_at,
            'user_name' => $plan->user_name,
            'user_full_name' => $fullName,
            'user_email' => $plan->user_email,
            'barangay_name' => $plan->barangay_name,
            'supporting_stats' => $plan->supporting_stats,
            'source_stats' => $plan->source_stats,
            'ppa_si' => $plan->ppa_si,
            'activity' => is_array($plan->activity) ? json_encode($plan->activity) : (is_string($plan->activity) ? $plan->activity : ''),
            'other_activity_category' => $plan->other_activity_category,
            'performance_target' => $plan->performance_target,
            'mooe' => $plan->mooe,
            'ps' => $plan->ps,
            'co' => $plan->co,
            'total_budget' => $plan->total_budget,
            'total_gad_budget' => $plan->total_gad_budget,
            'lp_allocation' => $plan->lp_allocation,
            'activity_scores' => $plan->activity_scores,
            'lgu_program' => $plan->lgu_program,
            'user_role' => $plan->user_role,
            // Add other fields as needed
        ];
        return response()->json(['plan' => $transformedPlan]);
    }

    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            $budgetPlan = BudgetPlan::findOrFail($id);

            // --- ADMIN-ONLY APPROVAL LOGIC ---
            // Only trigger admin approval logic for these statuses
            $adminOnlyStatuses = ['Approved', 'Disapproved', 'Revision'];
            if ($request->has('status') && in_array($request->status, $adminOnlyStatuses)) {
                \Log::info('Processing budget plan approval request', [
                    'plan_id' => $budgetPlan->id,
                    'status' => $request->status,
                    'user_role' => $user->role
                ]);

                // Check if user has permission to approve this budget plan
                if ($user->role !== 'admin' && $user->role !== 'punong_barangay') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unauthorized to approve budget plans'
                    ], 403);
                }

                // Update approval fields
                if ($request->has('status')) {
                    $budgetPlan->status = $request->status;
                }

                if ($request->has('approver_name')) {
                    $budgetPlan->admin_approver = $request->approver_name;
                }

                if ($request->has('admin_approver')) {
                    $budgetPlan->admin_approver = $request->admin_approver;
                }

                if ($request->has('approval_date')) {
                    $budgetPlan->approval_date = $request->approval_date;
                }

                if ($request->has('approved_by_punong_barangay')) {
                    $budgetPlan->approved_by_punong_barangay = filter_var($request->approved_by_punong_barangay, FILTER_VALIDATE_BOOLEAN);
                }

                if ($request->has('admin_remarks')) {
                    $budgetPlan->admin_remarks = $request->admin_remarks;
                }

                $budgetPlan->save();

                // Create notification for budget plan status change
                $this->createBudgetNotification($budgetPlan, $budgetPlan->status);

                \Log::info('Budget plan approval processed successfully', [
                    'plan_id' => $budgetPlan->id,
                    'status' => $budgetPlan->status,
                    'approver_name' => $budgetPlan->approver_name
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Budget plan approved successfully',
                    'plan' => $budgetPlan
                ]);
            }

            // --- AUTHORIZATION FOR REGULAR UPDATE (SUBMIT/REVISION) ---
            // Only allow barangay user to update their own plan if status is Draft, Revision, or Pending Revision
            if ($user->role === 'barangay') {
                if ($budgetPlan->user_id !== $user->id || !in_array($budgetPlan->status, ['Draft', 'Revision', 'Pending Revision'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unauthorized: Only the barangay user who owns this plan and if it is in Draft, Revision, or Pending Revision status can update/submit.'
                    ], 403);
                }
            }
            // Optionally, add more role-based checks for other roles here

            // Handle regular budget plan update
            $validated = $request->validate([
                'focused' => 'required|string',
                'title_desc' => 'required|string',
                'gad_objective' => 'required|string',
                'gad_activity' => 'required|string',
                'date_implementation_start' => 'required|date',
                'date_implementation_end' => 'required|date',
                'lead_office' => 'required|string',
            ]);

            $data = $request->all();
            $budgetPlan->focused = $data['focused'] ?? $budgetPlan->focused;
            $budgetPlan->gender_issue = $data['genderIssue'] ?? $budgetPlan->gender_issue;
            $budgetPlan->fiscal_year = $data['fiscalYear'] ?? $budgetPlan->fiscal_year;
            $budgetPlan->title_desc = $data['titleDesc'] ?? $budgetPlan->title_desc;
            $budgetPlan->supporting_stats = $data['supportingStats'] ?? $budgetPlan->supporting_stats;
            $budgetPlan->source_stats = $data['sourceStats'] ?? $budgetPlan->source_stats;
            $budgetPlan->ppa_si = $data['ppaSi'] ?? $budgetPlan->ppa_si;
            $budgetPlan->gad_objective = $data['gadObjective'] ?? $budgetPlan->gad_objective;
            $budgetPlan->lgu_program = $data['lguProgram'] ?? $budgetPlan->lgu_program;
            $budgetPlan->activity = $data['activity'] ?? $budgetPlan->activity;
            $budgetPlan->other_activity_category = $data['otherActivityCategory'] ?? $budgetPlan->other_activity_category;
            $budgetPlan->gad_activity = $data['gadActivity'] ?? $budgetPlan->gad_activity;
            $budgetPlan->date_implementation_start = $data['dateImplementationStart'] ?? $budgetPlan->date_implementation_start;
            $budgetPlan->date_implementation_end = $data['dateImplementationEnd'] ?? $budgetPlan->date_implementation_end;
            $budgetPlan->performance_target = $data['performanceTarget'] ?? $budgetPlan->performance_target;
            $budgetPlan->mooe = $data['mooe'] ?? $budgetPlan->mooe;
            $budgetPlan->ps = $data['ps'] ?? $budgetPlan->ps;
            $budgetPlan->co = $data['co'] ?? $budgetPlan->co;
            $budgetPlan->lead_office = $data['leadOffice'] ?? $budgetPlan->lead_office;

            // Linear programming removed: always reset LP-related fields
            $budgetPlan->lp_allocation = 0;
            $budgetPlan->activity_scores = null;

            // Update Total GAD Budget if provided
            if (array_key_exists('totalGadBudget', $data)) {
                $budgetPlan->total_gad_budget = (float) ($data['totalGadBudget'] ?? 0);
            }

            // Calculate total GAD budget (MOOE + PS + CO only)
            $manualTotal = ($budgetPlan->mooe ?? 0) + ($budgetPlan->ps ?? 0) + ($budgetPlan->co ?? 0);
            $budgetPlan->total_budget = $manualTotal;

            // --- SET STATUS TO 'Pending Revision' IF BARANGAY IS SUBMITTING A REVISION ---
            if ($user->role === 'barangay' && $budgetPlan->status === 'Revision') {
                $budgetPlan->status = 'Pending Revision';
            }

            $budgetPlan->save();

            // Create notification for budget plan status change
            $this->createBudgetNotification($budgetPlan, $budgetPlan->status);

            // Fetch updated plan with joins
            $plan = BudgetPlan::query()
                ->select(
                    'budget_plans.*',
                'users.name as user_name',
                'users.middle_name',
                'users.last_name',
                'users.suffix',
                'users.email as user_email',
                'users.role as user_role',
                'barangays.name as barangay_name'
            )
            ->leftJoin('users', 'budget_plans.user_id', '=', 'users.id')
            ->leftJoin('barangays', 'budget_plans.barangay_id', '=', 'barangays.id')
            ->where('budget_plans.id', $budgetPlan->id)
            ->first();
        $fullName = $plan->user_name ?? '';
        if ($plan->last_name) {
            $fullName = $plan->user_name;
            if ($plan->middle_name) {
                $fullName .= ' ' . strtoupper(substr($plan->middle_name, 0, 1)) . '.';
            }
            $fullName .= ' ' . strtoupper($plan->last_name);
            if ($plan->suffix) {
                $fullName .= ', ' . $plan->suffix;
            }
        }
        $fullName = strtoupper(trim($fullName));
        $transformedPlan = [
            'id' => $plan->id,
            'focused' => $plan->focused,
            'gender_issue' => $plan->gender_issue,
            'title_desc' => $plan->title_desc,
            'gad_objective' => $plan->gad_objective,
            'gad_activity' => $plan->gad_activity,
            'date_implementation_start' => $plan->date_implementation_start,
            'date_implementation_end' => $plan->date_implementation_end,
            'lead_office' => $plan->lead_office,
            'status' => $plan->status,
            'created_at' => $plan->created_at,
            'updated_at' => $plan->updated_at,
            'user_name' => $plan->user_name,
            'user_full_name' => $fullName,
            'user_email' => $plan->user_email,
            'barangay_name' => $plan->barangay_name,
            'supporting_stats' => $plan->supporting_stats,
            'source_stats' => $plan->source_stats,
            'ppa_si' => $plan->ppa_si,
            'activity' => is_array($plan->activity) ? json_encode($plan->activity) : (is_string($plan->activity) ? $plan->activity : ''),
            'other_activity_category' => $plan->other_activity_category,
            'performance_target' => $plan->performance_target,
            'mooe' => $plan->mooe,
            'ps' => $plan->ps,
            'co' => $plan->co,
            'total_budget' => $plan->total_budget,
            'total_gad_budget' => $plan->total_gad_budget,
            'lp_allocation' => $plan->lp_allocation,
            'activity_scores' => $plan->activity_scores,
            'lgu_program' => $plan->lgu_program,
            'user_role' => $plan->user_role,
            // Add other fields as needed
        ];
            return response()->json([
                'message' => 'Budget plan updated successfully',
                'plan' => $transformedPlan
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating budget plan: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error updating budget plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $budgetPlan = BudgetPlan::findOrFail($id);

        // Only allow barangay user to delete their own draft
        if (
            $user->role !== 'barangay' ||
            $budgetPlan->user_id !== $user->id ||
            !($budgetPlan->status === 'Draft' || $budgetPlan->is_draft)
        ) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized: Only the barangay user who owns this draft can delete it.'
            ], 403);
        }

        $budgetPlan->delete();

        return response()->json([
            'success' => true,
            'message' => 'Budget plan draft deleted successfully.'
        ]);
    }

    /**
     * Fast LP optimization calculation (matches client-side logic)
     */
    private function fastLPOptimization(Request $request)
    {
        try {
            // Extract activities from request
            $activities = $request->input('activity', []);
            if (is_string($activities)) {
                $activities = json_decode($activities, true);
            }

            if (empty($activities)) {
                return [
                    'lp_allocation' => 0,
                    'activity_scores' => [],
                    'success' => true
                ];
            }

            // Define activity scores (same as client-side)
            $activityScoreMap = [
                'Capdev/Training - gender sensitivity trainings (GST)' => 5,
                'Capdev/Training - gender analysis' => 5,
                'Capdev/Training - gender responsive planning and budgeting' => 4,
                'Capdev/Training - gad related policies' => 4,
                'development of IEC materials' => 3,
                'PPAs related to the implementation of republic act no. 10354-reproductive health law' => 4,
                'establishment of violence against women and their children (VAWC) Center' => 5,
                'establishment / Maintenance of day care center' => 4,
                'establishment / Maintenance of Women Crisis Center' => 5,
                'establishment / Maintenance of haftway Houses for traficked women and girls' => 5,
                'institutional meachanism to implement the MCW - creation and/or strengthening the LGU GFPS' => 4,
                'provision of targeted assistance/support such as livelihood programs to vurnerable, unemployed and/ or indigent women heads of families or single mothers' => 3,
            ];

            // Calculate scores for selected activities
            $activityScores = [];
            foreach ($activities as $activity) {
                $score = $activityScoreMap[$activity] ?? 3; // Default score of 3
                $activityScores[$activity] = $score;
            }

            // Fast calculation (same as client-side)
            // Use admin_budget from request if provided, otherwise fallback to system budget
            $totalBudget = $request->input('admin_budget') ?? BudgetAllocation::getTotalCityBudget();

            // If admin_budget is provided, also set it in cache for consistency
            if ($request->input('admin_budget')) {
                BudgetAllocation::setAdminBudget($request->input('admin_budget'));
            }

            $maxPercent = 0.01000;
            $estimatedAllocation = count($activities) * ($maxPercent * $totalBudget);

            \Log::info('LP Optimization calculation', [
                'total_budget' => $totalBudget,
                'admin_budget_from_request' => $request->input('admin_budget'),
                'activities_count' => count($activities),
                'max_percent' => $maxPercent,
                'estimated_allocation' => $estimatedAllocation,
                'activities' => $activities
            ]);

            return [
                'lp_allocation' => $estimatedAllocation,
                'activity_scores' => $activityScores,
                'success' => true
            ];

        } catch (\Exception $e) {
            \Log::error('Exception in fast LP optimization', [
                'error' => $e->getMessage()
            ]);

            return [
                'lp_allocation' => 0,
                'activity_scores' => [],
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Run LP optimization for budget allocation (original method - kept for reference)
     */
    private function runLPOptimization(Request $request)
    {
        try {
            // Extract activities from request
            $activities = $request->input('activity', []);
            if (is_string($activities)) {
                $activities = json_decode($activities, true);
            }

            if (empty($activities)) {
                \Log::info('No activities provided for LP optimization');
                return [
                    'lp_allocation' => 0,
                    'activity_scores' => [],
                    'success' => true
                ];
            }

            // Create a cache key based on activities and manual allocations
            $cacheKey = 'lp_optimization_' . md5(json_encode([
                'activities' => $activities,
                'mooe' => $request->input('mooe', 0),
                'ps' => $request->input('ps', 0),
                'co' => $request->input('co', 0)
            ]));

            // Check cache first (valid for 5 minutes)
            $cached = \Cache::get($cacheKey);
            if ($cached) {
                \Log::info('Using cached LP optimization result', ['cache_key' => $cacheKey]);
                return $cached;
            }

            \Log::info('About to run LP optimization', ['activities' => $activities]);

            // Define activity scores
            $activityScoreMap = [
                'Capdev/Training - gender sensitivity trainings (GST)' => 5,
                'Capdev/Training - gender analysis' => 5,
                'Capdev/Training - gender responsive planning and budgeting' => 4,
                'Capdev/Training - gad related policies' => 4,
                'development of IEC materials' => 3,
                'PPAs related to the implementation of republic act no. 10354-reproductive health law' => 4,
                'establishment of violence against women and their children (VAWC) Center' => 5,
                'establishment / Maintenance of day care center' => 4,
                'livelihood programs for women' => 3,
                'scholarship programs for women' => 4,
                'health programs for women' => 4
            ];

            // Calculate scores for selected activities
            $scores = [];
            $activityScores = [];
            foreach ($activities as $activity) {
                $score = $activityScoreMap[$activity] ?? 3; // Default score of 3
                $scores[] = $score;
                $activityScores[$activity] = $score;
            }

            // Prepare manual allocations
            $manualAllocations = [];
            foreach ($activities as $activity) {
                $manualAllocations[$activity] = [
                    'mooe' => $request->input('mooe', 0),
                    'ps' => $request->input('ps', 0),
                    'co' => $request->input('co', 0)
                ];
            }

            // Total budget for LP optimization (this should be the available LP budget)
            // Get total budget from budget allocation system
            $totalBudget = BudgetAllocation::getTotalCityBudget();

            // Prepare data for Python script
            $data = [
                'activities' => $activities,
                'scores' => $scores,
                'total_budget' => $totalBudget,
                'manual_allocations' => $manualAllocations
            ];

            \Log::info('Running LP optimization with data:', $data);

            // Run Python script as subprocess with timeout
            $process = new Process([
                'python',
                base_path('scripts/optimize_budget.py'),
                json_encode($data)
            ]);

            // Set timeout to 10 seconds to prevent hanging
            $process->setTimeout(10);
            $process->run();

            if (!$process->isSuccessful()) {
                \Log::error('LP optimization process failed', [
                    'error' => $process->getErrorOutput(),
                    'output' => $process->getOutput()
                ]);

                // Fallback to zero allocation
                return [
                    'lp_allocation' => 0,
                    'activity_scores' => $activityScores,
                    'success' => false,
                    'error' => 'Optimization process failed'
                ];
            }

            $output = $process->getOutput();
            $result = json_decode($output, true);

            if (!$result || !$result['success']) {
                \Log::error('LP optimization failed', ['result' => $result]);
                return [
                    'lp_allocation' => 0,
                    'activity_scores' => $activityScores,
                    'success' => false,
                    'error' => $result['message'] ?? 'Unknown error'
                ];
            }

            // Calculate total LP allocation from results
            $totalLpAllocation = 0;
            if (isset($result['results'])) {
                foreach ($result['results'] as $activityResult) {
                    $totalLpAllocation += $activityResult['optimal_allocation'] ?? 0;
                }
            }

            \Log::info('LP optimization successful', [
                'total_lp_allocation' => $totalLpAllocation,
                'activity_scores' => $activityScores
            ]);

            $optimizationResult = [
                'lp_allocation' => $totalLpAllocation,
                'activity_scores' => $activityScores,
                'success' => true,
                'optimization_results' => $result['results'] ?? []
            ];

            // Cache the result for 5 minutes
            \Cache::put($cacheKey, $optimizationResult, 300);

            return $optimizationResult;

        } catch (\Exception $e) {
            \Log::error('Exception in LP optimization', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'lp_allocation' => 0,
                'activity_scores' => [],
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create notification for budget plan status change
     */
    private function createBudgetNotification($budgetPlan, $status, $message = null)
    {
        try {
            // Load the relationships to ensure we have the data
            $budgetPlan->load(['user', 'barangay']);
            
            $title = '';
            $notificationMessage = '';
            $audience = null;
            $targetUserId = null;
            $targetBarangayId = null;

            switch ($status) {
                case 'Pending':
                    // Create notification for admin
                    $title = 'New Budget Plan Submitted';
                    $userName = $budgetPlan->user ? $budgetPlan->user->name : 'Unknown User';
                    $barangayName = $budgetPlan->barangay ? $budgetPlan->barangay->name : 'Unknown Barangay';
                    $fiscalYear = $budgetPlan->fiscal_year ?? date('Y');
                    $notificationMessage = $message ?? "{$userName} from Barangay {$barangayName} has submitted a new budget plan for review.";
                    $audience = 'admin';
                    $targetUserId = null; // Admin notification
                    $targetBarangayId = null;
                    
                    // Create the admin notification
                    Notification::create([
                        'user_id' => $targetUserId,
                        'barangay_id' => $targetBarangayId,
                        'type' => 'budget',
                        'audience' => $audience,
                        'title' => $title,
                        'message' => $notificationMessage,
                        'data' => [
                            'budget_plan_id' => $budgetPlan->id,
                            'status' => $status,
                            'submitted_by' => $userName,
                            'barangay_name' => $barangayName,
                            'fiscal_year' => $fiscalYear
                        ],
                        'is_read' => false
                    ]);
                    
                    // Also create a notification for the submitting user
                    $userTitle = "You Submitted a Budget Plan for Year {$fiscalYear}";
                    $userMessage = "{$userName} from Barangay {$barangayName} has submitted a new budget plan for review.";
                    
                    Notification::create([
                        'user_id' => $budgetPlan->user_id,
                        'barangay_id' => $budgetPlan->barangay_id,
                        'type' => 'budget',
                        'audience' => 'barangay',
                        'title' => $userTitle,
                        'message' => $userMessage,
                        'data' => [
                            'budget_plan_id' => $budgetPlan->id,
                            'status' => $status,
                            'submitted_by' => $userName,
                            'barangay_name' => $barangayName,
                            'fiscal_year' => $fiscalYear
                        ],
                        'is_read' => false
                    ]);
                    break;
                    
                case 'Approved':
                    $title = 'Budget Plan Approved';
                    $notificationMessage = $message ?? "Your budget plan '{$budgetPlan->title_desc}' has been approved by the admin.";
                    $audience = 'barangay';
                    $targetUserId = $budgetPlan->user_id;
                    $targetBarangayId = $budgetPlan->barangay_id;
                    // Add admin notification
                    $admin = \Auth::user();
                    if ($admin && $admin->role === 'admin') {
                        Notification::create([
                            'user_id' => $admin->id,
                            'barangay_id' => null,
                            'type' => 'budget',
                            'audience' => 'admin',
                            'title' => 'You approved a budget plan',
                            'message' => "You approved the budget plan '{$budgetPlan->title_desc}'.",
                            'data' => [
                                'budget_plan_id' => $budgetPlan->id,
                                'status' => $status
                            ],
                            'is_read' => false
                        ]);
                    }
                    break;
                    
                case 'Disapproved':
                case 'Revision':
                    $title = 'Budget Plan Requires Revision';
                    $notificationMessage = $message ?? "Your budget plan '{$budgetPlan->title_desc}' requires revision. Please review and resubmit.";
                    $audience = 'barangay';
                    $targetUserId = $budgetPlan->user_id;
                    $targetBarangayId = $budgetPlan->barangay_id;
                    // Add admin notification
                    $admin = \Auth::user();
                    if ($admin && $admin->role === 'admin') {
                        Notification::create([
                            'user_id' => $admin->id,
                            'barangay_id' => null,
                            'type' => 'budget',
                            'audience' => 'admin',
                            'title' => 'You disapproved a budget plan',
                            'message' => "You disapproved the budget plan '{$budgetPlan->title_desc}'.",
                            'data' => [
                                'budget_plan_id' => $budgetPlan->id,
                                'status' => $status
                            ],
                            'is_read' => false
                        ]);
                    }
                    break;
            }

            // Create notification for other statuses (Approved, Disapproved, Revision)
            if ($title && $notificationMessage && $status !== 'Pending') {
                Notification::create([
                    'user_id' => $targetUserId,
                    'barangay_id' => $targetBarangayId,
                    'type' => 'budget',
                    'audience' => $audience,
                    'title' => $title,
                    'message' => $notificationMessage,
                    'data' => [
                        'budget_plan_id' => $budgetPlan->id,
                        'status' => $status
                    ],
                    'is_read' => false
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error creating budget notification: ' . $e->getMessage());
        }
    }
}







