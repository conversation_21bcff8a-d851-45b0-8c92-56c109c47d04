<template>
  <div class="px-6 py-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
      <div class="bg-emerald-600 px-4 py-3 text-white font-semibold flex items-center justify-between">
        <h2 class="text-lg">Select an Endorsed GAD plan that needs to be supplemented</h2>
        <span class="text-xs opacity-90">Barangay view</span>
      </div>

      <div class="p-4">
        <div class="flex flex-col sm:flex-row gap-3 sm:items-center justify-between mb-3">
          <div class="text-sm text-emerald-700">
            Showing approved plans only
          </div>
          <div class="relative">
            <input v-model="searchQuery" type="text" placeholder="Search..."
                   class="pl-8 pr-3 py-1.5 text-sm rounded-md border border-emerald-400 bg-white/90 text-emerald-800 placeholder-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent" />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-500 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
          </div>
        </div>

        <!-- Selection details and inputs -->
        <div v-if="selectedPlan" class="mb-4 p-4 rounded-lg border border-emerald-200 bg-emerald-50/50">
          <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
            <div class="text-sm text-emerald-800">
              Selected Plan: <span class="font-semibold">FY {{ selectedPlan?.fiscal_year }}</span>
            </div>
            <button class="text-xs px-2 py-1 rounded-md bg-white text-emerald-700 border border-emerald-300 hover:bg-emerald-100"
                    @click="clearSelection">Clear selection</button>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-xs font-semibold text-emerald-900 mb-1">Is there any LGU Budget?</label>
              <select v-model="hasLguBudget" class="w-full text-sm rounded-md border border-emerald-300 bg-white px-3 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </select>
            </div>
            <div>
              <label class="block text-xs font-semibold text-emerald-900 mb-1">Additional LGU Budget</label>
              <input
                type="text"
                v-model="additionalLguBudgetInput"
                placeholder="0.00"
                class="w-full text-sm rounded-md border border-emerald-300 bg-white px-3 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div class="overflow-x-auto max-h-[60vh] py-2">
          <table class="min-w-full">
            <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
              <tr>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Selected</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Type of Plan</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">City/Municipality</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Year</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Total LGU Budget</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Total GAD Budget</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Status</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Remarks</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Date Processed</th>
                <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Action</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr
                v-for="plan in filteredPlans"
                :key="plan.id"
                class="hover:bg-gray-50 transition-colors"
                :class="{ 'bg-emerald-50/50 ring-2 ring-emerald-300': isSelected(plan) }"
              >
                <td class="px-3 py-2.5">
                  <span v-if="isSelected(plan)" class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-emerald-100 border border-emerald-200">
                    <Check class="h-4 w-4 text-emerald-700" />
                  </span>
                  <span v-else class="inline-block w-2.5 h-2.5 rounded-full border border-gray-300"></span>
                </td>
                <td class="px-3 py-2.5 text-sm text-gray-800">{{ plan.type || 'New Plan' }}</td>
                <td class="px-3 py-2.5 text-sm text-gray-800">CITY OF PANABO</td>
                <td class="px-3 py-2.5 text-sm text-gray-800">{{ plan.fiscal_year }}</td>
                <td class="px-3 py-2.5 text-sm text-gray-800">Php {{ formatCurrency(plan.total_lgu_budget || 0) }}</td>
                <td class="px-3 py-2.5 text-sm text-gray-800">Php {{ formatCurrency(plan.total_budget || plan.total_gad_budget || 0) }}</td>
                <td class="px-3 py-2.5">
                  <span class="px-2.5 py-1 inline-flex items-center text-xs font-medium rounded-md border bg-emerald-100 text-emerald-800 border-emerald-200">Endorsed by DILG Province</span>
                </td>
                <td class="px-3 py-2.5 text-sm text-gray-600 max-w-[320px] truncate" :title="plan.remarks || ''">{{ plan.remarks || '—' }}</td>
                <td class="px-3 py-2.5 text-sm text-gray-600 whitespace-nowrap">{{ formatDate(plan.date_approved || plan.approval_date || plan.updated_at) }}</td>
                <td class="px-3 py-2.5">
                  <button
                    class="px-3 py-1.5 text-xs font-medium rounded-md transition-colors"
                    :class="isSelected(plan)
                      ? 'bg-emerald-100 text-emerald-800 border border-emerald-200 cursor-default'
                      : 'text-white bg-emerald-600 hover:bg-emerald-700'"
                    :disabled="isSelected(plan)"
                    @click="selectPlan(plan)"
                  >
                    {{ isSelected(plan) ? 'Selected' : 'Select' }}
                  </button>
                </td>
              </tr>
              <tr v-if="filteredPlans.length === 0">
                <td colspan="9" class="px-4 py-8 text-center text-sm text-gray-600">No approved plans found.</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Save bar -->
    <div class="mt-4 flex justify-end">
      <button class="px-4 py-2 text-sm font-medium bg-emerald-600 text-white rounded-md hover:bg-emerald-700" @click="saveSelection" :disabled="!selectedPlan">Save</button>
    </div>
  </div>
  
  <!-- Modal reuse: view selected plan details -->
  <BarangayBudgetPlanModal v-if="showPlanModal" :show="showPlanModal" :plan="selectedPlan" @close="showPlanModal = false" />
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { Check } from 'lucide-vue-next';
import axios from 'axios';
import BarangayBudgetPlanModal from '@/components/BarangayBudgetPlanModal.vue';

interface BudgetPlan {
  id: number;
  fiscal_year: number;
  total_budget?: number;
  total_gad_budget?: number;
  total_lgu_budget?: number;
  status: string;
  approval_date?: string;
  date_approved?: string;
  updated_at?: string;
  remarks?: string;
  type?: string;
}

const plans = ref<BudgetPlan[]>([]);
const searchQuery = ref('');
const selectedPlan = ref<BudgetPlan | null>(null);
const showPlanModal = ref(false);
const hasLguBudget = ref<'Yes' | 'No'>('No');
const additionalLguBudget = ref<number>(0);
const additionalLguBudgetInput = computed({
  get() {
    return additionalLguBudget.value
      ? Number(additionalLguBudget.value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      : '';
  },
  set(val: string) {
    const raw = val.replace(/[^0-9.]/g, '');
    additionalLguBudget.value = raw ? Number(raw) : 0;
  }
});

async function fetchApprovedOnly() {
  try {
    const response = await axios.get('/api/budget-plans');
    const all = response.data?.budgetPlans || [];
    plans.value = all.filter((p: BudgetPlan) => p.status === 'Approved');
  } catch (_err) {
    plans.value = [];
  }
}

const filteredPlans = computed(() => {
  const q = searchQuery.value.trim().toLowerCase();
  if (!q) return plans.value;
  return plans.value.filter(p => String(p.fiscal_year).includes(q));
});

function selectPlan(plan: BudgetPlan) {
  selectedPlan.value = plan;
  showPlanModal.value = false; // show inline panel instead of modal
}

function isSelected(plan: BudgetPlan) {
  return !!(selectedPlan.value && selectedPlan.value.id === plan.id);
}

function saveSelection() {
  // Placeholder for persisting the supplemental selection
  const payload = {
    plan_id: selectedPlan.value?.id,
    has_lgu_budget: hasLguBudget.value,
    additional_lgu_budget: additionalLguBudget.value,
  };
  console.log('Saving supplemental selection', payload);
  alert('Supplemental plan selected.');
}

function clearSelection() {
  selectedPlan.value = null;
  hasLguBudget.value = 'No';
  additionalLguBudget.value = 0;
}

function formatCurrency(v: number) {
  return Math.floor(v).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

function formatDate(dateString?: string) {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
}

onMounted(() => {
  fetchApprovedOnly();
});

// No edit/select event emission here; editing belongs to Revised Plan page.
</script>


