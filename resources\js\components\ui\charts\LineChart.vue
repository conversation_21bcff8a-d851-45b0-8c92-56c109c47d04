<template>
  <LineChart :chart-data="chartData" :options="chartOptions" />
</template>

<script setup>
import { LineChart } from 'vue-chart-3';
import { Chart, LineElement, PointElement, Tooltip, Legend, CategoryScale, LinearScale, LineController } from 'chart.js';

Chart.register(LineController, LineElement, PointElement, CategoryScale, LinearScale, Tooltip, Legend);

const props = defineProps({
  chartData: { type: Object, required: true },
  chartOptions: { type: Object, default: () => ({ responsive: true, maintainAspectRatio: false }) }
});
</script>

<style scoped>
:deep(canvas) {
  width: 100% !important;
  height: 250px !important;
}
</style>
