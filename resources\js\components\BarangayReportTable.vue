<template>
  <Head title="Accomplishment Report" />
  <div class="space-y-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Location Card -->
      <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200 dark:bg-gray-800 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
        <div class="flex flex-col space-y-4">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
              </svg>
            </div>
            <h2 class="text-sm font-medium text-emerald-800 dark:text-emerald-200">Location Information</h2>
          </div>

          <div class="pl-11 space-y-2">
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Barangay:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userBarangay }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">City:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userCity }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Province:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userProvince }}</span>
            </div>
            <div class="flex">
              <span class="text-xs text-emerald-700 dark:text-emerald-300 w-20">Region:</span>
              <span class="text-xs font-medium text-emerald-900 dark:text-emerald-100">{{ userRegion }}</span>
            </div>
          </div>
        </div>
      </div>
 <!-- User Card -->
 <div class="bg-white rounded-lg shadow-md p-6 border border-teal-200 dark:border-teal-800 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="text-sm font-medium text-teal-800 dark:text-teal-200">User Information</h2>
        </div>
        <div class="pl-11 space-y-2">
          <div class="flex">
            <span class="text-xs text-teal-700 dark:text-teal-300 w-20">Name:</span>
            <span class="text-xs font-medium text-teal-900 dark:text-teal-100">{{ getUserFullName() }}</span>
          </div>
          <div class="flex">
            <span class="text-xs text-teal-700 dark:text-teal-300 w-20">Role:</span>
            <span class="text-xs font-medium text-teal-900 dark:text-teal-100">Barangay Gad Focal</span>
          </div>
        </div>
      </div>

      <!-- Budget Card -->
      <div class="bg-white rounded-lg shadow-md p-6 border border-green-200 dark:border-green-800 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-600 to-emerald-700 flex items-center justify-center mr-3 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="text-sm font-medium text-green-800 dark:text-green-200">Budget Information</h2>
        </div>
        <div class="pl-10">

                    <div class="flex items-center">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Annual Barangay Budget:</span>
            <span class="ml-2 text-sm font-bold text-green-600 dark:text-green-400">PHP {{ totalBudget.toLocaleString() }}</span>
          </div>
                    <div class="flex items-center mt-1">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Fiscal Year:</span>
            <span class="ml-2 text-xs font-medium text-gray-900 dark:text-gray-100">{{ new Date().getFullYear() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Reports Table -->
    <Barangayreportmodal :show="showModal" :report="selectedReport" @close="showModal = false" />
    <!-- Add edit modal -->
    <Barangayreporteditmodal
      v-if="showEditModal"
      :show="showEditModal"
      :report="selectedReport"
      @close="showEditModal = false"
      @update="handleReportUpdate"
    />
    <!-- Reports Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 mb-6">
      <!-- Table header -->
      <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 dark:from-emerald-700 dark:to-emerald-800 px-4 py-3">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div>
            <h2 class="text-white text-lg font-semibold">Accomplishment Reports</h2>
          </div>

          <div class="relative">
            <input
              type="text"
              placeholder="Search reports..."
              v-model="searchQuery"
              class="pl-8 pr-3 py-1.5 text-sm rounded-md border border-emerald-400 bg-white/90 text-emerald-800 placeholder-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-500 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-gray-50 dark:bg-gray-800/50 px-4 py-2.5 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap items-center gap-2">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Filter by:</span>

          <!-- Status Filter Dropdown -->
          <div class="relative">
            <button
              @click="toggleStatusDropdown"
              :class="[
                'px-2.5 py-1 text-xs font-medium rounded-md transition-all duration-200 flex items-center gap-1',
                statusFilter
                  ? 'bg-emerald-600 text-white shadow-sm'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
            >
              {{ statusFilter || 'All Status' }}
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Dropdown Menu -->
            <div v-if="showStatusDropdown" class="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 rounded shadow-lg border border-gray-200 dark:border-gray-700 z-50 min-w-[120px]">
              <button
                @click="selectStatus('')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t"
              >
                All Status
              </button>
              <button
                @click="selectStatus('pending')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Pending
              </button>
              <button
                @click="selectStatus('revision')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Revision
              </button>

              <button
                @click="selectStatus('approved')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Approved
              </button>
              <button
                @click="selectStatus('draft')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-b"
              >
                Draft
              </button>
              <button
                @click="selectStatus('pending revision')"
                class="w-full text-left px-2 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Pending Revision
              </button>
            </div>
          </div>

          <select
            v-model="focusedFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Focused</option>
            <option value="CLIENT-FOCUSED">Client-Focused</option>
            <option value="ORGANIZATION-FOCUSED">Organization-Focused</option>
          </select>

          <select
            v-model="issueFilter"
            class="text-xs rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2.5 py-1 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          >
            <option value="">Issues or Mandate</option>
            <option value="Gender Issue">Gender Issue</option>
            <option value="GAD Mandate">GAD Mandate</option>
          </select>
        </div>
      </div>

      <div class="overflow-x-auto overflow-y-auto max-h-[400px] py-2">
        <table class="w-full min-w-[900px]">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
            <tr>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Gender Issue</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">GAD Mandate</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Focused</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Status</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Date submitted</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Date Approved</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Total gad Budget</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Expenditure</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Fiscal Year</th>
              <th class="px-3 py-2.5 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b border-gray-200 dark:border-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="(report, idx) in filteredReports" :key="report.id"
                :class="idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'"
                class="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">
                <span v-if="report.genderIssue" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  Gender Issue
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">
                <span v-if="report.gadMandate" class="px-2 py-0.5 text-gray-700 dark:text-gray-300 text-xs">
                  GAD Mandate
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400">{{ report.focused || '' }}</td>
              <td class="px-3 py-2.5">
                <span :class="getStatusBadgeClass(report.status)"
                  class="px-3 py-1.5 text-xs font-semibold rounded-md border min-w-[110px] min-h-[32px] flex items-center justify-center whitespace-nowrap">
                  {{ report.status ? report.status.charAt(0).toUpperCase() + report.status.slice(1) : 'Pending' }}
                </span>
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">{{ report.dateSubmitted ? formatDate(report.dateSubmitted) : '' }}</td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">
                {{ report.dateApproved ? formatDate(report.dateApproved) : (report.status === 'approved' ? formatDate(report.updated_at) : '') }}
              </td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">{{ report.approvedBudget || 0 }}</td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">{{ report.actualCost || 0 }}</td>
              <td class="px-3 py-2.5 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">{{ report.fiscalYear || new Date().getFullYear() }}</td>
              <td class="px-3 py-2.5">
                <!-- Clean action buttons -->
                <div class="flex flex-nowrap gap-1.5 whitespace-nowrap">
                  <!-- View button (always visible) -->
                  <button
                    class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors relative"
                    @click="openViewModal(report)"
                  >
                    <!-- Red dot for unread -->
                    <span v-if="!isReportRead(report.id)" class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white"></span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View
                  </button>

                  <!-- More options button (for draft and revision status) -->
                  <div v-if="['draft', 'revision'].includes((report.status || '').toLowerCase())" class="relative">
                    <button
                      class="dropdown-container inline-flex items-center justify-center gap-1 px-2.5 py-1.5 text-xs font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-md transition-colors min-w-[70px] min-h-[32px]"
                      @click="toggleMoreOptions(report.id, $event)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                      </svg>
                    </button>
                    <!-- Dropdown menu rendered via Teleport -->
                    <Teleport to="body" v-if="activeMoreOptions === report.id && dropdownPosition">
                      <div
                        class="dropdown-container absolute bg-white dark:bg-gray-800 rounded-md shadow-lg z-[9999] border border-gray-200 dark:border-gray-600 w-36"
                        :style="{ position: 'absolute', top: dropdownPosition.top + 'px', left: dropdownPosition.left + 'px' }"
                      >
                        <div class="py-1">
                          <button
                            class="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                            @click="editReport(report)"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                          </button>
                          <button
                            class="w-full text-left px-3 py-2 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                            @click="submitReport(report)"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Submit
                          </button>
                        </div>
                      </div>
                    </Teleport>
                  </div>
                </div>
              </td>
            </tr>
            <tr v-if="filteredReports.length === 0">
              <td colspan="10" class="px-3 py-8 text-center">
                <div class="flex flex-col items-center">
                  <div class="rounded-full bg-emerald-50 dark:bg-emerald-900/30 p-5 mb-4 border border-emerald-200 dark:border-emerald-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-emerald-700 dark:text-emerald-300">No accomplishment reports found</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 max-w-md">
                    Reports will appear here once created
                  </p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <!-- Modal for delete confirmation and notifications (copied from BudgetPlan.vue) -->
    <div v-if="modalVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3">
            <!-- Error Icon -->
            <svg v-if="modalType === 'error'" class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <!-- Success Icon -->
            <svg v-else-if="modalType === 'success'" class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Confirm Icon -->
            <svg v-else-if="modalType === 'confirm'" class="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <!-- Info Icon -->
            <svg v-else class="w-6 h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-emerald-800">{{ modalTitle }}</h3>
        </div>
        <p class="text-emerald-700 mb-6">{{ modalMessage }}</p>
        <div class="flex justify-end gap-3">
          <button
            v-if="modalType === 'confirm'"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            @click="modalType === 'confirm' ? confirmAction() : closeModal()"
            class="px-4 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
          >
            {{ modalType === 'confirm' ? 'Confirm' : 'OK' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { usePage, Head } from '@inertiajs/vue3';
import axios from 'axios';
import Barangayreportmodal from './Barangayreportmodal.vue';
import Barangayreporteditmodal from './Barangayreporteditmodal.vue';

// Define emits properly
const emit = defineEmits(['view', 'download']);

interface User {
  barangay: string;
  region: string;
  province: string;
  city: string;
  name: string;
  // add other user properties if needed
}

const props = defineProps<{
  reports: any[],
  loading?: boolean
}>();

const page = usePage<{
  props: {
    auth: {
      user: User;
    };
  };
}>();
// Add this computed property to access user data
const userBarangay = computed(() => (page.props as any).auth.user.barangay);
const userRegion = computed(() => (page.props as any).auth.user.region);
const userProvince = computed(() => (page.props as any).auth.user.province);
const userCity = computed(() => (page.props as any).auth.user.city);
const userName = computed(() => (page.props as any).auth.user.name);

const showModal = ref(false);
const showEditModal = ref(false);
const selectedReport = ref<any>(null);
const searchQuery = ref('');

// Filter variables
const statusFilter = ref('');
const focusedFilter = ref('');
const issueFilter = ref('');

// Dropdown state
const showStatusDropdown = ref(false);

// More options dropdown state (simplified for inline positioning)
const activeMoreOptions = ref<number | null>(null);
const dropdownPosition = ref<{ top: number; left: number } | null>(null);

// Modal state for delete confirmation and notifications (same as parent)
const modalVisible = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const modalType = ref<'info' | 'error' | 'success' | 'confirm'>('info');
let modalAction: (() => void) | null = null;

function showModalAlert(title: string, message: string, type: 'info' | 'error' | 'success' | 'confirm', action?: () => void) {
  modalTitle.value = title;
  modalMessage.value = message;
  modalType.value = type;
  modalVisible.value = true;
  modalAction = action || null;
}
function closeModal() {
  modalVisible.value = false;
  modalAction = null;
}
function confirmAction() {
  if (modalAction) {
    modalVisible.value = false;
    modalAction();
    modalAction = null;
  }
}

// --- Read/Unread logic for reports ---
const readReportIds = ref<number[]>([]);

function loadReadReportIds() {
  const stored = localStorage.getItem('barangayReportReadIds');
  readReportIds.value = stored ? JSON.parse(stored) : [];
}

function saveReadReportIds() {
  localStorage.setItem('barangayReportReadIds', JSON.stringify(readReportIds.value));
}

function isReportRead(reportId: number | undefined) {
  if (!reportId) return false;
  return readReportIds.value.includes(reportId);
}

function markReportAsRead(reportId: number | undefined) {
  if (!reportId) return;
  if (!readReportIds.value.includes(reportId)) {
    readReportIds.value.push(reportId);
    saveReadReportIds();
  }
}
// Expose isReportRead for debugging
// @ts-ignore
window.isReportRead = isReportRead;
// --- End read/unread logic ---

// Fetch total GAD budget from localStorage (set by admin)
const totalBudget = ref(0);
onMounted(() => {
  const storedBudget = localStorage.getItem('gad_total_budget');
  totalBudget.value = storedBudget ? Number(storedBudget) : 12000000;

  // Add click listener to close dropdown when clicking outside
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    if (activeMoreOptions.value !== null && !target.closest('.dropdown-container')) {
      activeMoreOptions.value = null;
      dropdownPosition.value = null;
    }
  });
  loadReadReportIds();
});

// Watch for changes in reports prop to ensure instant updates
watch(() => props.reports, (newReports) => {
  // This ensures the table updates immediately when new data is received
  // Reports are now cached in localStorage for instant loading
}, { immediate: true });

// Sort newest reports on top
const sortedReports = computed(() => {
  return [...(props.reports || [])].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
});

const filteredReports = computed(() => {
  // Ensure we always have an array to work with for instant display
  const reportsToUse = sortedReports.value;

  // Return early if no reports to avoid unnecessary processing
  if (reportsToUse.length === 0) {
    return [];
  }

  return reportsToUse.filter(report => {
    // Search filter - optimized for performance
    const searchMatch = !searchQuery.value || (() => {
      const query = searchQuery.value.toLowerCase();
      const searchableFields = [
        // Basic info fields
        report.title,
        report.description,
        report.focused,
        report.status,
        report.barangay_name,
        report.barangay,
        // Additional searchable terms
        report.fiscalYear,
        report.approvedBudget,
        report.actualCost
      ];

      return searchableFields.some(field =>
        field && field.toString().toLowerCase().includes(query)
      ) || (report.genderIssue && 'gender issue'.includes(query)) ||
          (report.gadMandate && 'gad mandate'.includes(query));
    })();

    // Status filter
    const statusMatch = !statusFilter.value ||
      (report.status && report.status.toLowerCase() === statusFilter.value.toLowerCase());

    // Focused filter
    const focusedMatch = !focusedFilter.value ||
      (report.focused && report.focused.toUpperCase() === focusedFilter.value);

    // Issue filter
    const issueMatch = !issueFilter.value ||
      (issueFilter.value === 'Gender Issue' && report.genderIssue) ||
      (issueFilter.value === 'GAD Mandate' && report.gadMandate);

    return searchMatch && statusMatch && focusedMatch && issueMatch;
  });
});

// Dropdown functions
function toggleStatusDropdown() {
  showStatusDropdown.value = !showStatusDropdown.value;
}

function selectStatus(status: string) {
  statusFilter.value = status;
  showStatusDropdown.value = false;
}

// Clear all filters
function clearFilters() {
  searchQuery.value = '';
  focusedFilter.value = '';
  issueFilter.value = '';
  statusFilter.value = '';
}

function openViewModal(report: any) {
  // Instead of emitting, directly show the modal
  selectedReport.value = report;
  showModal.value = true;
  markReportAsRead(report.id); // Mark as read when viewing
}

function openEditModal(report: any) {
  selectedReport.value = JSON.parse(JSON.stringify(report)); // Create a deep copy
  showEditModal.value = true;
}

// More options dropdown functions (simplified for inline positioning)
function toggleMoreOptions(reportId: number, event?: MouseEvent) {
  if (event) {
    event.stopPropagation();
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();
    // 144px = dropdown width (w-36)
    dropdownPosition.value = {
      top: rect.bottom + window.scrollY,
      left: rect.right - 144 + window.scrollX,
    };
  }
  if (activeMoreOptions.value === reportId) {
    activeMoreOptions.value = null;
    dropdownPosition.value = null;
  } else {
    activeMoreOptions.value = reportId;
  }
}

function editReport(report: any) {
  activeMoreOptions.value = null;
  openEditModal(report);
}

function submitReport(report: any) {
  // Updated required fields for accomplishment report (check both camelCase and snake_case)
  const requiredFields = [
    { field: 'genderIssue', altField: 'gender_issue', label: 'Gender Issues or GAD Mandate' },
    { field: 'gadObjective', altField: 'gad_objective', label: 'GAD Objective' },
    { field: 'lguProgram', altField: 'lgu_program', label: 'Relevant LGU Program or Project' },
    { field: 'gadActivity', altField: 'gad_activity', label: 'GAD Activity' },
    { field: 'performanceIndicatorTarget', altField: 'performance_indicator_target', label: 'Performance Indicator and Target' },
    { field: 'actualResults', altField: 'actual_results', label: 'Actual Results' },
    { field: 'approvedGadBudget', altField: 'approved_gad_budget', label: 'Approved GAD Budget' },
    { field: 'actualCostExpenditure', altField: 'actual_cost_expenditure', label: 'Actual Cost or Expenditure' },
    { field: 'varianceRemarks', altField: 'variance_remarks', label: 'Variance or Remarks' },
    { field: 'supportingDocuments', altField: 'supporting_documents', label: 'Supporting Documents' },
  ];
  const missingFields = requiredFields.filter(f => {
    return !report[f.field] && !report[f.altField];
  });
  if (missingFields.length > 0) {
    const missingFieldNames = missingFields.map(f => f.label).join(', ');
    showModalAlert('Missing Required Fields', `Please fill in all required fields before submitting: ${missingFieldNames}`, 'info');
    return;
  }
  showModalAlert(
    'Submit Accomplishment Report',
    `Are you sure you want to submit this accomplishment report?`,
    'confirm',
    () => {
      // Update the report status to 'pending' or 'pending revision'
      let newStatus = 'pending';
      if (report.status && report.status.trim().toLowerCase() === 'revision') {
        newStatus = 'pending revision';
      }
      const updateData = {
        status: newStatus,
        dateSubmitted: new Date().toISOString().slice(0, 10)
      };
      axios.put(`/api/accomplishment-reports/${report.id}`, updateData)
        .then(response => {
          if (response.data.success) {
            // Update the local report object
            const index = props.reports.findIndex(r => r.id === report.id);
            if (index !== -1) {
              props.reports[index] = { ...props.reports[index], ...updateData };
            }
            activeMoreOptions.value = null;
            showModalAlert('Submitted', 'Report submitted successfully!', 'success');
          } else {
            throw new Error(response.data.message || 'Unknown error');
          }
        })
        .catch(error => {
          const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
          showModalAlert('Failed', 'Failed to submit report: ' + errorMessage, 'error');
        });
    }
  );
}

function deleteReport(report: any) {
  showModalAlert(
    'Delete Accomplishment Report',
    `Are you sure you want to delete the accomplishment report "${report.title || report.title_desc || 'Untitled Report'}"?`,
    'confirm',
    () => {
      axios.delete(`/api/accomplishment-reports/${report.id}`)
        .then(response => {
          if (response.data.success) {
            const index = props.reports.findIndex(r => r.id === report.id);
            if (index !== -1) {
              props.reports.splice(index, 1);
            }
            activeMoreOptions.value = null;
            showModalAlert('Deleted', 'Report draft deleted successfully.', 'success');
          } else {
            throw new Error(response.data.message || 'Unknown error');
          }
        })
        .catch(error => {
          if (error.response?.status === 404) {
            const index = props.reports.findIndex(r => r.id === report.id);
            if (index !== -1) {
              props.reports.splice(index, 1);
            }
            activeMoreOptions.value = null;
            showModalAlert('Not Found', 'The report was already deleted or does not exist. The list has been updated.', 'info');
          } else {
            const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || 'Unknown error occurred';
            showModalAlert('Failed', 'Failed to delete report: ' + errorMessage, 'error');
          }
        });
    }
  );
}

function formatDate(dateString: string): string {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function formatLongDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

// Consistent status badge functions for both tables
function getStatusBadgeClass(status: string): string {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';
  const normalizedStatus = (typeof status === 'string' ? status.trim().toLowerCase() : '');
  switch (normalizedStatus) {
    case 'approved':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case 'pending revision':
      return 'bg-amber-100 text-blue-800 border-blue-200';
    case 'revision':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'rejected':
    case 'disapproved':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

function getStatusLabel(status: string): string {
  if (!status) return 'UNKNOWN';

  switch (status.toLowerCase()) {
    case 'approved':
      return 'APPROVED';
    case 'pending':
      return 'PENDING';
    case 'revision':
      return 'REVISION';
    case 'rejected':
      return 'REJECTED';
    case 'disapproved':
      return 'DISAPPROVED';
    case 'draft':
      return 'DRAFT';
    default:
      return status.toUpperCase();
  }
}

function statusLabel(status: string) {
  switch (status.toLowerCase()) {
    case 'approved':
      return 'APPROVED';
    case 'pending':
      return 'PENDING';
    case 'rejected':
      return 'REJECTED';
    case 'draft':
      return 'DRAFT';
    case 'revision':
      return 'REVISION';
    default:
      return status.toUpperCase();
  }
}

function handleReportUpdate(updatedReport: any) {
  // Find and update the report in the local array
  const index = props.reports.findIndex(r => r.id === updatedReport.id);
  if (index !== -1) {
    props.reports[index] = updatedReport;
  }
  showEditModal.value = false;
}

// Add this function to get the user's full name
function getUserFullName() {
  const user = page.props.auth.user;

  if (!user) return 'Unknown User';

  let fullName = user.name || '';

  if (user.middle_name) {
    // Add middle initial
    fullName += ' ' + user.middle_name.charAt(0) + '.';
  }

  if (user.last_name) {
    fullName += ' ' + user.last_name;
  }

  if (user.suffix) {
    fullName += ', ' + user.suffix;
  }

  return fullName;
}


</script>





































