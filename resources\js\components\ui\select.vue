<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
    modelValue?: string | number;
    class?: string;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string | number): void;
}>();

const value = computed({
    get: () => props.modelValue || '',
    set: (value) => emit('update:modelValue', value),
});
</script>

<template>
    <select
        v-model="value"
        :class="[
            'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm',
            props.class,
        ]"
    >
        <slot />
    </select>
</template> 