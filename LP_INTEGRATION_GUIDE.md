# Linear Programming Budget Allocation Integration

## Overview
This integration adds Linear Programming (LP) optimization to the Vue.js + Laravel GAD budget planning system. The LP algorithm automatically allocates optimal budgets for selected activities based on priority scores, which are then combined with manual MOOE, PS, and CO inputs.

## Features Implemented

### 🎯 Core Functionality
- **Automatic LP Optimization**: Triggered on "Save as Draft" and "Submit" actions
- **Activity-Based Scoring**: Priority-based scoring system for GAD activities
- **Combined Budget Calculation**: `Total Budget = LP Allocation + (MOOE + PS + CO)`
- **Real-time Preview**: Shows estimated LP allocation as activities are selected
- **Fallback Handling**: Graceful degradation if optimization fails

### 📊 Activity Scoring System
```
High Priority (Score 5):
- Gender sensitivity trainings (GST)
- Gender analysis
- VAWC Centers
- Women Crisis Centers
- Halfway Houses

Medium Priority (Score 4):
- Gender responsive planning and budgeting
- GAD related policies
- Day care centers
- MCW implementation mechanisms

Lower Priority (Score 3):
- IEC materials development
- Livelihood programs for women
```

## Database Changes

### New Fields Added to `budget_plans` table:
```sql
ALTER TABLE budget_plans ADD COLUMN lp_allocation DECIMAL(15,2) DEFAULT 0;
ALTER TABLE budget_plans ADD COLUMN activity_scores JSON;
```

## Backend Integration (Laravel)

### Files Modified:
1. **`app/Http/Controllers/BudgetPlanController.php`**
   - Added `runLPOptimization()` method
   - Modified `store()` and `storeDraft()` methods
   - Added activity scoring logic

2. **`app/Models/BudgetPlan.php`**
   - Added `lp_allocation` and `activity_scores` to fillable
   - Added proper casting for new fields

3. **`scripts/optimize_budget.py`**
   - Enhanced to handle multiple activities
   - Improved error handling and logging

### API Response Format:
```json
{
  "success": true,
  "budgetPlan": {
    "id": 1,
    "lp_allocation": 150000.50,
    "activity_scores": {"activity1": 5, "activity2": 4},
    "total_budget": 250000.50,
    "mooe": 50000,
    "ps": 30000,
    "co": 20000
  },
  "lpOptimization": {
    "lp_allocation": 150000.50,
    "activity_scores": {...},
    "combined_total": 250000.50
  }
}
```

## Frontend Integration (Vue.js)

### Files Modified:
1. **`resources/js/pages/BudgetPlan.vue`**
   - Added LP allocation display
   - Real-time combined budget calculation
   - Activity selection watcher

2. **`resources/js/components/BarangayBudgetPlanTable.vue`**
   - Enhanced budget display with LP breakdown
   - Shows "LP: ₱X + Manual: ₱Y" format

### UI Enhancements:
- **LP Allocation Card**: Shows estimated allocation based on selected activities
- **Combined Budget Display**: Real-time total calculation
- **Budget Breakdown**: Detailed view in table showing LP vs manual allocations

## How It Works

### 1. User Interaction Flow:
```
1. User selects activities from dropdown
   ↓
2. Frontend estimates LP allocation (real-time)
   ↓
3. User inputs MOOE, PS, CO values
   ↓
4. Combined total updates automatically
   ↓
5. User clicks "Save as Draft" or "Submit"
   ↓
6. Backend runs LP optimization
   ↓
7. Results saved and displayed
```

### 2. LP Optimization Process:
```python
# Constraints
min_allocation = 0.01% of total_budget
max_allocation = 0.1094% of total_budget

# Objective: Maximize Σ(score_i × allocation_i)
# Subject to: Σ(allocation_i) ≤ total_budget
```

### 3. Error Handling:
- **Python script fails**: Falls back to manual totals only
- **No activities selected**: LP allocation = 0
- **Invalid scores**: Uses default score of 3

## Testing the Integration

### 1. Manual Testing:
1. Navigate to Budget Plan form
2. Select 2-3 activities from dropdown
3. Observe LP allocation estimate appears
4. Input MOOE, PS, CO values
5. Save as draft or submit
6. Check database for `lp_allocation` and `activity_scores`
7. Verify combined total in budget table

### 2. API Testing:
```bash
# Test LP optimization endpoint
curl -X POST /api/budget-plans/draft \
  -H "Content-Type: application/json" \
  -d '{
    "activity": ["Capdev/Training - gender sensitivity trainings (GST)"],
    "mooe": 50000,
    "ps": 30000,
    "co": 20000
  }'
```

## Configuration

### Python Dependencies:
```bash
pip install scipy numpy
```

### Environment Variables:
- No additional environment variables required
- Uses existing Laravel configuration

## Troubleshooting

### Common Issues:
1. **Python not found**: Ensure Python is in system PATH
2. **SciPy import error**: Install dependencies with `pip install scipy numpy`
3. **LP allocation shows 0**: Check if activities are selected and have valid scores
4. **Optimization fails**: Check Laravel logs for Python script errors

### Debug Mode:
- Check `storage/logs/laravel.log` for LP optimization errors
- Check `optimize_budget.log` for Python script debugging
- Use browser dev tools to inspect API responses

## Future Enhancements

### Potential Improvements:
1. **Dynamic Scoring**: Allow admins to modify activity scores
2. **Budget Constraints**: Add per-category budget limits
3. **Multi-objective Optimization**: Consider multiple criteria
4. **Historical Analysis**: Track optimization performance over time
5. **Batch Processing**: Optimize multiple plans simultaneously

## Support

For issues or questions about the LP integration:
1. Check Laravel logs for backend errors
2. Check browser console for frontend errors
3. Verify Python dependencies are installed
4. Test with simple activity selections first
