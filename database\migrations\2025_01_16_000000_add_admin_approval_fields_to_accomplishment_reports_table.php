<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Add admin_approver column if it doesn't exist
            if (!Schema::hasColumn('accomplishment_reports', 'admin_approver')) {
                $table->string('admin_approver')->nullable()->after('status');
            }
            
            // Add date_approved column if it doesn't exist
            if (!Schema::hasColumn('accomplishment_reports', 'date_approved')) {
                $table->timestamp('date_approved')->nullable()->after('admin_approver');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accomplishment_reports', function (Blueprint $table) {
            // Drop the columns if they exist
            if (Schema::hasColumn('accomplishment_reports', 'date_approved')) {
                $table->dropColumn('date_approved');
            }
            
            if (Schema::hasColumn('accomplishment_reports', 'admin_approver')) {
                $table->dropColumn('admin_approver');
            }
        });
    }
};
