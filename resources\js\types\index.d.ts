import type { PageProps } from '@inertiajs/core';
import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
    spaTab?: string; // Added for SPA sidebar support
}

export interface SharedData extends PageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
}

export interface User {
    id: number;
    name: string;
    middle_name?: string;
    last_name?: string;
    suffix?: string;
    birthdate?: string;
    gender?: string;
    mobile_number?: string;
    region?: string;
    province?: string;
    city?: string;
    barangay?: string;
    barangay_id?: number;
    email: string;
    role?: string;
    status?: string;
    avatar?: string;
    profile_photo_url?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;
