<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class UserController extends Controller
{
    public function getBarangayUsers(Request $request)
    {
        try {
            $user = $request->user();

            if ($user->role === 'admin') {
                // Admin can see all users
                $adminUsers = User::where('role', 'admin')->get();
                $barangayUsers = User::where('role', 'barangay')->get();

                return response()->json([
                    'admin' => [
                        'total' => $adminUsers->count(),
                        'active' => $adminUsers->where('status', 'active')->count(),
                        'list' => $adminUsers
                    ],
                    'barangay' => [
                        'total' => $barangayUsers->count(),
                        'active' => $barangayUsers->where('status', 'active')->count(),
                        'list' => $barangayUsers
                    ]
                ]);
            } else {
                // Barangay users can only see themselves if they are active
                if ($user->status === 'active') {
                    return response()->json([
                        'barangay' => [
                            'total' => 1,
                            'active' => 1,
                            'list' => [$user]
                        ],
                        'admin' => [
                            'total' => 0,
                            'active' => 0,
                            'list' => []
                        ]
                    ]);
                } else {
                    return response()->json([
                        'barangay' => [
                            'total' => 0,
                            'active' => 0,
                            'list' => []
                        ],
                        'admin' => [
                            'total' => 0,
                            'active' => 0,
                            'list' => []
                        ]
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error in getBarangayUsers: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function createUser(Request $request)
    {
        try {
            // Check if user is admin
            $currentUser = $request->user();
            if ($currentUser->role !== 'admin') {
                return back()->withErrors(['error' => 'Unauthorized']);
            }

            // Validate the request
            $request->validate([
                'name' => 'required|string|max:255',
                'middle_name' => 'nullable|string|max:255',
                'last_name' => 'required|string|max:255',
                'suffix' => 'nullable|string|max:10',
                'birthdate' => 'required|date',
                'gender' => 'required|string|in:male,female,other',
                'mobile_number' => 'required|string|max:20',
                'region' => 'required|string|max:255',
                'province' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'barangay' => 'required|string|max:255',
                'email' => 'required|string|lowercase|email|max:255|unique:users',
                'password' => 'required|confirmed|min:8',
                'role' => 'required|string|in:admin,barangay,punong_barangay',
            ]);

            // Find or create barangay record
            $barangayId = \DB::table('barangays')
                ->where('name', $request->barangay)
                ->where('region', $request->region)
                ->where('province', $request->province)
                ->where('city', $request->city)
                ->value('id');

            if (!$barangayId) {
                $barangayId = \DB::table('barangays')->insertGetId([
                    'name' => $request->barangay,
                    'region' => $request->region,
                    'province' => $request->province,
                    'city' => $request->city,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Create the user
            $user = User::create([
                'name' => $request->name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'suffix' => $request->suffix,
                'birthdate' => $request->birthdate,
                'gender' => $request->gender,
                'mobile_number' => $request->mobile_number,
                'region' => $request->region,
                'province' => $request->province,
                'city' => $request->city,
                'barangay' => $request->barangay,
                'barangay_id' => $barangayId,
                'email' => $request->email,
                'password' => \Hash::make($request->password),
                'role' => $request->role,
                'status' => 'active', // Default status for new users
                'email_verified_at' => now(), // Auto-verify admin created users
            ]);

            // Return redirect response for Inertia (this prevents the white JSON modal)
            return redirect()->back()->with('success', 'User created successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error creating user: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create user'])->withInput();
        }
    }

    public function updateUser(Request $request, $id)
    {
        try {
            // Check if user is admin
            $currentUser = $request->user();
            if ($currentUser->role !== 'admin') {
                return back()->withErrors(['error' => 'Unauthorized']);
            }

            // Find the user to update
            $user = User::findOrFail($id);

            // Validate the request
            $validationRules = [
                'name' => 'required|string|max:255',
                'middle_name' => 'nullable|string|max:255',
                'last_name' => 'required|string|max:255',
                'suffix' => 'nullable|string|max:10',
                'birthdate' => 'required|date',
                'gender' => 'required|string|in:male,female,other',
                'mobile_number' => 'required|string|max:20',
                'region' => 'required|string|max:255',
                'province' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'barangay' => 'required|string|max:255',
                'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $id,
                'role' => 'required|string|in:admin,barangay,punong_barangay',
            ];

            // Only validate password if it's provided
            if ($request->filled('password')) {
                $validationRules['password'] = 'required|min:8';
            }

            $request->validate($validationRules);

            // Find or create barangay record
            $barangayId = \DB::table('barangays')
                ->where('name', $request->barangay)
                ->where('region', $request->region)
                ->where('province', $request->province)
                ->where('city', $request->city)
                ->value('id');

            if (!$barangayId) {
                $barangayId = \DB::table('barangays')->insertGetId([
                    'name' => $request->barangay,
                    'region' => $request->region,
                    'province' => $request->province,
                    'city' => $request->city,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Prepare update data
            $updateData = [
                'name' => $request->name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'suffix' => $request->suffix,
                'birthdate' => $request->birthdate,
                'gender' => $request->gender,
                'mobile_number' => $request->mobile_number,
                'region' => $request->region,
                'province' => $request->province,
                'city' => $request->city,
                'barangay' => $request->barangay,
                'barangay_id' => $barangayId,
                'email' => $request->email,
                'role' => $request->role,
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = \Hash::make($request->password);
            }

            // Update the user
            $user->update($updateData);

            // Return redirect response for Inertia (this prevents the white JSON modal)
            return redirect()->back()->with('success', 'User updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return back()->withErrors(['error' => 'User not found'])->withInput();
        } catch (\Exception $e) {
            \Log::error('Error updating user: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update user'])->withInput();
        }
    }

    public function deactivateUser(Request $request, $id)
    {
        try {
            // Check if user is admin
            $currentUser = $request->user();
            if ($currentUser->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Find the user to deactivate
            $user = User::findOrFail($id);

            // Prevent deactivating self
            if ($user->id === $currentUser->id) {
                return response()->json(['error' => 'You cannot deactivate your own account'], 400);
            }

            // Update user status to inactive
            $user->update(['status' => 'inactive']);

            return response()->json(['message' => 'User deactivated successfully']);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'User not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Error deactivating user: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to deactivate user'], 500);
        }
    }

    public function reactivateUser(Request $request, $id)
    {
        try {
            // Check if user is admin
            $currentUser = $request->user();
            if ($currentUser->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Find the user to reactivate
            $user = User::findOrFail($id);

            // Update user status to active
            $user->update(['status' => 'active']);

            return response()->json(['message' => 'User reactivated successfully']);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'User not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Error reactivating user: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to reactivate user'], 500);
        }
    }
}
