<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Mail;

try {
    echo "Testing email sending...\n";
    
    Mail::raw('This is a test email from <PERSON><PERSON> application.', function($message) {
        $message->to('<EMAIL>')
                ->subject('Test Email - ' . now()->format('Y-m-d H:i:s'))
                ->from('<EMAIL>', 'Laravel Test');
    });
    
    echo "Email sent successfully!\n";
    echo "Check your Gmail inbox and spam folder.\n";
    
} catch (Exception $e) {
    echo "Error sending email: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
} 