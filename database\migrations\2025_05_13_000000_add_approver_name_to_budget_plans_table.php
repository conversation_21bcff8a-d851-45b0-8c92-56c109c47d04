<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // Add approver_name column if it doesn't exist
            if (!Schema::hasColumn('budget_plans', 'approver_name')) {
                $table->string('approver_name')->nullable()->after('status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('budget_plans', function (Blueprint $table) {
            // Drop the column if it exists
            if (Schema::hasColumn('budget_plans', 'approver_name')) {
                $table->dropColumn('approver_name');
            }
        });
    }
};