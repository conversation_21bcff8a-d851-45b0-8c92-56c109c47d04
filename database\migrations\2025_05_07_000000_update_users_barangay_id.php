<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update specific users with their barangay_id
        DB::table('users')
            ->where('email', '<EMAIL>')
            ->update(['barangay_id' => 1]);
            
        // You can add more users as needed
        DB::table('users')
            ->whereNull('barangay_id')
            ->update(['barangay_id' => 1]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // If needed, you can revert these changes
        DB::table('users')
            ->whereIn('email', ['<EMAIL>'])
            ->update(['barangay_id' => null]);
    }
};