<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (Auth::check()) {
            $user = Auth::user();
            
            // If user is inactive, logout and redirect to login with message
            if ($user->status === 'inactive') {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                // If they're trying to access the login page, just show the error
                if ($request->routeIs('login')) {
                    return $next($request);
                }
                
                return redirect()->route('login')->withErrors([
                    'email' => 'Your account has been deactivated. Please contact an administrator for assistance.',
                ]);
            }
        }

        return $next($request);
    }
}
