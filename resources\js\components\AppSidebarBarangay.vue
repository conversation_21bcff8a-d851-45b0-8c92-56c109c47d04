<template>
    <!-- Mobile Overlay -->
    <div v-if="isMobile && isOpen" class="fixed inset-0 bg-black/50 z-40 lg:hidden" @click="closeMobileSidebar"></div>

    <!-- Sidebar -->
    <div
        :class="[
            'bg-gradient-to-br from-emerald-500 via-emerald-600 to-emerald-800 min-h-screen flex flex-col shadow-2xl border-r border-emerald-400/20 transition-all duration-300 ease-in-out',
            // Mobile styles
            isMobile ? 'fixed top-0 left-0 z-50 transform' : '',
            isMobile && isOpen ? 'translate-x-0' : '',
            isMobile && !isOpen ? '-translate-x-full' : '',
            // Desktop styles
            !isMobile && isCollapsed ? 'w-20' : 'w-64',
        ]"
    >
        <!-- Unified Single Sidebar Content -->
        <div class="flex flex-col h-full text-white bg-gradient-to-br from-emerald-500 via-emerald-600 to-emerald-800">
            <!-- Compact Header -->
            <div class="p-4 border-b border-white/20 relative">
                <!-- Centered Logo and Title -->
                <div class="flex flex-col items-center text-center mb-3">
                    <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg flex-shrink-0 p-2 mb-2">
                        <img src="/images/panabo-city-logo-1.png" alt="Panabo City Logo" class="w-full h-full object-contain rounded-full" />
                    </div>
                    <div v-if="!isCollapsed || isMobile" class="transition-opacity duration-300">
                        <p class="text-sm font-bold text-white drop-shadow-sm leading-tight">GAD Planning and Budgetting Management System</p>
                    </div>
                </div>

                <!-- Compact User Profile -->
                <Link
                    :href="route('profile.edit')"
                    :class="[
                        'flex items-center p-2 bg-gradient-to-r from-emerald-800/10 to-emerald-800/5 rounded-lg backdrop-blur-sm border border-emerald-800/20 hover:from-emerald-800/20 hover:to-emerald-800/10 transition-all duration-200 cursor-pointer group',
                        isCollapsed && !isMobile ? 'justify-center' : 'gap-2'
                    ]"
                >
                    <img
                        :src="user?.avatar || 'https://randomuser.me/api/portraits/lego/2.jpg'"
                        alt="User Avatar"
                        class="w-8 h-8 rounded-full object-cover border border-white/30 group-hover:border-white/50 transition-colors duration-200 flex-shrink-0"
                    />
                    <div v-if="!isCollapsed || isMobile" class="flex-1 min-w-0 transition-opacity duration-300">
                        <p class="text-xs font-medium text-white truncate group-hover:text-white/90 transition-colors duration-200">{{ user?.email || '<EMAIL>' }}</p>
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-white/20 text-white group-hover:bg-white/30 transition-colors duration-200">
                            Barangay
                        </span>
                    </div>
                </Link>
            </div>

            <!-- Main Navigation - Responsive -->
            <div class="flex-1 p-4">
                <SidebarMenu class="space-y-1">
                    <SidebarMenuItem v-for="item in mainNavItems" :key="item.title">
                        <SidebarMenuButton
                            @click="item.spaTab && onSidebarTab(item.spaTab as any); closeMobileSidebar()"
                            :class="[
                                'w-full text-white hover:bg-white/10 transition-all duration-200 rounded-lg cursor-pointer group',
                                isCollapsed && !isMobile ? 'justify-center' : ''
                            ]"
                        >
                            <div
                                :class="[
                                    'flex items-center p-2',
                                    isCollapsed && !isMobile ? 'justify-center' : 'gap-3'
                                ]"
                                :title="isCollapsed && !isMobile ? item.title : ''"
                            >
                                <div :class="[
                                    'flex items-center justify-center rounded bg-emerald-800/20 group-hover:bg-emerald-800/30 transition-colors duration-200',
                                    isCollapsed && !isMobile ? 'w-8 h-8' : 'w-6 h-6'
                                ]">
                                    <component v-if="item.icon" :is="item.icon" :class="[
                                        'text-white',
                                        isCollapsed && !isMobile ? 'h-4 w-4' : 'h-3 w-3'
                                    ]" />
                                </div>
                                <span v-if="!isCollapsed || isMobile" class="text-sm font-medium text-white transition-opacity duration-300">{{ item.title }}</span>
                            </div>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>

                <!-- Settings and Logout for Barangay Users -->
                <div class="mt-4 pt-4 border-t border-white/20">
                    <SidebarMenu class="space-y-1">
                        <SidebarMenuItem>
                            <SidebarMenuButton
                                :as-child="true"
                                :class="[
                                    'w-full text-white hover:bg-white/10 transition-all duration-200 rounded-lg group cursor-pointer',
                                    isCollapsed && !isMobile ? 'justify-center' : ''
                                ]"
                            >
                                <button
                                    @click="onSidebarTab('settings'); closeMobileSidebar()"
                                    :class="[
                                        'flex items-center p-2 cursor-pointer group w-full text-left',
                                        isCollapsed && !isMobile ? 'justify-center' : 'gap-3'
                                    ]"
                                    :title="isCollapsed && !isMobile ? 'Settings' : ''"
                                >
                                    <div :class="[
                                        'flex items-center justify-center rounded bg-emerald-800/20 group-hover:bg-emerald-800/30 transition-colors duration-200',
                                        isCollapsed && !isMobile ? 'w-8 h-8' : 'w-6 h-6'
                                    ]">
                                        <Settings :class="[
                                            'text-white',
                                            isCollapsed && !isMobile ? 'h-4 w-4' : 'h-3 w-3'
                                        ]" />
                                    </div>
                                    <span v-if="!isCollapsed || isMobile" class="text-sm font-medium text-white transition-opacity duration-300">Settings</span>
                                </button>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                        <SidebarMenuItem>
                            <SidebarMenuButton
                                :as-child="true"
                                :class="[
                                    'w-full text-white hover:bg-white/10 transition-all duration-200 rounded-lg cursor-pointer group',
                                    isCollapsed && !isMobile ? 'justify-center' : ''
                                ]"
                            >
                                <button
                                    @click="handleLogout"
                                    :class="[
                                        'flex items-center p-2 w-full text-left',
                                        isCollapsed && !isMobile ? 'justify-center' : 'gap-3'
                                    ]"
                                    :title="isCollapsed && !isMobile ? 'Logout' : ''"
                                >
                                    <div :class="[
                                        'flex items-center justify-center rounded bg-emerald-800/20 group-hover:bg-emerald-800/30 transition-colors duration-200',
                                        isCollapsed && !isMobile ? 'w-8 h-8' : 'w-6 h-6'
                                    ]">
                                        <LogOut :class="[
                                            'text-white',
                                            isCollapsed && !isMobile ? 'h-4 w-4' : 'h-3 w-3'
                                        ]" />
                                    </div>
                                    <span v-if="!isCollapsed || isMobile" class="text-sm font-medium text-white transition-opacity duration-300">Logout</span>
                                </button>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </div>
            </div>

            <!-- Responsive Footer -->
            <div class="p-4 border-t border-white/20 mt-auto">
                <!-- System Info - Responsive -->
                <div v-if="!isCollapsed || isMobile" class="transition-opacity duration-300">
                    <p class="text-xs text-white/80 text-center font-medium">GAD System v1.0</p>
                </div>
            </div>
        </div>
    </div>
    <slot />
</template>
<script setup lang="ts">
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { BookOpen, Folder, Settings, LogOut, Home, FilePlus2, FileEdit } from 'lucide-vue-next';
import { ref, onMounted, onUnmounted } from 'vue';

import { usePage, Link, router } from '@inertiajs/vue3';
import { route } from 'ziggy-js';

// Responsive sidebar state
const isCollapsed = ref(false);
const isMobile = ref(false);
const isOpen = ref(false);

// Check screen size
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 1024; // lg breakpoint
  if (isMobile.value) {
    isCollapsed.value = false; // On mobile, use overlay instead of collapsed
  }
};

// Toggle functions
const toggleSidebar = () => {
  if (isMobile.value) {
    isOpen.value = !isOpen.value;
  } else {
    isCollapsed.value = !isCollapsed.value;
  }
};

const closeMobileSidebar = () => {
  if (isMobile.value) {
    isOpen.value = false;
  }
};

onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});

const page = usePage<{ auth: { user: { role: string, name: string, email: string, avatar?: string, barangay?: string } } }>();
const user = page.props.auth.user;

// Navigation items for barangay users only
const mainNavItems = [
  {
    title: 'Dashboard',
    icon: Home,
    spaTab: 'dashboard',
  },
  {
    title: 'Budget Plan',
    icon: Folder,
    spaTab: 'budget',
  },
  {
    title: 'Supplemental',
    icon: FilePlus2,
    spaTab: 'supplemental',
  },
  {
    title: 'Revised Plan',
    icon: FileEdit,
    spaTab: 'revised',
  },
  {
    title: 'Accomplishment Report',
    icon: BookOpen,
    spaTab: 'accomplishment',
  },
] as (NavItem & { spaTab?: string })[];

const emit = defineEmits(['spa-tab']);
import { barangayActiveTab } from '../dashboardTabState';
import { settingsActiveTab } from '../settingsTabState';

function onSidebarTab(tab: 'dashboard' | 'budget' | 'accomplishment' | 'supplemental' | 'revised' | 'settings') {
  if (tab === 'settings') {
    settingsActiveTab.value = 'general';
  } else {
    barangayActiveTab.value = tab;
  }
  emit('spa-tab', tab);
}

function handleLogout() {
  router.post(route('logout'), {}, {
    onFinish: () => {
      window.location.href = route('login');
    }
  });
}

// Expose toggle function for external use
defineExpose({
  toggleSidebar
});
</script>
