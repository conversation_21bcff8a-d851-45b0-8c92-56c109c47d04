<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BudgetAllocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'barangay_id',
        'barangay_name',
        'allocated_budget',
        'fiscal_year',
        'deadline',
        'is_active',
        'total_city_budget',
        'metadata',
        'created_by',
    ];

    protected $casts = [
        'allocated_budget' => 'decimal:2',
        'total_city_budget' => 'decimal:2',
        'deadline' => 'date',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the barangay that this allocation belongs to
     */
    public function barangay()
    {
        return $this->belongsTo(Barangay::class);
    }

    /**
     * Get the user who created this allocation
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get total city budget for a specific fiscal year
     */
    public static function getTotalCityBudget($fiscalYear = null)
    {
        $fiscalYear = $fiscalYear ?? date('Y');

        // First try to get admin-set budget from cache/session
        $adminBudget = self::getAdminSetBudget();
        if ($adminBudget > 0) {
            return $adminBudget;
        }

        // Then try database allocation
        $allocation = self::where('fiscal_year', $fiscalYear)
                         ->where('is_active', true)
                         ->first();

        return $allocation ? $allocation->total_city_budget : 91407936.05; // Fallback to current hardcoded value
    }

    /**
     * Set admin budget in cache for LP optimization
     */
    public static function setAdminBudget($budget)
    {
        cache(['admin_gad_budget' => $budget], now()->addDays(30));
        return $budget;
    }

    /**
     * Get admin-set budget from cache
     */
    public static function getAdminSetBudget()
    {
        return cache('admin_gad_budget', 0);
    }

    /**
     * Get allocated budget for a specific barangay and fiscal year
     */
    public static function getBarangayBudget($barangayId, $fiscalYear = null)
    {
        $fiscalYear = $fiscalYear ?? date('Y');

        $allocation = self::where('barangay_id', $barangayId)
                         ->where('fiscal_year', $fiscalYear)
                         ->where('is_active', true)
                         ->first();

        return $allocation ? $allocation->allocated_budget : 0;
    }

    /**
     * Get all active allocations for a fiscal year
     */
    public static function getActiveAllocations($fiscalYear = null)
    {
        $fiscalYear = $fiscalYear ?? date('Y');

        return self::where('fiscal_year', $fiscalYear)
                  ->where('is_active', true)
                  ->get();
    }
}
