<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold text-gray-900">System Notice Manager</h2>
      <button
        @click="showCreateModal = true"
        class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors duration-200"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Create Notice
      </button>
    </div>

    <!-- Notice Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405C18.37 15.37 18 14.698 18 14V11a6 6 0 1 0-12 0v3c0 .698-.37 1.37-.595 1.595L4 17h5m6 0v1a3 3 0 1 1-6 0v-1m6 0H9" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-blue-600">Total Notices</p>
            <p class="text-2xl font-bold text-blue-900">{{ systemNotices.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 p-4 rounded-lg border border-green-200">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-green-600">Read</p>
            <p class="text-2xl font-bold text-green-900">{{ readCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-orange-600">Unread</p>
            <p class="text-2xl font-bold text-orange-900">{{ unreadCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-red-50 p-4 rounded-lg border border-red-200">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-red-600">Urgent</p>
            <p class="text-2xl font-bold text-red-900">{{ urgentCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="flex flex-wrap gap-4 mb-6">
      <select v-model="selectedType" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
        <option value="">All Types</option>
        <option value="announcement">Announcement</option>
        <option value="maintenance">Maintenance</option>
        <option value="update">System Update</option>
        <option value="reminder">Reminder</option>
        <option value="urgent">Urgent</option>
      </select>

      <select v-model="selectedAudience" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
        <option value="">All Audiences</option>
        <option value="all">All Users</option>
        <option value="admin">Admin Only</option>
        <option value="barangay">Barangay Only</option>
      </select>

      <select v-model="selectedPriority" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
        <option value="">All Priorities</option>
        <option value="urgent">Urgent</option>
        <option value="high">High</option>
        <option value="normal">Normal</option>
        <option value="low">Low</option>
      </select>

      <button
        @click="clearFilters"
        class="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
      >
        Clear Filters
      </button>
    </div>

    <!-- Notices List -->
    <div class="space-y-4">
      <div
        v-for="notice in filteredNotices"
        :key="notice.id"
        class="border rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
        :class="getPriorityBorderClass(notice.data?.priority)"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-3 flex-1">
            <div class="text-2xl">{{ getNotificationIcon(notice.type) }}</div>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <h3 class="font-semibold text-gray-900">{{ notice.title }}</h3>
                <span
                  v-if="notice.data?.priority"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getPriorityBadgeClass(notice.data.priority)"
                >
                  {{ notice.data.priority }}
                </span>
                <span
                  v-if="notice.data?.notice_type"
                  class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full"
                >
                  {{ notice.data.notice_type }}
                </span>
              </div>
              <p class="text-gray-600 mb-2">{{ notice.message }}</p>
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <span>Audience: {{ getAudienceLabel(notice.audience) }}</span>
                <span>Created: {{ formatTime(notice.created_at) }}</span>
                <span v-if="notice.data?.created_by">By: {{ notice.data.created_by }}</span>
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="deleteNotice(notice.id)"
              class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Delete Notice"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div v-if="filteredNotices.length === 0" class="text-center py-8">
        <div class="text-gray-400 mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405C18.37 15.37 18 14.698 18 14V11a6 6 0 1 0-12 0v3c0 .698-.37 1.37-.595 1.595L4 17h5m6 0v1a3 3 0 1 1-6 0v-1m6 0H9" />
          </svg>
        </div>
        <p class="text-gray-500">No system notices found</p>
      </div>
    </div>

    <!-- Create Notice Modal -->
    <div
      v-if="showCreateModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showCreateModal = false"
    >
      <div
        @click.stop
        class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Create System Notice</h3>
          <button
            @click="showCreateModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="createNotice" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
            <input
              v-model="newNotice.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="Enter notice title"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
            <textarea
              v-model="newNotice.message"
              required
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="Enter notice message"
            ></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <select
                v-model="newNotice.type"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              >
                <option value="announcement">Announcement</option>
                <option value="maintenance">Maintenance</option>
                <option value="update">System Update</option>
                <option value="reminder">Reminder</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
              <select
                v-model="newNotice.priority"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              >
                <option value="low">Low</option>
                <option value="normal">Normal</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Audience</label>
            <select
              v-model="newNotice.audience"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option value="all">All Users</option>
              <option value="admin">Admin Only</option>
              <option value="barangay">Barangay Only</option>
            </select>
          </div>

          <div class="flex justify-end gap-3 pt-4">
            <button
              type="button"
              @click="showCreateModal = false"
              class="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="creating"
              class="bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
            >
              <svg v-if="creating" class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ creating ? 'Creating...' : 'Create Notice' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

interface SystemNotice {
  id: number;
  title: string;
  message: string;
  type: string;
  audience: string;
  data?: any;
  created_at: string;
  is_read: boolean;
}

const systemNotices = ref<SystemNotice[]>([]);
const loading = ref(false);
const creating = ref(false);
const showCreateModal = ref(false);

// Filters
const selectedType = ref('');
const selectedAudience = ref('');
const selectedPriority = ref('');

// New notice form
const newNotice = ref({
  title: '',
  message: '',
  type: 'announcement',
  priority: 'normal',
  audience: 'all'
});

// Computed properties
const filteredNotices = computed(() => {
  let filtered = systemNotices.value;

  if (selectedType.value) {
    filtered = filtered.filter(notice => notice.data?.notice_type === selectedType.value);
  }

  if (selectedAudience.value) {
    filtered = filtered.filter(notice => notice.audience === selectedAudience.value);
  }

  if (selectedPriority.value) {
    filtered = filtered.filter(notice => notice.data?.priority === selectedPriority.value);
  }

  return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
});

const readCount = computed(() => systemNotices.value.filter(n => n.is_read).length);
const unreadCount = computed(() => systemNotices.value.filter(n => !n.is_read).length);
const urgentCount = computed(() => systemNotices.value.filter(n => n.data?.priority === 'urgent').length);

// Methods
const fetchSystemNotices = async () => {
  try {
    loading.value = true;
    const response = await axios.get('/api/system-notices?all_audiences=1');
    if (response.data.success) {
      systemNotices.value = response.data.notices;
    }
  } catch (error) {
    console.error('Error fetching system notices:', error);
  } finally {
    loading.value = false;
  }
};

const createNotice = async () => {
  try {
    creating.value = true;
    const response = await axios.post('/api/system-notices', newNotice.value);
    
    if (response.data.success) {
      showCreateModal.value = false;
      resetForm();
      await fetchSystemNotices();
    }
  } catch (error) {
    console.error('Error creating notice:', error);
  } finally {
    creating.value = false;
  }
};

const deleteNotice = async (id: number) => {
  if (!confirm('Are you sure you want to delete this notice?')) return;
  
  try {
    const response = await axios.delete(`/api/system-notices/${id}`);
    if (response.data.success) {
      await fetchSystemNotices();
    }
  } catch (error) {
    console.error('Error deleting notice:', error);
  }
};

const clearFilters = () => {
  selectedType.value = '';
  selectedAudience.value = '';
  selectedPriority.value = '';
};

const resetForm = () => {
  newNotice.value = {
    title: '',
    message: '',
    type: 'announcement',
    priority: 'normal',
    audience: 'all'
  };
};

const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'system': return '📢';
    case 'announcement': return '📢';
    case 'maintenance': return '🔧';
    case 'update': return '🔄';
    case 'reminder': return '⏰';
    case 'urgent': return '🚨';
    default: return '🔔';
  }
};

const getPriorityBorderClass = (priority: string): string => {
  switch (priority) {
    case 'urgent': return 'border-red-300 bg-red-50';
    case 'high': return 'border-orange-300 bg-orange-50';
    case 'normal': return 'border-blue-300 bg-blue-50';
    case 'low': return 'border-green-300 bg-green-50';
    default: return 'border-gray-300 bg-white';
  }
};

const getPriorityBadgeClass = (priority: string): string => {
  switch (priority) {
    case 'urgent': return 'bg-red-100 text-red-800';
    case 'high': return 'bg-orange-100 text-orange-800';
    case 'normal': return 'bg-blue-100 text-blue-800';
    case 'low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getAudienceLabel = (audience: string): string => {
  switch (audience) {
    case 'all': return 'All Users';
    case 'admin': return 'Admin Only';
    case 'barangay': return 'Barangay Only';
    default: return audience || 'All';
  }
};

const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

onMounted(() => {
  fetchSystemNotices();
});
</script> 